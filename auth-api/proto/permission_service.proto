syntax = "proto3";

package auth;

option go_package = "./permission";

service PermissionService {
  // Validate if a user has a specific permission
  rpc ValidateUserPermission (ValidateUserPermissionRequest) returns (ValidatePermissionResponse);
  
  // Validate if a user has access to a specific company
  rpc ValidateCompanyAccess (ValidateCompanyAccessRequest) returns (ValidateAccessResponse);
  
  // Validate if a user has access to a specific enterprise
  rpc ValidateEnterpriseAccess (ValidateEnterpriseAccessRequest) returns (ValidateAccessResponse);

  rpc ListUserAccessableCompaniesWithEnterprises (UserIdInput) returns (ListCompanyOrEnterpriseResponse);
  
  rpc ListUserAccessableEnterprises (UserIdInput) returns (ListCompanyOrEnterpriseResponse);
}

// Permission validation request
message ValidateUserPermissionRequest {
  string user_id = 1;
  string feature_key = 2;
  int32 operation = 3;  // 1=Read, 2=Write, 3=Both
}

// Common response for permission validation
message ValidatePermissionResponse {
  bool has_permission = 1;
  string error_message = 2;
}

// Company access validation request
message ValidateCompanyAccessRequest {
  string user_id = 1;
  string company_id = 2;
}

// Enterprise access validation request
message ValidateEnterpriseAccessRequest {
  string user_id = 1;
  string enterprise_id = 2;
}

// Common response for access validation
message ValidateAccessResponse {
  bool has_access = 1;
  string error_message = 2;
}

// to list all accessable companies or enterprises
message UserIdInput {
  string user_id = 1;
}

// Common response for access validation
message ListCompanyOrEnterpriseResponse {
  repeated string ids = 1;
  bool is_all = 2;
}
