package main

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/cucumber/godog"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"

	definitions "auth-api/features/step-definitions"
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/db"
)

func TestMain(m *testing.M) {
	ctx := context.Background()
	log := logger.NewLogger()

	mongodbContainer, e := mongodb.Run(ctx, "mongo:6")

	defer func() {
		if e := testcontainers.TerminateContainer(mongodbContainer); e != nil {
			log.Errorf("failed to terminate container: %s", e)
		}
	}()

	if e != nil {
		log.Errorf("failed to start container: %s", e)
		return
	}

	mongoUri := fmt.Sprintf("%s/epikFax", _err.Must(mongodbContainer.ConnectionString(ctx)))
	log.Info(mongoUri)
	db.Connect(mongoUri)

	status := godog.TestSuite{
		ScenarioInitializer: definitions.InitializeScenario,
	}.Run()

	if status != 0 {
		os.Exit(status)
	}
}
