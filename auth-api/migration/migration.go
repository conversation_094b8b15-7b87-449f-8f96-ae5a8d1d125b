package migration

import (
	"auth-api/internal/common/logger"
	"auth-api/internal/db"
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

// SourceConfig represents the configuration for the source MongoDB
type SourceConfig struct {
	URI          string
	DatabaseName string
}

// MigrationService handles the migration of data from the old MongoDB to the new one
type MigrationService struct {
	sourceClient *mongo.Client
	sourceDB     *mongo.Database
	targetDB     *mongo.Database
	ctx          context.Context
	log          *logger.Logger
}

// NewMigrationService creates a new instance of MigrationService
func NewMigrationService(sourceConfig SourceConfig) (*MigrationService, error) {
	ctx := context.Background()
	log := logger.NewLogger()
	bsonOpts := &options.BSONOptions{
		UseJSONStructTags: true,
		NilSliceAsEmpty:   true,
		OmitEmpty:         true,
	}
	// Connect to the source MongoDB
	sourceClient, err := mongo.Connect(options.Client().ApplyURI(sourceConfig.URI).SetBSONOptions(bsonOpts))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to source MongoDB: %w", err)
	}

	// Ping the source MongoDB to verify connection
	if err := sourceClient.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping source MongoDB: %w", err)
	}

	return &MigrationService{
		sourceClient: sourceClient,
		sourceDB:     sourceClient.Database(sourceConfig.DatabaseName),
		targetDB:     db.DB,
		ctx:          ctx,
		log:          log,
	}, nil
}

// Close closes the connection to the source MongoDB
func (m *MigrationService) Close() error {
	return m.sourceClient.Disconnect(m.ctx)
}

// MigrateAll runs all migration functions
func (m *MigrationService) MigrateAll() error {
	m.log.Info("Starting migration process...")

	if err := m.MigrateCompanies(); err != nil {
		return fmt.Errorf("company migration failed: %w", err)
	}
	m.log.Info("Companies migration completed")

	if err := m.MigrateEnterpriseGroups(); err != nil {
		return fmt.Errorf("enterprise groups migration failed: %w", err)
	}
	m.log.Info("Enterprise groups migration completed")

	if err := m.MigrateUsers(); err != nil {
		return fmt.Errorf("users migration failed: %w", err)
	}
	m.log.Info("Users migration completed")

	m.log.Info("Migration process completed successfully")
	return nil
}
