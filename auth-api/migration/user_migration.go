package migration

import (
	"auth-api/internal/db/models"
	"fmt"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type PermissionNameIdMap map[string]bson.ObjectID

// OldUser represents the structure of the user in the old MongoDB
type OldUser struct {
	ID               bson.ObjectID `bson:"_id,omitempty"`
	Name             string        `bson:"name"`
	Email            string        `bson:"email"`
	PermissionsGroup string        `bson:"permissionsGroup"`
	Role             string        `bson:"role"`

	EpikEngineering bool `json:"epikEngineering"`
	Manager         bool `json:"manager"`

	ExtraPermissions map[models.Permission]models.PermissionsSchema `bson:"extraPermissions"`
}

// // UserPermissions represents the structure of user permissions in the old MongoDB
// type UserPermissions struct {
// 	FaxNotifications   bool               `bson:"faxNotifications"`
// 	ShowCallRecording  bool               `bson:"showCallRecording"`
// 	VmailNotifications VmailNotifications `bson:"vmailNotifications"`
// 	EmailNotifications bool               `bson:"emailNotifications"`
// 	ShowCallLogs       bool               `bson:"showCallLogs"`
// }

//Migrate SuperAdmins

func (m *MigrationService) migrateSuperAdmin(cDB *mongo.Database, tDB *mongo.Database, sCol *mongo.Collection, tCol *mongo.Collection, permissionNameIdMap PermissionNameIdMap, useraccessv2 *mongo.Collection) error {
	cursor, err := sCol.Find(m.ctx, bson.M{"role": "superAdmin"})
	if err != nil {
		return fmt.Errorf("failed to find users in source DB: %w", err)
	}
	defer cursor.Close(m.ctx)
	for cursor.Next(m.ctx) {
		var oldUser OldUser
		var access models.UserAccess
		if err := cursor.Decode(&oldUser); err != nil {
			m.log.Errorf("Failed to decode user: %v", err)
			continue
		}
		err := useraccessv2.FindOne(m.ctx, bson.M{"userId": oldUser.ID}).Decode(&access)
		if err == mongo.ErrNoDocuments {
			access.UserId = oldUser.ID
			access.Type = "admin"
			access.Manager = oldUser.Manager
			access.EpikEngineering = oldUser.EpikEngineering
			access.AddonPermissions = oldUser.ExtraPermissions
			useraccessv2.InsertOne(m.ctx, access)
			useraccessv2.FindOne(m.ctx, bson.M{"userId": oldUser.ID}).Decode(&access)
		}
		permissionGroupsArr := bson.A{}
		if !permissionNameIdMap[oldUser.PermissionsGroup].IsZero() {
			permissionGroupsArr = append(permissionGroupsArr, permissionNameIdMap[oldUser.PermissionsGroup])
		}
		m.log.Info(permissionGroupsArr)
		_, _err := useraccessv2.UpdateOne(
			m.ctx,
			bson.M{"userId": oldUser.ID},
			bson.M{"$set": bson.M{
				"permissionGroups": permissionGroupsArr,
			},
			},
		)
		if _err != nil {
			m.log.Error("updateusererr", _err)
		}
	}
	return nil
}

//Migrate company group admins

func (m *MigrationService) migrateCompanyGroupAdmins(cDB *mongo.Database, tDB *mongo.Database, sCol *mongo.Collection, tCol *mongo.Collection, permissionNameIdMap PermissionNameIdMap) error {
	cursor, err := sCol.Find(m.ctx, bson.M{"role": "companyGroupAdmin"})
	if err != nil {
		return fmt.Errorf("failed to find users in source DB: %w", err)
	}
	defer cursor.Close(m.ctx)
	for cursor.Next(m.ctx) {
		var oldUser OldUser
		if err := cursor.Decode(&oldUser); err != nil {
			m.log.Errorf("Failed to decode user: %v", err)
			continue
		}
		m.log.Info(oldUser.PermissionsGroup)
		permissionGroupsArr := bson.A{}
		if !permissionNameIdMap[oldUser.PermissionsGroup].IsZero() {
			permissionGroupsArr = append(permissionGroupsArr, permissionNameIdMap[oldUser.PermissionsGroup])
		}
		_, _err := tCol.UpdateOne(
			m.ctx,
			bson.M{"_id": oldUser.ID},
			bson.M{"$set": bson.M{
				"access.type":             "admin",
				"access.addonPermissions": oldUser.ExtraPermissions,
				"access.permissionGroups": permissionGroupsArr,
			},
			},
		)
		if _err != nil {
			m.log.Error("updateusererr", _err)
		}
	}
	return nil
}

// MigrateUsers migrates users from the old MongoDB to the new one
func (m *MigrationService) MigrateUsers() error {
	sourceCollection := m.sourceDB.Collection("users")
	targetCollection := m.targetDB.Collection("users")
	useraccessv2 := m.targetDB.Collection("useraccessv2")
	sourcePermissionGroupsCol := m.sourceDB.Collection("permissiongroups")

	var permissionNameIdMap = make(PermissionNameIdMap)
	cursor, _ := sourcePermissionGroupsCol.Find(m.ctx, bson.M{})

	defer cursor.Close(m.ctx)

	for cursor.Next(m.ctx) {
		var permission models.PermissionGroup
		if err := cursor.Decode(&permission); err != nil {
			m.log.Errorf("Failed to decode user: %v", err)
			continue
		}
		m.log.Info(permission.Permissions[""])
		permissionNameIdMap[permission.Title] = permission.ID
	}
	m.log.Info("permissionNameIdMap", permissionNameIdMap)

	err := m.migrateSuperAdmin(m.sourceDB, m.targetDB, sourceCollection, targetCollection, permissionNameIdMap, useraccessv2)
	if err != nil {
		return err
	}

	return nil
}

// func (m *MigrationService) MigrateUsers() error {

// 	// Create a cursor for all users in the source collection

// 	// Iterate through each document
//
//

// 		// Map old user permissions to new permission groups
// 		permissionGroupID, err := m.createOrGetPermissionGroup(oldUser)
// 		if err != nil {
// 			m.log.Errorf("Failed to create permission group for user %s: %v", oldUser.Email, err)
// 			continue
// 		}

// 		// Transform to new User model
// 		newUser := models.User{
// 			ID:                    oldUser.ID,
// 			Name:                  oldUser.Name,
// 			Email:                 oldUser.Email,
// 			PhoneNumber:           oldUser.PhoneNumber,
// 			RegisterDate:          bson.DateTime(oldUser.RegisterDate.UnixNano() / int64(time.Millisecond)),
// 			Enabled:               oldUser.Enabled,
// 			WarningStatus:         oldUser.WarningStatus,
// 			Password:              oldUser.Password,
// 			TwoFactor:             oldUser.TwoFactor,
// 			TwoFactorKey:          oldUser.TwoFactorKey,
// 			SmsCode:               oldUser.SmsCode,
// 			SmsRetries:            oldUser.SmsRetries,
// 			EmailCode:             oldUser.EmailCode,
// 			EmailRetries:          oldUser.EmailRetries,
// 			Deleted:               oldUser.Deleted,
// 			ProfilePic:            oldUser.ProfilePic,
// 			ResetPassword:         oldUser.ResetPassword,
// 			LockReminder:          oldUser.LockReminder,
// 			PreviousPasswords:     oldUser.PreviousPasswords,
// 			LastLoggedIn:          bson.DateTime(oldUser.LastLoggedIn.UnixNano() / int64(time.Millisecond)),
// 			MaintenanceNoteSeen:   oldUser.MaintenanceNoteSeen,
// 			PasswordLastUpdated:   bson.DateTime(oldUser.PasswordLastUpdated.UnixNano() / int64(time.Millisecond)),
// 			PasswordExpiryInDays:  oldUser.PasswordExpiryInDays,
// 			WarnedAboutInactivity: oldUser.WarnedAboutInactivity,
// 			DeleteLock:            oldUser.DeleteLock,
// 			TimeZone:              oldUser.TimeZone,
// 			Default2FA:            oldUser.Default2fa,
// 			AccountLocked:         oldUser.AccountLocked,
// 			PermissionsGroup:      permissionGroupID,
// 			ExtraPermissions:      make(map[models.Permission]models.PermissionsSchema),
// 			CreatedAt:             bson.DateTime(oldUser.RegisterDate.UnixNano() / int64(time.Millisecond)),
// 			UpdatedAt:             bson.DateTime(time.Now().UnixNano() / int64(time.Millisecond)),
// 		}

// 		// Check if the user already exists
// 		var existingUser models.User
// 		err = targetCollection.FindOne(m.ctx, bson.M{"_id": oldUser.ID}).Decode(&existingUser)

// 		if err == mongo.ErrNoDocuments {
// 			// Insert the new user
// 			_, err = targetCollection.InsertOne(m.ctx, newUser)
// 			if err != nil {
// 				m.log.Errorf("Failed to insert user %s: %v", oldUser.Email, err)
// 				continue
// 			}
// 			m.log.Infof("User migrated: %s", oldUser.Email)
// 		} else if err != nil {
// 			m.log.Errorf("Error checking for existing user %s: %v", oldUser.Email, err)
// 			continue
// 		} else {
// 			// Update the existing user
// 			_, err = targetCollection.ReplaceOne(m.ctx, bson.M{"_id": oldUser.ID}, newUser)
// 			if err != nil {
// 				m.log.Errorf("Failed to update user %s: %v", oldUser.Email, err)
// 				continue
// 			}
// 			m.log.Infof("User updated: %s", oldUser.Email)
// 		}
// 	}

// 	if err := cursor.Err(); err != nil {
// 		return fmt.Errorf("cursor error: %w", err)
// 	}

// 	return nil
// }

// // createOrGetPermissionGroup creates or gets a permission group for a user
// func (m *MigrationService) createOrGetPermissionGroup(oldUser OldUser) (bson.ObjectID, error) {
// 	permissionGroupCollection := m.targetDB.Collection("permissiongroup")

// 	// Define a permission group based on user's role and existing permissions
// 	groupName := fmt.Sprintf("%s_%s", oldUser.Role, oldUser.Email)

// 	// Check if the permission group already exists
// 	var existingGroup struct {
// 		ID bson.ObjectID `bson:"_id"`
// 	}
// 	err := permissionGroupCollection.FindOne(m.ctx, bson.M{"name": groupName}).Decode(&existingGroup)
// 	if err != nil && err != mongo.ErrNoDocuments {
// 		return bson.ObjectID{}, fmt.Errorf("error checking for existing permission group: %w", err)
// 	}

// 	// If the group exists, return its ID
// 	if err == nil {
// 		return existingGroup.ID, nil
// 	}

// 	// Create permissions based on the old user's permissions and role
// 	permissions := mapOldPermissionsToNew(oldUser)

// 	// Create a new permission group
// 	permGroup := struct {
// 		ID          bson.ObjectID          `bson:"_id"`
// 		Name        string                 `bson:"name"`
// 		Description string                 `bson:"description"`
// 		Permissions models.PermissionGroup `bson:"permissions"`
// 		CreatedAt   bson.DateTime          `bson:"createdAt"`
// 		UpdatedAt   bson.DateTime          `bson:"updatedAt"`
// 	}{
// 		ID:          bson.NewObjectID(),
// 		Name:        groupName,
// 		Description: fmt.Sprintf("Migrated permissions for %s with role %s", oldUser.Email, oldUser.Role),
// 		Permissions: permissions,
// 		CreatedAt:   bson.DateTime(time.Now().UnixNano() / int64(time.Millisecond)),
// 		UpdatedAt:   bson.DateTime(time.Now().UnixNano() / int64(time.Millisecond)),
// 	}

// 	_, err = permissionGroupCollection.InsertOne(m.ctx, permGroup)
// 	if err != nil {
// 		return bson.ObjectID{}, fmt.Errorf("failed to create permission group: %w", err)
// 	}

// 	return permGroup.ID, nil
// }

// // mapOldPermissionsToNew maps old user permissions to new permission groups structure
// func mapOldPermissionsToNew(oldUser OldUser) models.PermissionGroup {
// 	// Initialize a map to hold permissions
// 	permissions := models.PermissionGroup{}

// 	// Map permissions based on user's old permissions and role
// 	// This is a simplified example - you'll need to adjust according to your new permission structure

// 	// Basic permissions based on user role
// 	// switch oldUser.Role {
// 	// case "superAdmin":
// 	// 	permissions["admin"] = true
// 	// 	permissions["user.management"] = true
// 	// 	permissions["company.management"] = true
// 	// 	// Add more permissions as needed
// 	// case "admin":
// 	// 	permissions["user.management"] = true
// 	// 	permissions["company.view"] = true
// 	// 	// Add more permissions as needed
// 	// case "user":
// 	// 	permissions["user.view"] = true
// 	// 	// Add more permissions as needed
// 	// }

// 	// // Additional permissions based on specific flags
// 	// if oldUser.DataCenterMonitor {
// 	// 	permissions["datacenter.monitor"] = true
// 	// }

// 	// if oldUser.DeviceMonitor {
// 	// 	permissions["device.monitor"] = true
// 	// }

// 	// if oldUser.NumberCarrierChageAccess {
// 	// 	permissions["number.carrier.change"] = true
// 	// }

// 	// if oldUser.Permissions.ShowCallRecording {
// 	// 	permissions["call.recording.view"] = true
// 	// }

// 	// if oldUser.Permissions.ShowCallLogs {
// 	// 	permissions["call.logs.view"] = true
// 	// }

// 	// if oldUser.PortingAccessEnabled {
// 	// 	permissions["porting.access"] = true
// 	// }

// 	// Add more mappings as needed based on your new permission structure

// 	return permissions
// }
