package migration

import (
	"auth-api/internal/db/models"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

// OldCompany represents the structure of the company in the old MongoDB
type OldCompany struct {
	ID              bson.ObjectID   `bson:"_id,omitempty"`
	CreationDate    time.Time       `bson:"creationDate"`
	TotalBoxes      int             `bson:"totalBoxes"`
	EpikCustomerId  string          `bson:"epikCustomerId"`
	ContactPerson   ContactPerson   `bson:"contactPerson"`
	ContactEmails   []string        `bson:"contactEmails"`
	Name            string          `bson:"name"`
	Permissions     Permissions     `bson:"permissions"`
	Enabled         bool            `bson:"enabled"`
	CompanyGroups   []bson.ObjectID `bson:"companyGroups"`
	CompanyGroup    bson.ObjectID   `bson:"companyGroup"`
	Deleted         bool            `bson:"deleted"`
	Logo            string          `bson:"logo"`
	Addresses       []Address       `bson:"addresses"`
	CompanyLocation CompanyLocation `bson:"companyLocation"`
	CompanyType     string          `bson:"companyType"`
}

// ContactPerson represents the structure of contact person in the old MongoDB
type ContactPerson struct {
	Name  string `bson:"name"`
	Email string `bson:"email"`
	Phone string `bson:"phone"`
}

// CompanyLocation represents the structure of company location in the old MongoDB
type CompanyLocation struct {
	Address1 string `bson:"address1"`
	Address2 string `bson:"address2"`
	City     string `bson:"city"`
	State    string `bson:"state"`
	Address  string `bson:"address"`
	Zip      string `bson:"zip"`
	Country  string `bson:"country"`
}

// Address represents the structure of address in the old MongoDB
type Address struct {
	City    string `bson:"city"`
	State   string `bson:"state"`
	Address string `bson:"address"`
	Zip     string `bson:"zip"`
}

// Permissions represents the structure of permissions in the old MongoDB
type Permissions struct {
	FaxNotifications         bool               `bson:"faxNotifications"`
	FaxDeliveryConfirmations bool               `bson:"faxDeliveryConfirmations"`
	VmailNotifications       VmailNotifications `bson:"vmailNotifications"`
	EmailNotifications       bool               `bson:"emailNotifications"`
	ShowCallRecording        bool               `bson:"showCallRecording"`
	ShowCallLogs             bool               `bson:"showCallLogs"`
	ShowFaxDownload          bool               `bson:"showFaxDownload"`
}

// VmailNotifications represents the structure of vmail notifications in the old MongoDB
type VmailNotifications struct {
	Text  bool `bson:"text"`
	Email bool `bson:"email"`
}

// MigrateCompanies migrates companies from the old MongoDB to the new one
func (m *MigrationService) MigrateCompanies() error {
	sourceCollection := m.sourceDB.Collection("companies")
	targetCollection := m.targetDB.Collection("companiesv2")

	// Create a cursor for all companies in the source collection
	cursor, err := sourceCollection.Find(m.ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to find companies in source DB: %w", err)
	}
	defer cursor.Close(m.ctx)

	// Iterate through each document
	for cursor.Next(m.ctx) {
		var oldCompany OldCompany
		if err := cursor.Decode(&oldCompany); err != nil {
			m.log.Errorf("Failed to decode company: %v", err)
			continue
		}

		// Transform to new Company model
		newCompany := models.Company{
			ID:             oldCompany.ID,
			EpikCustomerId: oldCompany.EpikCustomerId,
			Name:           oldCompany.Name,
			Logo:           oldCompany.Logo,
			CompanyState:   getCompanyState(oldCompany.Deleted, oldCompany.Enabled),
			ContactEmails:  oldCompany.ContactEmails,
			ContactPerson: models.CompanyContactPerson{
				Name:  oldCompany.ContactPerson.Name,
				Email: oldCompany.ContactPerson.Email,
				Phone: oldCompany.ContactPerson.Phone,
			},
			Location: models.CompanyLocation{
				City:    oldCompany.CompanyLocation.City,
				State:   oldCompany.CompanyLocation.State,
				Address: oldCompany.CompanyLocation.Address,
				Zip:     oldCompany.CompanyLocation.Zip,
			},
			CreatedAt: oldCompany.CreationDate,
			UpdatedAt: time.Now(),
		}

		// Check if the company already exists
		var existingCompany models.Company
		err := targetCollection.FindOne(m.ctx, bson.M{"_id": oldCompany.ID}).Decode(&existingCompany)

		if err == mongo.ErrNoDocuments {
			// Insert the new company
			_, err = targetCollection.InsertOne(m.ctx, newCompany)
			if err != nil {
				m.log.Errorf("Failed to insert company %s: %v", oldCompany.Name, err)
				continue
			}
			m.log.Infof("Company migrated: %s", oldCompany.Name)
		} else if err != nil {
			m.log.Errorf("Error checking for existing company %s: %v", oldCompany.Name, err)
			continue
		} else {
			// Update the existing company
			_, err = targetCollection.ReplaceOne(m.ctx, bson.M{"_id": oldCompany.ID}, newCompany)
			if err != nil {
				m.log.Errorf("Failed to update company %s: %v", oldCompany.Name, err)
				continue
			}
			m.log.Infof("Company updated: %s", oldCompany.Name)
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error: %w", err)
	}

	return nil
}

// getCompanyState determines the company state based on deleted and enabled flags
func getCompanyState(deleted, enabled bool) models.CompanyState {
	if deleted {
		return models.CompanyDelete
	}
	if !enabled {
		return models.CompanyDisabled
	}
	return models.CompanyEnabled
}
