package migration

import (
	"auth-api/internal/db/models"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

// OldCompanyGroup represents the company group structure in the old MongoDB
type OldCompanyGroup struct {
	ID                bson.ObjectID   `bson:"_id,omitempty"`
	Name              string          `bson:"name"`
	GroupSuperAdmins  []bson.ObjectID `bson:"groupSuperAdmins"`
	GroupManagers     []bson.ObjectID `bson:"groupManagers"`
	SupportEngineers  []bson.ObjectID `bson:"supportEngineers"`
	ManagedCompanies  []bson.ObjectID `bson:"managedCompanies"`
	UnassingedCompany bson.ObjectID   `bson:"unassingedCompany"`
	Company           bson.ObjectID   `bson:"company"`
	Deleted           bool            `bson:"deleted"`
	HostTag           string          `bson:"hostTag"`
	NotificationMails []string        `bson:"notificationMails"`
}

// OldEnterpriseGroup represents the enterprise group structure in the old MongoDB
type OldEnterpriseGroup struct {
	ID                bson.ObjectID   `bson:"_id,omitempty"`
	Name              string          `bson:"name"`
	TagName           string          `bson:"tagName"`
	ManagementCompany bson.ObjectID   `bson:"managementCompany"`
	MemberUsers       []bson.ObjectID `bson:"memberUsers"`
	MemberCompanies   []bson.ObjectID `bson:"memberCompanies"`
	IsDeleted         bool            `bson:"isDeleted"`
	CreatedAt         time.Time       `bson:"createdAt"`
	UpdatedAt         time.Time       `bson:"updatedAt"`
}

// MigrateEnterpriseGroups migrates enterprise groups from the old MongoDB to the new one
func (m *MigrationService) MigrateEnterpriseGroups() error {
	// First, migrate the enterprise groups
	if err := m.migrateEnterpriseGroupsCollection(); err != nil {
		return err
	}

	// Then, integrate company groups into enterprises
	return m.integrateCompanyGroupsIntoEnterprises()
}

// migrateEnterpriseGroupsCollection migrates the enterprise groups collection
func (m *MigrationService) migrateEnterpriseGroupsCollection() error {
	sourceCollection := m.sourceDB.Collection("enterprisegroups")
	targetCollection := m.targetDB.Collection("enterprisesv2")

	// Create a cursor for all enterprise groups in the source collection
	cursor, err := sourceCollection.Find(m.ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to find enterprise groups in source DB: %w", err)
	}
	defer cursor.Close(m.ctx)

	// Iterate through each document
	for cursor.Next(m.ctx) {
		var oldEnterpriseGroup OldEnterpriseGroup
		if err := cursor.Decode(&oldEnterpriseGroup); err != nil {
			m.log.Errorf("Failed to decode enterprise group: %v", err)
			continue
		}

		// Transform to new Enterprise model
		newEnterprise := models.Enterprise{
			ID:              oldEnterpriseGroup.ID,
			Name:            oldEnterpriseGroup.Name + " (old enterprise)",
			EnterpriseState: getEnterpriseState(oldEnterpriseGroup.IsDeleted, true),
			CreatedAt:       bson.DateTime(time.Now().Unix()),
			UpdatedAt:       bson.DateTime(time.Now().Unix()),
		}

		// Check if the enterprise already exists
		var existingEnterprise models.Enterprise
		err := targetCollection.FindOne(m.ctx, bson.M{"_id": oldEnterpriseGroup.ID}).Decode(&existingEnterprise)

		if err == mongo.ErrNoDocuments {
			// Insert the new enterprise
			_, err = targetCollection.InsertOne(m.ctx, newEnterprise)
			if err != nil {
				m.log.Errorf("Failed to insert enterprise %s: %v", oldEnterpriseGroup.Name, err)
				continue
			}
			m.log.Infof("Enterprise migrated: %s", oldEnterpriseGroup.Name)
		} else if err != nil {
			m.log.Errorf("Error checking for existing enterprise %s: %v", oldEnterpriseGroup.Name, err)
			continue
		} else {
			// Update the existing enterprise
			_, err = targetCollection.ReplaceOne(m.ctx, bson.M{"_id": oldEnterpriseGroup.ID}, newEnterprise)
			if err != nil {
				m.log.Errorf("Failed to update enterprise %s: %v", oldEnterpriseGroup.Name, err)
				continue
			}
			m.log.Infof("Enterprise updated: %s", oldEnterpriseGroup.Name)
		}

		// Update companies with this enterprise
		companiesCollection := m.targetDB.Collection("companiesv2")
		for _, companyID := range oldEnterpriseGroup.MemberCompanies {
			_, err := companiesCollection.UpdateOne(
				m.ctx,
				bson.M{"_id": companyID},
				bson.M{"$addToSet": bson.M{"enterprises": oldEnterpriseGroup.ID}},
			)
			if err != nil {
				m.log.Errorf("Failed to update company %s with enterprise %s: %v", companyID.Hex(), oldEnterpriseGroup.Name, err)
			}
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error: %w", err)
	}

	return nil
}

// integrateCompanyGroupsIntoEnterprises integrates company groups into enterprises
func (m *MigrationService) integrateCompanyGroupsIntoEnterprises() error {
	sourceCollection := m.sourceDB.Collection("companygroups")

	// Create a cursor for all company groups in the source collection that are not deleted
	cursor, err := sourceCollection.Find(m.ctx, bson.M{"deleted": false})
	if err != nil {
		return fmt.Errorf("failed to find company groups in source DB: %w", err)
	}
	defer cursor.Close(m.ctx)

	// Iterate through each document
	for cursor.Next(m.ctx) {
		var oldCompanyGroup OldCompanyGroup
		if err := cursor.Decode(&oldCompanyGroup); err != nil {
			m.log.Errorf("Failed to decode company group: %v", err)
			continue
		}

		// Create a new enterprise for the company group
		enterpriseCollection := m.targetDB.Collection("enterprisesv2")
		newEnterprise := models.Enterprise{
			ID:              bson.NewObjectID(),
			Name:            oldCompanyGroup.Name,
			EnterpriseState: getEnterpriseState(oldCompanyGroup.Deleted, true),
			CreatedAt:       bson.DateTime(time.Now().Unix()),
			UpdatedAt:       bson.DateTime(time.Now().Unix()),
		}

		_, err = enterpriseCollection.InsertOne(m.ctx, newEnterprise)
		if err != nil {
			m.log.Errorf("Failed to create enterprise from company group %s: %v", oldCompanyGroup.Name, err)
			continue
		}
		m.log.Infof("Enterprise created from company group: %s", oldCompanyGroup.Name)

		// Update companies with this enterprise
		companiesCollection := m.targetDB.Collection("companiesv2")
		for _, companyID := range oldCompanyGroup.ManagedCompanies {
			_, err := companiesCollection.UpdateOne(
				m.ctx,
				bson.M{"_id": companyID},
				bson.M{"$addToSet": bson.M{"enterprises": newEnterprise.ID}},
			)
			if err != nil {
				m.log.Errorf("Failed to update company %s with enterprise %s: %v", companyID.Hex(), newEnterprise.Name, err)
			}
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error: %w", err)
	}

	return nil
}
func getEnterpriseState(deleted, enabled bool) models.EnterpriseState {
	if deleted {
		return models.EnterpriseDelete
	}
	if !enabled {
		return models.EnterpriseDisabled
	}
	return models.EnterpriseEnabled
}
