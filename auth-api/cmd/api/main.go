package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	grpc_server "auth-api/cmd/grpc"
	"auth-api/internal/api/handlers"
	"auth-api/internal/common/global"
	"auth-api/internal/common/logger"
	"auth-api/internal/config"
	"auth-api/internal/db"
)

func main() {
	log := logger.NewLogger()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup signal handling
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)

	// Load configuration
	cfg := config.Load()

	// Connect to databases
	log.Info("Connecting to MongoDB...")
	db.Connect(cfg.MongoURI)
	log.Info("Connecting to Redis...")
	db.NewRedisClient(db.DefaultRedisConfig(cfg))

	locals := global.SetupLocals(ctx, &cfg)

	permissionService := locals.Service.Permissions

	grpc_server.StartGRPCServer(ctx, &cfg, permissionService)

	r := handlers.SetupRoutes(cfg, locals)

	go func() {
		log.Infof("🚀 HTTP Server running on port %s", cfg.Port)
		if err := r.Listen(":" + cfg.Port); err != nil {
			log.Fatalf("Server error: %v", err)
		}
	}()

	<-quit
	log.Info("Shutting down servers...")

	cancel()

	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := r.ShutdownWithContext(shutdownCtx); err != nil {
		log.Fatalf("HTTP Server forced to shutdown: %v", err)
	}

	log.Info("Servers gracefully stopped")
}
