package main

import (
	"auth-api/internal/config"
	"auth-api/internal/db"
	"auth-api/migration"
	"flag"
	"fmt"
	"os"
)

func main() {
	// Parse command line flags
	sourceURI := flag.String("source-uri", "", "MongoDB URI for the source database")
	sourceDB := flag.String("source-db", "", "Source database name")
	flag.Parse()

	// Validate flags
	if *sourceURI == "" || *sourceDB == "" {
		fmt.Println("Error: Both source-uri and source-db must be provided")
		fmt.Println("Usage: migrate --source-uri=mongodb://localhost:27017 --source-db=oldDatabaseName")
		os.Exit(1)
	}

	// Load configuration for the target database
	cfg := config.Load()

	// Connect to the target database
	db.Connect(cfg.MongoURI)

	// Initialize migration service
	migrationService, err := migration.NewMigrationService(migration.SourceConfig{
		URI:          *sourceURI,
		DatabaseName: *sourceDB,
	})
	if err != nil {
		fmt.Printf("Failed to initialize migration service: %v\n", err)
		os.Exit(1)
	}
	defer migrationService.Close()

	// Run migration
	if err := migrationService.MigrateAll(); err != nil {
		fmt.Printf("Migration failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Migration completed successfully")
}
