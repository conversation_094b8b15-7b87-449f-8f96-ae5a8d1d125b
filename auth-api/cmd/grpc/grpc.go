package grpc_server

import (
	"context"
	"net"

	"auth-api/internal/common/logger"
	"auth-api/internal/config"
	"auth-api/internal/grpc"
	"auth-api/internal/services"
	pb "auth-api/proto"

	grpcServer "google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func StartGRPCServer(
	ctx context.Context,
	cfg *config.Config,
	permissionService *services.PermissionService,
) {
	log := logger.NewLogger()
	grpcPort := cfg.GRPCPort

	lis, err := net.Listen("tcp", ":"+grpcPort)
	if err != nil {
		log.Fatalf("Failed to listen for gRPC: %v", err)
	}

	s := grpcServer.NewServer()

	permServer := grpc.NewPermissionServer(permissionService)
	pb.RegisterPermissionServiceServer(s, permServer)

	if cfg.DEV_MODE {
		reflection.Register(s)
	}

	log.Infof("🚀 gRPC Server running on port %s", grpcPort)
	go func() {
		if err := s.Serve(lis); err != nil {
			log.Fatalf("Failed to serve gRPC: %v", err)
		}
	}()
	go func() {
		<-ctx.Done()
		log.Info("Shutting down gRPC server...")
		s.GracefulStop()
		log.Info("gRPC server stopped")
	}()
}
