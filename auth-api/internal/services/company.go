package services

import (
	"auth-api/internal/common/logger"
	"auth-api/internal/common/transport"
	"auth-api/internal/config"
	"auth-api/internal/db/repository"
	"context"
)

// CompanyService provides methods for managing companies
type CompanyService struct {
	repo              *repository.Repositories
	ctx               context.Context
	log               *logger.Logger
	config            *config.Config
	permissionService *PermissionService
}

// NewCompanyService creates a new company service instance
func NewCompanyService(ctx context.Context, repo *repository.Repositories, log *logger.Logger, config *config.Config) *CompanyService {
	if ctx == nil {
		ctx = context.Background()
	}
	return &CompanyService{
		repo:   repo,
		ctx:    ctx,
		log:    log,
		config: config,
	}
}

func (s *CompanyService) ListCompanies(userId string, input *transport.ListCompanyInput) *transport.ListCompanyResponse {
	isAllCompanies, companies, _ := s.permissionService.ListUserAccessableCompanies(userId)
	isAllEnterprises, enterprises, _ := s.permissionService.ListUserAccessableEnterprises(userId)
	internalInput := &transport.ListCompanyInputInternal{
		IsAllCompanies: isAllCompanies, Companies: companies, IsAllEnterprises: isAllEnterprises, Enterprises: enterprises,
	}
	data := s.repo.Company.FindCompanyPaginated(input, internalInput)
	return data
}
