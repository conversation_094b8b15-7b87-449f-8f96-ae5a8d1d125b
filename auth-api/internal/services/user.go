package services

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/common/transport"
	"auth-api/internal/config"
	"auth-api/internal/db/models"
	"auth-api/internal/db/repository"
	"context"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/v2/bson"
)

type TokenPair struct {
	AccessToken  string    `json:"accessToken"`
	RefreshToken string    `json:"refreshToken"`
	ExpiresAt    time.Time `json:"expiresAt"`
}

type UserService struct {
	Repo              *repository.Repositories
	ctx               context.Context
	Log               *logger.Logger
	Config            *config.Config
	permissionService *PermissionService
}

func NewUserService(ctx context.Context, repo *repository.Repositories, log *logger.Logger, config *config.Config) *UserService {
	return &UserService{
		Repo:   repo,
		ctx:    ctx,
		Log:    log,
		Config: config,
	}
}

func (u *UserService) generateTokens(userID *string, email *string) *string {
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":    userID,
		"email": email,
		"exp":   time.Now().Add(time.Minute * 15).Unix(),
	})
	accessTokenString, err := accessToken.SignedString([]byte(u.Config.JWTSecret))
	if err != nil {
		u.Log.Error(err)
		panic(_err.New(_err.ErrInternal, ""))
	}
	return &accessTokenString
}

func (u *UserService) LoginWithMyepikToken(v1Secret *string, token *string) *string {
	v1User := middleware.ValidateToken(token, *v1Secret)
	newToken := u.generateTokens(&v1User.UserID, &v1User.Email)
	u.Repo.UserSession.Create(&models.UserSessions{
		UserId:    _err.Must(bson.ObjectIDFromHex(v1User.UserID)),
		Token:     *newToken,
		ExpiresAt: time.Now().Add(time.Duration(time.Minute * 15)),
	})
	return newToken

}

func (u *UserService) RefreshAccessToken(userId *string, token string) string {
	user := u.GetById(userId)
	findQuery := &models.UserSessions{UserId: user.ID, Token: token}
	session := u.Repo.UserSession.FindOne(findQuery)
	u.Log.Info("session", session.ExpiresAt, time.Now(), session.ExpiresAt.After(time.Now()))
	if session == nil {
		panic(_err.New(_err.ErrForbidden, "Your token is already expired."))
	}
	if session.ExpiresAt.Before(time.Now()) {
		panic(_err.New(_err.ErrForbidden, "Your token is already expired."))
	}
	newToken := u.generateTokens(userId, &user.Email)
	u.Repo.UserSession.UpdateOne(
		findQuery,
		&models.UserSessions{ExpiresAt: time.Now().Add(time.Duration(time.Minute * 15)), Token: *newToken},
		repository.SetOp,
	)

	return *newToken
}

func (u *UserService) GetById(userId *string) *models.User {
	user := u.Repo.User.FindByID(*userId)
	return user
}

func (u *UserService) ListUsers(userId string, input *transport.ListUserInput) *transport.ListUserResponse {
	u.Log.Info(input)
	isAll, bsonIds, _ := u.permissionService.ListUserAccessableCompaniesWithEnterprises(userId)
	internalInput := &transport.ListUserInputInternal{
		AllCompanies: isAll,
		Companies:    bsonIds,
	}
	data := u.Repo.User.FindUserPaginated(input, internalInput)
	return data
}
