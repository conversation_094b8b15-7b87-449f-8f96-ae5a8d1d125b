package services

import (
	"auth-api/internal/common/logger"
	"auth-api/internal/config"
	"auth-api/internal/db/repository"
	"context"
)

type Services struct {
	User        *UserService
	Company     *CompanyService
	Permissions *PermissionService
	Log         *logger.Logger
	Config      *config.Config
}

func SetupServices(ctx context.Context, repo *repository.Repositories, config *config.Config) *Services {
	log := logger.NewLogger()
	service := Services{
		Permissions: NewPermissionService(ctx, repo, log, config),
		User:        NewUserService(ctx, repo, log, config),
		Company:     NewCompanyService(ctx, repo, log, config),
		Log:         log,
		Config:      config,
	}
	service.Company.permissionService = service.Permissions
	service.User.permissionService = service.Permissions
	return &service
}
