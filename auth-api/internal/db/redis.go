package db

import (
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/config"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

type RedisConfig struct {
	WriteAddress  string
	ReadAddress   string
	Password      string
	DB            int
	PoolSize      int
	MinIdleConns  int
	DialTimeout   time.Duration
	ReadTimeout   time.Duration
	WriteTimeout  time.Duration
	PoolTimeout   time.Duration
	MaxRetries    int
	MinRetryDelay time.Duration
	MaxRetryDelay time.Duration
}

func DefaultRedisConfig(config config.Config) RedisConfig {
	uri := fmt.Sprintf("%s:%s", config.REDIS_SERVER, config.REDIS_PORT)
	return RedisConfig{
		WriteAddress:  uri, // Service pointing to master
		ReadAddress:   uri, // Service pointing to all nodes
		Password:      "",  // Add password if configured
		DB:            0,
		PoolSize:      10,
		MinIdleConns:  2,
		DialTimeout:   5 * time.Second,
		ReadTimeout:   3 * time.Second,
		WriteTimeout:  3 * time.Second,
		PoolTimeout:   4 * time.Second,
		MaxRetries:    3,
		MinRetryDelay: 8 * time.Millisecond,
		MaxRetryDelay: 500 * time.Millisecond,
	}
}

type RedisClient struct {
	writeClient *redis.Client
	readClient  *redis.Client
	ctx         context.Context
	log         *logger.Logger
}

var Redis *RedisClient

func NewRedisClient(cfg RedisConfig) *RedisClient {
	ctx := context.Background()

	writeClient := redis.NewClient(&redis.Options{
		Addr:            cfg.WriteAddress,
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        cfg.PoolSize,
		MinIdleConns:    cfg.MinIdleConns,
		DialTimeout:     cfg.DialTimeout,
		ReadTimeout:     cfg.ReadTimeout,
		WriteTimeout:    cfg.WriteTimeout,
		PoolTimeout:     cfg.PoolTimeout,
		MaxRetries:      cfg.MaxRetries,
		MinRetryBackoff: cfg.MinRetryDelay,
		MaxRetryBackoff: cfg.MaxRetryDelay,
	})

	readClient := redis.NewClient(&redis.Options{
		Addr:            cfg.ReadAddress,
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        cfg.PoolSize,
		MinIdleConns:    cfg.MinIdleConns,
		DialTimeout:     cfg.DialTimeout,
		ReadTimeout:     cfg.ReadTimeout,
		WriteTimeout:    cfg.WriteTimeout,
		PoolTimeout:     cfg.PoolTimeout,
		MaxRetries:      cfg.MaxRetries,
		MinRetryBackoff: cfg.MinRetryDelay,
		MaxRetryBackoff: cfg.MaxRetryDelay,
	})

	Redis = &RedisClient{
		writeClient: writeClient,
		readClient:  readClient,
		ctx:         ctx,
		log:         logger.NewLogger(),
	}
	Redis.Ping()
	return Redis
}

func (r *RedisClient) Close() error {
	if err := r.writeClient.Close(); err != nil {
		r.log.Error("error closing write client: ", err)
		return _err.New(_err.ErrInternal, "")
	}
	if err := r.readClient.Close(); err != nil {
		r.log.Error("error closing read client: ", err)
		return _err.New(_err.ErrInternal, "")
	}
	r.log.Info("Redis client closed")
	return nil
}

func (r *RedisClient) Ping() {
	if _, err := r.writeClient.Ping(r.ctx).Result(); err != nil {
		r.log.Error(err)
		panic("Error on ping redis write server")
	}

	if _, err := r.readClient.Ping(r.ctx).Result(); err != nil {
		r.log.Error(err)
		panic("Error on ping redis read server")
	}
	r.log.Info("Redis client connected")
}

// Set stores any value with automatic marshaling for complex types
func (r *RedisClient) Set(key string, value interface{}, expiration time.Duration) error {
	var storableValue interface{}

	// Check if value needs marshaling
	switch v := value.(type) {
	case string, []byte, int, int64, uint64, float64, bool:
		// These types are handled natively by Redis client
		storableValue = v
	default:
		// For complex types, marshal to JSON
		bytes, err := json.Marshal(v)
		if err != nil {
			return fmt.Errorf("failed to marshal value: %w", err)
		}
		storableValue = bytes
	}

	cmd := r.writeClient.Set(r.ctx, key, storableValue, expiration)
	return cmd.Err()
}

// Get retrieves a string value from Redis
func (r *RedisClient) Get(key string) (string, error) {
	cmd := r.readClient.Get(r.ctx, key)
	if cmd.Err() != nil {
		return "", cmd.Err()
	}
	return cmd.Result()
}

// GetObject retrieves and unmarshals an object from Redis
func (r *RedisClient) GetObject(key string, dest interface{}) error {
	cmd := r.readClient.Get(r.ctx, key)
	if cmd.Err() != nil {
		return cmd.Err()
	}

	data, err := cmd.Bytes()
	if err != nil {
		return fmt.Errorf("failed to get bytes: %w", err)
	}

	err = json.Unmarshal(data, dest)
	if err != nil {
		return fmt.Errorf("failed to unmarshal data: %w", err)
	}

	return nil
}

func (r *RedisClient) HSet(key string, field string, value interface{}) error {
	var storableValue interface{}

	// Check if value needs marshaling
	switch v := value.(type) {
	case string, []byte, int, int64, uint64, float64, bool:
		storableValue = v
	default:
		bytes, err := json.Marshal(v)
		if err != nil {
			return fmt.Errorf("failed to marshal value: %w", err)
		}
		storableValue = bytes
	}

	cmd := r.writeClient.HSet(r.ctx, key, field, storableValue)
	return cmd.Err()
}

func (r *RedisClient) HGet(key, field string) (string, error) {
	cmd := r.readClient.HGet(r.ctx, key, field)
	if cmd.Err() != nil {
		return "", cmd.Err()
	}
	return cmd.Result()
}

func (r *RedisClient) HGetObject(key, field string, dest interface{}) error {
	cmd := r.readClient.HGet(r.ctx, key, field)
	if cmd.Err() != nil {
		return cmd.Err()
	}

	data, err := cmd.Bytes()
	if err != nil {
		return fmt.Errorf("failed to get bytes: %w", err)
	}

	err = json.Unmarshal(data, dest)
	if err != nil {
		return fmt.Errorf("failed to unmarshal data: %w", err)
	}

	return nil
}
