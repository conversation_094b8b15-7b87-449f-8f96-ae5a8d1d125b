package repository

import (
	"context"
)

type Repositories struct {
	User            *RUser
	Company         *RCompany
	Enterprise      *REnterprise
	PermissionGroup *RPermissionGroup
	UserAccess      *RUserAccess
	UserSession     *RUserSession
}

func SetupRepositories(ctx context.Context) *Repositories {
	repo := Repositories{
		User:            SetupUserRepository(ctx),
		Company:         SetupCompanyRepository(ctx),
		Enterprise:      SetupEnterpriseRepository(ctx),
		PermissionGroup: SetupPermissionGroupRepository(ctx),
		UserAccess:      SetupUserAccessRepository(ctx),
		UserSession:     SetupUserSessionRepository(ctx),
	}
	repo.UserAccess.Repo = &repo
	return &repo
}
