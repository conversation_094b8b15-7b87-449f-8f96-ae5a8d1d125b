package repository

import (
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"
)

const UserSessionCollectionName = "usersessionv2"

type RUserSession struct {
	*Repository[models.UserSessions]
}

func SetupUserSessionRepository(ctx context.Context) *RUserSession {
	collection := db.DB.Collection(UserSessionCollectionName)
	return &RUserSession{
		NewRepository[models.UserSessions](ctx, collection),
	}
}
