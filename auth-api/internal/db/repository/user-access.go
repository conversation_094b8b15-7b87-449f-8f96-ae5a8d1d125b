package repository

import (
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type UserAccessPopulatePath string

const (
	PopulateUserAccessPermissionGroup UserAccessPopulatePath = "PermissionGroups"
	PopulateUserAccessCompanies       UserAccessPopulatePath = "Companies"
	PopulateUserAccessEnterprises     UserAccessPopulatePath = "Enterprises"
)

const UserAccessCollectionName = "useraccessv2"

type RUserAccess struct {
	*Repository[models.UserAccess]
	Repo *Repositories
}

func SetupUserAccessRepository(ctx context.Context) *RUserAccess {
	collection := db.DB.Collection(UserAccessCollectionName)
	return &RUserAccess{
		Repository: NewRepository[models.UserAccess](ctx, collection),
		Repo:       nil,
	}
}

func (s *RUserAccess) Populate(doc *models.UserAccess, paths []UserAccessPopulatePath) *models.UserAccessPopulated {
	var populated = models.UserAccessPopulated{UserAccess: *doc}

	for _, path := range paths {
		switch path {
		case PopulateUserAccessCompanies:
			companyIDs := make([]bson.ObjectID, 0, len(doc.CompanyAccess))
			for _, ca := range doc.CompanyAccess {
				companyIDs = append(companyIDs, ca.CompanyId)
			}
			companyDocs := s.Repo.Company.BsonFind(&bson.M{"_id": bson.M{"$in": companyIDs}}, []string{})
			for _, company := range *companyDocs {
				populated.CompanyAccessDocs = append(populated.CompanyAccessDocs, models.UserCompanyAccessPopulated{Company: company})
			}
		case PopulateUserAccessEnterprises:
			enterpriseIds := make([]bson.ObjectID, 0, len(doc.EnterpriseAccess))
			for _, en := range doc.EnterpriseAccess {
				enterpriseIds = append(enterpriseIds, en.EnterpriseId)
			}
			enterpriseDocs := s.Repo.Enterprise.BsonFind(&bson.M{"_id": bson.M{"$in": enterpriseIds}}, []string{})
			for _, enterprise := range *enterpriseDocs {
				populated.EnterpriseAccessDocs = append(populated.EnterpriseAccessDocs, models.UserEnterpriseAccessPopulated{Enterprise: enterprise})
			}
		case PopulateUserAccessPermissionGroup:

			permissionGroups := s.Repo.PermissionGroup.BsonFind(&bson.M{"_id": bson.M{"$in": doc.PermissionGroups}}, []string{})
			populated.PermissionGroupsDoc = *permissionGroups
		}
	}
	return &populated
}
