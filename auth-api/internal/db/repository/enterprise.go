package repository

import (
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"
)

const EnterpriseCollectionName = "enterprisesv2"

type REnterprise struct {
	*Repository[models.Enterprise]
}

func SetupEnterpriseRepository(ctx context.Context) *REnterprise {
	collection := db.DB.Collection(EnterpriseCollectionName)
	return &REnterprise{
		NewRepository[models.Enterprise](ctx, collection),
	}
}
