package repository

import (
	"auth-api/internal/common/transport"
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"

	"go.mongodb.org/mongo-driver/v2/bson"
)

const CompanyCollectionName = "companiesv2"

type RCompany struct {
	*Repository[models.Company]
}

func SetupCompanyRepository(ctx context.Context) *RCompany {
	collection := db.DB.Collection(CompanyCollectionName)
	return &RCompany{
		NewRepository[models.Company](ctx, collection),
	}
}

func (ru *RCompany) FindCompanyPaginated(input *transport.ListCompanyInput, inputInternal *transport.ListCompanyInputInternal) *transport.ListCompanyResponse {
	filter := bson.M{}
	accessOr := bson.A{}
	searchOr := bson.A{}

	if !inputInternal.IsAllCompanies {
		accessOr = append(accessOr, bson.M{"_id": bson.M{"$in": inputInternal.Companies}})
	}

	if !inputInternal.IsAllEnterprises {
		accessOr = append(accessOr, bson.M{"enterprises": bson.M{"$in": inputInternal.Enterprises}})
	}

	if input.EpikCustomerId != nil {
		filter["epikCustomerId"] = bson.M{"$regex": *input.EpikCustomerId, "$options": "i"}
	}
	if input.Name != nil {
		filter["name"] = bson.M{"$regex": *input.Name, "$options": "i"}
	}
	if input.Query != nil {
		searchOr = append(searchOr, bson.M{"name": bson.M{"$regex": *input.Query, "$options": "i"}})
		searchOr = append(searchOr, bson.M{"epikCustomerId": bson.M{"$regex": *input.Query, "$options": "i"}})
		searchOr = append(searchOr, bson.M{"contactEmails": bson.M{"$regex": *input.Query, "$options": "i"}})
		searchOr = append(searchOr, bson.M{"contactPerson.name": bson.M{"$regex": *input.Query, "$options": "i"}})
	}
	if len(accessOr) > 0 && len(searchOr) > 0 {
		filter["$and"] = bson.A{bson.M{"$or": accessOr}, bson.M{"$or": searchOr}}
	} else {
		if len(accessOr) > 0 {
			filter["$or"] = accessOr
		}
		if len(searchOr) > 0 {
			filter["$or"] = searchOr
		}
	}
	ru.Log.Info(filter)
	data := ru.FindPaginated(filter, input.Pagination.Page, input.Pagination.PageSize)
	return data
}
