package repository

import (
	"auth-api/internal/common/transport"
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"

	"go.mongodb.org/mongo-driver/v2/bson"
)

const UserCollectionName = "users"

type RUser struct {
	*Repository[models.User]
}

func SetupUserRepository(ctx context.Context) *RUser {
	collection := db.DB.Collection(UserCollectionName)
	return &RUser{
		NewRepository[models.User](ctx, collection),
	}
}

func (ru *RUser) FindUserPaginated(input *transport.ListUserInput, internalInput *transport.ListUserInputInternal) *transport.ListUserResponse {
	filter := bson.M{}
	accessOr := bson.A{}
	searchOr := bson.A{}
	if !internalInput.AllCompanies {
		accessOr = append(accessOr, bson.M{"company": bson.M{"$in": internalInput.Companies}})
	}
	if input.Company != nil {
		filter["company"] = *input.Company
	}
	if input.Email != nil {
		filter["email"] = bson.M{"$regex": *input.Email, "$options": "i"}
	}
	if input.Name != nil {
		filter["name"] = bson.M{"$regex": *input.Name, "$options": "i"}
	}
	if input.Role != nil {
		filter["role"] = *input.Role
	}
	if input.Query != nil {
		searchOr = append(searchOr, bson.M{"name": bson.M{"$regex": *input.Query, "$options": "i"}})
		searchOr = append(searchOr, bson.M{"email": bson.M{"$regex": *input.Query, "$options": "i"}})
		searchOr = append(searchOr, bson.M{"phoneNumber": bson.M{"$regex": *input.Query, "$options": "i"}})
	}

	if len(accessOr) > 0 && len(searchOr) > 0 {
		filter["$and"] = bson.A{bson.M{"$or": accessOr}, bson.M{"$or": searchOr}}
	} else {
		if len(accessOr) > 0 {
			filter["$or"] = accessOr
		}
		if len(searchOr) > 0 {
			filter["$or"] = searchOr
		}
	}
	data := ru.FindPaginated(filter, input.Pagination.Page, input.Pagination.PageSize)
	return data
}
