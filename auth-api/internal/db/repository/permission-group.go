package repository

import (
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"context"
)

const PermissionGroupCollectionName = "permissiongroups"

type RPermissionGroup struct {
	*Repository[models.PermissionGroup]
}

func SetupPermissionGroupRepository(ctx context.Context) *RPermissionGroup {
	collection := db.DB.Collection(PermissionGroupCollectionName)
	return &RPermissionGroup{
		NewRepository[models.PermissionGroup](ctx, collection),
	}
}
