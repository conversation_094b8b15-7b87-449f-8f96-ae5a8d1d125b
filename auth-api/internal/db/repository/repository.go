package repository

import (
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/common/transport"
	"context"
	"math"
	"reflect"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

type Repository[T any] struct {
	Collection *mongo.Collection
	Ctx        context.Context
	Log        *logger.Logger
}

type UpdateOperation string

const (
	SetOp      UpdateOperation = "$set"
	PushOp     UpdateOperation = "$push"
	PullOp     UpdateOperation = "$pull"
	IncOp      UpdateOperation = "$inc"
	AddToSetOp UpdateOperation = "$addToSet"
	UnsetOp    UpdateOperation = "$unset"
)

func getFieldName(field reflect.StructField) string {
	// Check for bson tag
	if tag, ok := field.Tag.Lookup("bson"); ok {
		// Parse the tag
		parts := strings.Split(tag, ",")
		if parts[0] != "" && parts[0] != "-" {
			return parts[0]
		}
	}

	// Default to field name (you can choose to convert to snake_case or camelCase)
	return strings.ToLower(field.Name[:1]) + field.Name[1:]
}

func toBSONMap(doc any) bson.M {
	if doc == nil {
		return bson.M{}
	}

	filter := bson.M{}
	v := reflect.ValueOf(doc)

	// If pointer, get the underlying value
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return filter
		}
		v = v.Elem()
	}

	// Must be a struct
	if v.Kind() != reflect.Struct {
		return filter
	}

	t := v.Type()

	// Iterate through struct fields
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)

		// Skip unexported fields
		if field.PkgPath != "" {
			continue
		}

		// Skip zero values
		if value.IsZero() {
			continue
		}

		// Get field name from BSON tag or use field name
		fieldName := getFieldName(field)

		// Add to filter
		filter[fieldName] = value.Interface()
	}

	return filter
}

func NewRepository[T any](ctx context.Context, collection *mongo.Collection) *Repository[T] {
	log := logger.NewLogger()
	return &Repository[T]{
		Collection: collection,
		Ctx:        ctx,
		Log:        log,
	}
}

func setDefaultIfNil(doc any, fieldName string, value any) {
	v := reflect.ValueOf(doc)
	if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
		return
	}
	v = v.Elem()
	field := v.FieldByName(fieldName)
	if !field.IsValid() || !field.CanSet() {
		return
	}
	val := reflect.ValueOf(value)
	if field.Type() != val.Type() {
		return
	}
	if reflect.DeepEqual(field.Interface(), reflect.Zero(field.Type()).Interface()) {
		field.Set(val)
	}
}

func (r *Repository[T]) FindByID(id string) *T {
	var out T
	_id := _err.Must(bson.ObjectIDFromHex(id))
	filter := bson.M{"_id": _id}
	e := r.Collection.FindOne(r.Ctx, filter).Decode(&out)
	if e == mongo.ErrNoDocuments {
		return nil
	}
	if e != nil {
		panic(e)
	}
	return &out
}

func (r *Repository[T]) FindOne(filter *T) *T {
	var out T
	bsonFilter := toBSONMap(filter)
	e := r.Collection.FindOne(r.Ctx, bsonFilter).Decode(&out)
	if e == mongo.ErrNoDocuments {
		return nil
	}
	if e != nil {
		panic(e)
	}
	return &out
}

func (r *Repository[T]) Find(filter *T) *[]T {
	var out []T
	bsonFilter := toBSONMap(filter)
	cursor := _err.Must(r.Collection.Find(r.Ctx, bsonFilter))
	if e := cursor.All(r.Ctx, &out); e != nil {
		panic(e)
	}
	return &out
}

func (r *Repository[T]) BsonFind(filter *bson.M, fields []string) *[]T {
	var out []T
	projection := bson.M{}
	for _, field := range fields {
		projection[field] = 1
	}
	findOptions := options.Find().SetProjection(projection)

	cursor := _err.Must(r.Collection.Find(r.Ctx, filter, findOptions))
	if e := cursor.All(r.Ctx, &out); e != nil {
		panic(e)
	}
	return &out
}

func (r *Repository[T]) FindPaginated(filter interface{}, page, pageSize int64) *transport.PaginatedResponse[T] {
	if pageSize <= 0 {
		pageSize = 10
	}
	if page < 1 {
		page = 1
	}

	var out transport.PaginatedResponse[T]
	// bsonFilter := toBSONMap(filter)
	skip := (page - 1) * pageSize
	findOptions := options.Find().SetSkip(skip).SetLimit(pageSize)

	cursor := _err.Must(r.Collection.Find(r.Ctx, filter, findOptions))
	if e := cursor.All(r.Ctx, &out.Docs); e != nil {
		panic(e)
	}

	totalCount := _err.Must(r.Collection.CountDocuments(r.Ctx, filter))

	out.Pagination.Count = totalCount
	out.Pagination.TotalPages = int64(math.Ceil(float64(totalCount) / float64(pageSize)))
	out.Pagination.CurrentPage = page
	return &out
}

func (r *Repository[T]) Create(doc *T) *string {
	setDefaultIfNil(&doc, "CreatedAt", time.Now())
	setDefaultIfNil(&doc, "UpdatedAt", time.Now())
	logger.NewLogger().Info("create", doc)
	createdDoc := _err.Must(r.Collection.InsertOne(r.Ctx, doc))
	id := createdDoc.InsertedID.(bson.ObjectID).Hex()
	return &id
}

// func (r *Repository[T]) CreateMany(doc []T) *[]string {
// 	createdDoc := _err.Must(r.Collection.InsertMany(r.Ctx, doc))
// 	var ids []string
// 	for _, id := range createdDoc.InsertedIDs {
// 		ids = append(ids, id.(bson.ObjectID).Hex())
// 	}
// 	return &ids
// }

func (r *Repository[T]) UpdateOne(filter *T, doc *T, operation UpdateOperation) {
	bsonFilter := toBSONMap(filter)
	update := bson.M{string(operation): toBSONMap(doc)}
	_err.Must(r.Collection.UpdateOne(r.Ctx, bsonFilter, update))
}

func (r *Repository[T]) UpdateMany(filter T, doc T, operation UpdateOperation) {
	bsonFilter := toBSONMap(filter)
	update := bson.M{string(operation): toBSONMap(doc)}
	_err.Must(r.Collection.UpdateMany(r.Ctx, bsonFilter, update))
}

func (r *Repository[T]) UpdateOneComplex(filter T, updates map[UpdateOperation]T) {
	bsonFilter := toBSONMap(filter)

	updateDoc := bson.M{}
	for op, val := range updates {
		updateDoc[string(op)] = toBSONMap(val)
	}

	_err.Must(r.Collection.UpdateOne(r.Ctx, bsonFilter, updateDoc))
}

func (r *Repository[T]) UpdateManyComplex(filter T, updates map[UpdateOperation]T) {
	bsonFilter := toBSONMap(filter)

	updateDoc := bson.M{}
	for op, val := range updates {
		updateDoc[string(op)] = toBSONMap(val)
	}

	_err.Must(r.Collection.UpdateMany(r.Ctx, bsonFilter, updateDoc))
}

func (r *Repository[T]) DeleteOne(filter *T) {
	bsonFilter := _err.Must(bson.Marshal(filter))
	_, e := r.Collection.DeleteOne(r.Ctx, bsonFilter)
	if e != nil {
		r.Log.Error(e)
		panic(e)
	}
}
