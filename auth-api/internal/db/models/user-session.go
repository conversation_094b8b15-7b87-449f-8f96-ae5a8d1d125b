package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type UserSessions struct {
	ID        bson.ObjectID `bson:"_id,omitempty" json:"_id"`
	UserId    bson.ObjectID `bson:"userId,omitempty" json:"userId"`
	Token     string        `bson:"token,omitempty" json:"token"`
	ExpiresAt time.Time     `bson:"expiresAt,omitempty" json:"expiresAt"`
	CreatedAt time.Time     `bson:"createdAt,omitempty" json:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt,omitempty" json:"updatedAt"`
}
