package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// REMOVED ITEMS
//
//	permissions: {
//	    faxNotifications: Boolean,
//	    faxDeliveryConfirmations: Bo<PERSON><PERSON>,
//	    vmailNotifications: {
//	      text: <PERSON><PERSON><PERSON>,
//	      email: <PERSON><PERSON><PERSON>,
//	    },
//	    emailNotifications: Boolean,
//	    showCallRecording: Boolean,
//	    showCallLogs: Boolean,
//	    showFaxDownload: Boolean,
//	  },
//
// addresses: [
//
//	  {
//	    city: String,
//	    state: String,
//	    address: String,
//	    zip: String,
//	  },
//	],

type CompanyContactPerson struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

type CompanyLocation struct {
	City    string `json:"city"`
	State   string `json:"state"`
	Address string `json:"address"`
	Zip     string `json:"zip"`
}

type CompanyState string

const (
	CompanyDelete   CompanyState = "deleted"
	CompanyDisabled CompanyState = "disabled"
	CompanyEnabled  CompanyState = "enabled"
)

type Company struct {
	ID             bson.ObjectID `json:"_id,omitempty"`
	EpikCustomerId string        `json:"epikCustomerId"`
	CompanyState   CompanyState  `json:"companyState"`

	Name string `json:"name"`
	Logo string `json:"logo"`

	ContactEmails []string             `json:"contactEmails"`
	ContactPerson CompanyContactPerson `json:"contactPerson"`
	Location      CompanyLocation      `json:"location"`

	//DB relations
	Enterprises []bson.ObjectID `json:"enterprises"` // Array of Enterprise IDs
	//meta
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
