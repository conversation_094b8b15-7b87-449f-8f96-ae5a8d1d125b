package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type UserType string

const (
	UserTypeUser  UserType = "user"
	UserTypeAdmin UserType = "admin"
)

type UserCompanyAccess struct {
	CompanyId bson.ObjectID `json:"companyId"`
}

type UserCompanyAccessPopulated struct {
	Company Company `json:"company"`
}

type UserEnterpriseAccess struct {
	EnterpriseId bson.ObjectID `json:"enterpriseId"`
}
type UserEnterpriseAccessPopulated struct {
	Enterprise Enterprise `json:"enterprise"`
}

type UserAccess struct {
	ID               bson.ObjectID                    `bson:"_id,omitempty" json:"_id,omitempty"`
	UserId           bson.ObjectID                    `json:"userId"`
	Type             UserType                         `json:"type"`
	PermissionGroups []bson.ObjectID                  `json:"permissionGroups"` // multiple permission groups
	AddonPermissions map[Permission]PermissionsSchema `json:"addonPermissions"`
	CompanyAccess    []UserCompanyAccess              `json:"companyAccess"`
	EnterpriseAccess []UserEnterpriseAccess           `json:"enterpriseAccess"`
	EpikEngineering  bool                             `json:"epikEngineering"`
	Manager          bool                             `json:"manager"`
	CreatedAt        time.Time                        `bson:"createdAt" json:"createdAt"`
	UpdatedAt        time.Time                        `bson:"updatedAt" json:"updatedAt"`
}

type UserAccessPopulated struct {
	UserAccess
	PermissionGroupsDoc  []PermissionGroup               `json:"permissionGroupsDocs"`
	CompanyAccessDocs    []UserCompanyAccessPopulated    `json:"companyAccessDocs"`
	EnterpriseAccessDocs []UserEnterpriseAccessPopulated `json:"enterpriseAccessDocs"`
}
