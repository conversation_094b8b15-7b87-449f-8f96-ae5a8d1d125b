package models

import "go.mongodb.org/mongo-driver/v2/bson"

type EnterpriseState string

const (
	EnterpriseDelete   EnterpriseState = "deleted"
	EnterpriseDisabled EnterpriseState = "disabled"
	EnterpriseEnabled  EnterpriseState = "enabled"
)

type Enterprise struct {
	ID              bson.ObjectID   `json:"_id,omitempty"`
	Name            string          `json:"name"`
	EnterpriseState EnterpriseState `json:"enterpriseState"`
	//meta
	CreatedAt bson.DateTime `json:"createdAt"`
	UpdatedAt bson.DateTime `json:"updatedAt"`
}
