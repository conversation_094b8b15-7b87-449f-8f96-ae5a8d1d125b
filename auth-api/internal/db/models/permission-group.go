package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type PermissionsSchema struct {
	Read  bool `bson:"read" json:"read"`
	Write bool `bson:"write" json:"write"`
}

type Permission string

const (
	PermissionPortingAccess              Permission = "portingAccessEnabled"
	PermissionDCMonitor                  Permission = "dataCenterMonitor"
	PermissionWarehouseQc                Permission = "warehouseQcAccess"
	PermissionEth3                       Permission = "eth3Access"
	PermissionPcap                       Permission = "pcapModulePermission"
	PermissionPortConfig                 Permission = "portConfigurationAccess"
	PermissionEngineeringLock            Permission = "engineeringLock"
	PermissionRegistrationOffSet         Permission = "registrationOffSet"
	PermissionDataCenter                 Permission = "dataCenter"
	PermissionPowerSave                  Permission = "powerSave"
	PermissionCancelIntegrationAccess    Permission = "cancelIntegrationAccess"
	PermissionRealTimeActivityEnabled    Permission = "realTimeActivityEnabled"
	PermissionNumberCarrierChangeAccess  Permission = "numberCarrierChangeAccess"
	PermissionNocWizardAccess            Permission = "nocWizardAccess"
	PermissionPhoneAccess                Permission = "phoneAccess"
	PermissionInternationalDialingAccess Permission = "internationalDialingAccess"
	PermissionRecDiagnostics             Permission = "recDiagnostics"
	PermissionOmitLeading                Permission = "omitLeading"
	PermissionOutBandManagement          Permission = "outBandManagement"
	PermissionCallWaiting                Permission = "callWaiting"
	PermissionDeleteEpi                  Permission = "deleteEpi"
	PermissionModifyEPIS                 Permission = "modifyEPIS"
	PermissionEnhancedRouting            Permission = "enhancedRouting"
	PermissionNumberBulkActionsAccess    Permission = "numberBulkActionsAccess"
	PermissionEpiUtilitiesPermission     Permission = "epiUtilitiesPermission"
	PermissionCallerIdMaskingAccess      Permission = "callerIdMaskingAccess"
	PermissionEnableMonitoring           Permission = "enableMonitoring"
	PermissionEventsAccess               Permission = "eventsAccess"
	PermissionEpikUpdates                Permission = "epikUpdates"
	PermissionAdminDashboardAccess       Permission = "adminDashboardAccess"
	PermissionEnhancedAnalyzer           Permission = "enhancedAnalyzer"
	PermissionTtyOptionsAccess           Permission = "ttyOptionsAccess"
	PermissionEnableAlarmProtocol        Permission = "enableAlarmProtocol"
	PermissionDocToolAccess              Permission = "docToolAccess"
	PermissionEpiFirmwareUpdate          Permission = "epiFirmwareUpdate"
	PermissionPmToolsAccess              Permission = "pmToolsAccess"
	PermissionTcToolsAccess              Permission = "tcToolsAccess"
	// adding new need to be add in permission service "internal/services/permissions.go" func name "ListPermissionOptions"
)

type PermissionGroup struct {
	ID          bson.ObjectID                    `bson:"_id,omitempty"`
	Title       string                           `bson:"title"`
	Color       string                           `bson:"color"`
	Permissions map[Permission]PermissionsSchema `bson:"permissions"`
	CreatedAt   time.Time                        `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time                        `bson:"updatedAt" json:"updatedAt"`
}
