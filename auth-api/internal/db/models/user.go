package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// t'ype VmailNotifications struct {
// 	Text  bool `bson:"text" json:"text"`
// 	Email bool `bson:"email" json:"email"`
// }'

// type Permissions string

// const (
// 	WarningNone           WarningStatus = "none"
// 	WarningInactivity     WarningStatus = "inactivity_warning"
// 	WarningFinalReminder  WarningStatus = "final_reminder"
// 	WarningDisabled       WarningStatus = "disabled"
// )

// var ValidWarningStatuses = map[WarningStatus]bool{
// 	WarningNone:          true,
// 	WarningInactivity:    true,
// 	WarningFinalReminder: true,
// 	WarningDisabled:      true,
// }

//REMOVED FIELDS FROM OLD USER TABLE

//dataCenterMonitor
//deviceMonitor
// NumberCarrierChageAccess
//permissions
// type Permissions struct {
// 	FaxNotifications   bool               `bson:"faxNotifications" json:"faxNotifications"`
// 	ShowCallRecording  bool               `bson:"showCallRecording" json:"showCallRecording"`
// 	VmailNotifications VmailNotifications `bson:"vmailNotifications" json:"vmailNotifications"`
// 	EmailNotifications bool               `bson:"emailNotifications" json:"emailNotifications"`
// 	ShowCallLogs       bool               `bson:"showCallLogs" json:"showCallLogs"`
// }
//IsReleaseNotesAdmin
//ReleaseNoteSeen
//PortingAccessEnabled
//IsAccountApprover
// RealTimeActivityEnabled
// TTYOptionsAccess
// WarehouseQcAccess
// NocWizardAccess
// WarehouseQcRecAccess
// Eth3Access
// PhoneAccess
// NumberBulkActionsAccess
//ModifyEPIS
// PcapModulePermission
// PortConfigurationAccess
// InternationalDialingAccess
// DocToolAccess
// CancelIntegrationAccess
// TCToolsAccess
// PMToolsAccess
// NocLogsAccess
// EpiUtilitiesPermission
// RecDiagnostics
// EpikEngineering
// DeleteEpi
// EnableMonitoring
// RegistrationOffset
// OmitLeading
// DataCenter
// PowerSave
// OutBandManagement
// CallWaiting
// EngineeringLock
// CallerIDMaskingAccess
// EventsAccess
// AdminDashboardAccess
// EpikUpdates
// EnhancedAnalyzer
// IsSecurityAcknowledged
// EnableAlarmProtocol
// EpiFirmwareUpdate

type User struct {
	ID                    bson.ObjectID `bson:"_id,omitempty" json:"_id"`
	Company               bson.ObjectID `bson:"company" json:"company"`
	Name                  string        `bson:"name" json:"name"`
	Email                 string        `bson:"email" json:"email"`
	PhoneNumber           string        `bson:"phoneNumber" json:"phoneNumber"`
	RegisterDate          time.Time     `bson:"registerDate" json:"registerDate"`
	Enabled               bool          `bson:"enabled" json:"enabled"`
	WarningStatus         string        `bson:"warningStatus" json:"-"`
	Password              string        `bson:"password" json:"-"`
	SmsCode               string        `bson:"smsCode" json:"-"`
	SmsRetries            int           `bson:"smsRetries" json:"-"`
	EmailCode             string        `bson:"emailCode" json:"-"`
	EmailRetries          int           `bson:"emailRetries" json:"-"`
	Deleted               bool          `bson:"deleted" json:"-"`
	ProfilePic            string        `bson:"profilePic" json:"profilePic"`
	ResetPassword         bool          `bson:"resetPassword" json:"-"`
	LockReminder          bool          `bson:"lockReminder" json:"lockReminder"`
	PreviousPasswords     []string      `bson:"previousPasswords" json:"-"`
	LastLoggedIn          time.Time     `bson:"lastLoggedIn" json:"-"`
	MaintenanceNoteSeen   bool          `bson:"maintenanceNoteSeen" json:"maintenanceNoteSeen"`
	PasswordLastUpdated   time.Time     `bson:"passwordLastUpdated" json:"passwordLastUpdated"`
	PasswordExpiryInDays  int           `bson:"passwordExpiryInDays" json:"-"`
	WarnedAboutInactivity bool          `bson:"warnedAboutInactivity" json:"-"`
	DeleteLock            bool          `bson:"deleteLock" json:"deleteLock"`
	TimeZone              string        `bson:"timeZone" json:"timeZone"`
	Default2FA            string        `bson:"default2fa" json:"default2fa"`
	TwoFactor             bool          `bson:"twoFactor" json:"twoFactor"`
	TwoFactorKey          string        `bson:"twoFactorKey" json:"-"`
	AccountLocked         bool          `bson:"accountLocked" json:"-"`
	// PermissionsGroup      bson.ObjectID                    `bson:"permissionsGroup" json:"permissionsGroup"`
	// ExtraPermissions      map[Permission]PermissionsSchema `bson:"extraPermissions" json:"extraPermissions"`
	UpdatedAt time.Time `bson:"updatedAt" json:"updatedAt"`
}
