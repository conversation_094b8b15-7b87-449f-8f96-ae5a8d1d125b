package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.72

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"auth-api/internal/graph/generated"
	"auth-api/internal/graph/model"
	"context"
)

// Permissions is the resolver for the permissions field.
func (r *permissionGroupResolver) Permissions(ctx context.Context, obj *models.PermissionGroup) ([]*model.PermissionEntry, error) {
	var res []*model.PermissionEntry
	for key, value := range obj.Permissions {
		res = append(res, &model.PermissionEntry{Permission: string(key), Schema: &value})
	}
	return res, nil
}

// ListUserPermission is the resolver for the listUserPermission field.
func (r *queryResolver) ListUserPermission(ctx context.Context) (*model.UserPermissionData, error) {
	usr := middleware.GetUserClaimFromContext(ctx)
	uiSchema, menuItems := r.Service.Permissions.ListUserPermission(&usr.UserID)
	return &model.UserPermissionData{UISchema: *uiSchema, MenuItems: menuItems}, nil
}

// ListPermissionGroups is the resolver for the ListPermissionGroups field.
func (r *queryResolver) ListPermissionGroups(ctx context.Context) (*transport.ListPermissionGroupsResponse, error) {
	usr := middleware.GetUserClaimFromContext(ctx)
	r.Service.Permissions.ValidateUserPermission(usr.UserID, transport.PermissionGroupFeatureList, transport.ValidateRead)
	docs := r.Service.Permissions.ListPermissionGroups()
	res := &transport.ListPermissionGroupsResponse{
		Docs:    docs,
		Options: r.Service.Permissions.ListPermissionOptions(),
	}
	return res, nil
}

// Resources is the resolver for the resources field.
func (r *uISchemaResolver) Resources(ctx context.Context, obj transport.UISchema) ([]*model.UISchemaResource, error) {
	var res []*model.UISchemaResource
	for resource, feature := range obj {
		res = append(res, &model.UISchemaResource{ResourceKey: resource, Features: feature})
	}
	return res, nil
}

// PermissionGroup returns generated.PermissionGroupResolver implementation.
func (r *Resolver) PermissionGroup() generated.PermissionGroupResolver {
	return &permissionGroupResolver{r}
}

// UISchema returns generated.UISchemaResolver implementation.
func (r *Resolver) UISchema() generated.UISchemaResolver { return &uISchemaResolver{r} }

type permissionGroupResolver struct{ *Resolver }
type uISchemaResolver struct{ *Resolver }
