package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.72

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"context"
)

// ListCompanies is the resolver for the ListCompanies field.
func (r *queryResolver) ListCompanies(ctx context.Context, input transport.ListCompanyInput) (*transport.PaginatedResponse[models.Company], error) {
	usr := middleware.GetUserClaimFromContext(ctx)
	r.Service.Permissions.ValidateUserPermission(usr.UserID, transport.CompanyFeatureList, transport.ValidateRead)
	res := r.Service.Company.ListCompanies(usr.UserID, &input)
	return res, nil
}
