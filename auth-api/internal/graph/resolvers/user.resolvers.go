package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.72

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"auth-api/internal/graph/generated"
	"context"
)

// ListUsers is the resolver for the ListUsers field.
func (r *queryResolver) ListUsers(ctx context.Context, input transport.ListUserInput) (*transport.PaginatedResponse[models.User], error) {
	usr := middleware.GetUserClaimFromContext(ctx)
	r.Service.Permissions.ValidateUserPermission(usr.UserID, transport.UserFeatureListUser, transport.ValidateRead)
	list := r.Service.User.ListUsers(usr.UserID, &input)
	return list, nil
}

// CompanyDoc is the resolver for the companyDoc field.
func (r *userResolver) CompanyDoc(ctx context.Context, obj *models.User) (*models.Company, error) {
	id := obj.Company
	if !id.IsZero() {
		r.Service.Log.Info(id)
		return r.Locals.Repo.Company.FindByID(id.Hex()), nil
	}
	return nil, nil
}

// User returns generated.UserResolver implementation.
func (r *Resolver) User() generated.UserResolver { return &userResolver{r} }

type userResolver struct{ *Resolver }
