package scalars

import (
	"auth-api/internal/common/_err"
	"fmt"
	"io"
	"time"
)

// DateTime implements a custom scalar for MongoDB DateTime
type DateTime time.Time

// MarshalGQL implements the graphql.Marshaler interface for DateTime
func (dt DateTime) MarshalGQL(w io.Writer) {
	t := time.Time(dt)
	w.Write([]byte(fmt.Sprintf(`"%s"`, t.Format(time.RFC3339))))
}

// UnmarshalGQL implements the graphql.Unmarshaler interface for DateTime
func (dt *DateTime) UnmarshalGQL(v interface{}) error {
	switch v := v.(type) {
	case string:
		t, err := time.Parse(time.RFC3339, v)
		if err != nil {
			// Try additional date formats if RFC3339 fails
			t, err = time.Parse("2006-01-02T15:04:05Z", v)
			if err != nil {
				t, err = time.Parse("2006-01-02", v)
				if err != nil {
					return _err.New(_err.ErrBadRequest, "invalid datetime format")
				}
			}
		}
		*dt = DateTime(t)
		return nil
	case time.Time:
		*dt = DateTime(v)
		return nil
	case int:
		*dt = DateTime(time.Unix(int64(v), 0))
		return nil
	case int64:
		*dt = DateTime(time.Unix(v, 0))
		return nil
	case float64:
		*dt = DateTime(time.Unix(int64(v), 0))
		return nil
	default:
		return fmt.Errorf("cannot convert %T to DateTime", v)
	}
}

// Add these methods to the model package

// ToTime converts DateTime to time.Time
func (dt DateTime) ToTime() time.Time {
	return time.Time(dt)
}
