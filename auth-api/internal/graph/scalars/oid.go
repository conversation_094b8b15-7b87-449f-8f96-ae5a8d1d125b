package scalars

import (
	"fmt"
	"io"

	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/v2/bson"
)

// Marshal ObjectID to string
func MarshalOID(id bson.ObjectID) graphql.Marshaler {
	return graphql.WriterFunc(func(w io.Writer) {
		io.WriteString(w, fmt.Sprintf("%q", id.Hex()))
	})
}

// Unmarshal string to ObjectID
func UnmarshalOID(v interface{}) (bson.ObjectID, error) {
	str, ok := v.(string)
	if !ok {
		return bson.NilObjectID, fmt.Errorf("ObjectID must be a string")
	}
	return bson.ObjectIDFromHex(str)
}
