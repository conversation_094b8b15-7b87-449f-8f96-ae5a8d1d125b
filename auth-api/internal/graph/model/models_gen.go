// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
)

type PermissionEntry struct {
	Permission string                    `json:"permission"`
	Schema     *models.PermissionsSchema `json:"schema"`
}

type Query struct {
}

type UISchemaFeature struct {
	Read        bool                `json:"read"`
	Write       bool                `json:"write"`
	ResourceKey transport.Resources `json:"resourceKey"`
}

type UISchemaResource struct {
	ResourceKey transport.Resources        `json:"resourceKey"`
	Features    []*transport.ResourceEntry `json:"features"`
}

type UserPermissionData struct {
	UISchema  transport.UISchema     `json:"uiSchema"`
	MenuItems []*transport.MenuItems `json:"menuItems"`
}
