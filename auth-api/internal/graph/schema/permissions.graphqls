
# Enum for Resources type
enum Resources {
  USER
  PERMISSION_GROUP
  USER_APPROVAL
}

# Enum for Features type
enum Features {
  USER_LIST_USER
  USER_ADD
  USER_UPDATE
  USER_SOFT_DELETE
  USER_PERMISSION_GROUP
  USER_USER_APPROVAL
  PERMISSION_GROUP_LIST
  USER_APPROVAL_LIST
}

# Type for FeatureMeta
type FeatureMeta {
  read: Boolean!
  write: Boolean!
  resourceKey: Resources!
}

# Type for MenuItems with recursive structure
type MenuItem {
  title: String!
  resourceKey: Resources!
  childs: [MenuItem]
}

# Type for UISchema
type UISchemaFeature {
  read: Boolean!
  write: Boolean!
  resourceKey: Resources!
}

# We need to restructure UISchema for GraphQL
# Since it's originally a map[Resources]map[Features]*FeatureMeta
type UISchema {
  resources: [UISchemaResource!]!
}

type UISchemaResource {
  resourceKey: Resources!
  features: [ResourceEntry!]!
}

type ResourceEntry {
  feature: Features!
  meta: FeatureMeta!
}


type PermissionsSchema {
  read: Boolean!
  write: Boolean!
}


type PermissionEntry {
  permission: String!
  schema: PermissionsSchema!
}

type PermissionGroup {
  _id: OID
  title: String!
  color: String!
  permissions: [PermissionEntry!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type PermissionOptions {
  label: String!
  value: String!
}

type ListPermissionGroupsResponse {
  docs: [PermissionGroup!]!
  options: [PermissionOptions!]!
}

# Simple Query type with only the needed function
extend type Query {
  ListUserPermission: UserPermissionData
  ListPermissionGroups: ListPermissionGroupsResponse
}

# Type to hold the combined return values of ListUserPermission
type UserPermissionData {
  uiSchema: UISchema!
  menuItems: [MenuItem!]!
}
