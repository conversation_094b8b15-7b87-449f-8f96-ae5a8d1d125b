type User {
  _id: OID
  company: OID
  companyDoc: Company
  name: String
  email: String
  phoneNumber: String
  registerDate: DateTime
  enabled: Boolean
  profilePic: String
  resetPassword: Boolean
  lockReminder: Boolean
  maintenanceNoteSeen: Boolean
  timeZone: String
  default2fa: String
  twoFactor: Boolean
  updatedAt: DateTime
}

type ListUserResponse {
  pagination: PaginationResponse
  docs: [User!]!
}

extend type Query {
  ListUsers(input: ListUserInput!): ListUserResponse!
}

input ListUserInput {
  pagination: PaginationInput!
  query: String
  company: String
  email: String
  name: String
  role: String
}
