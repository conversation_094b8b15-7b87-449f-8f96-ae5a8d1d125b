// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"auth-api/internal/graph/model"
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/v2/bson"
)

// region    ************************** generated!.gotpl **************************

type PermissionGroupResolver interface {
	Permissions(ctx context.Context, obj *models.PermissionGroup) ([]*model.PermissionEntry, error)
}
type UISchemaResolver interface {
	Resources(ctx context.Context, obj transport.UISchema) ([]*model.UISchemaResource, error)
}

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _FeatureMeta_read(ctx context.Context, field graphql.CollectedField, obj *transport.FeatureMeta) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeatureMeta_read(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Read, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeatureMeta_read(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeatureMeta",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeatureMeta_write(ctx context.Context, field graphql.CollectedField, obj *transport.FeatureMeta) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeatureMeta_write(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Write, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeatureMeta_write(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeatureMeta",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeatureMeta_resourceKey(ctx context.Context, field graphql.CollectedField, obj *transport.FeatureMeta) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeatureMeta_resourceKey(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResourceKey, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.Resources)
	fc.Result = res
	return ec.marshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeatureMeta_resourceKey(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeatureMeta",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Resources does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ListPermissionGroupsResponse_docs(ctx context.Context, field graphql.CollectedField, obj *transport.ListPermissionGroupsResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ListPermissionGroupsResponse_docs(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Docs, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*[]models.PermissionGroup)
	fc.Result = res
	return ec.marshalNPermissionGroup2ᚖᚕauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroupᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ListPermissionGroupsResponse_docs(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ListPermissionGroupsResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_PermissionGroup__id(ctx, field)
			case "title":
				return ec.fieldContext_PermissionGroup_title(ctx, field)
			case "color":
				return ec.fieldContext_PermissionGroup_color(ctx, field)
			case "permissions":
				return ec.fieldContext_PermissionGroup_permissions(ctx, field)
			case "createdAt":
				return ec.fieldContext_PermissionGroup_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_PermissionGroup_updatedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PermissionGroup", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ListPermissionGroupsResponse_options(ctx context.Context, field graphql.CollectedField, obj *transport.ListPermissionGroupsResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ListPermissionGroupsResponse_options(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Options, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*[]transport.PermissionOptions)
	fc.Result = res
	return ec.marshalNPermissionOptions2ᚖᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptionsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ListPermissionGroupsResponse_options(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ListPermissionGroupsResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "label":
				return ec.fieldContext_PermissionOptions_label(ctx, field)
			case "value":
				return ec.fieldContext_PermissionOptions_value(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PermissionOptions", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MenuItem_title(ctx context.Context, field graphql.CollectedField, obj *transport.MenuItems) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MenuItem_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MenuItem_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MenuItem",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MenuItem_resourceKey(ctx context.Context, field graphql.CollectedField, obj *transport.MenuItems) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MenuItem_resourceKey(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResourceKey, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.Resources)
	fc.Result = res
	return ec.marshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MenuItem_resourceKey(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MenuItem",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Resources does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MenuItem_childs(ctx context.Context, field graphql.CollectedField, obj *transport.MenuItems) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MenuItem_childs(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Childs, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]transport.MenuItems)
	fc.Result = res
	return ec.marshalOMenuItem2ᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MenuItem_childs(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MenuItem",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "title":
				return ec.fieldContext_MenuItem_title(ctx, field)
			case "resourceKey":
				return ec.fieldContext_MenuItem_resourceKey(ctx, field)
			case "childs":
				return ec.fieldContext_MenuItem_childs(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MenuItem", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionEntry_permission(ctx context.Context, field graphql.CollectedField, obj *model.PermissionEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionEntry_permission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Permission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionEntry_permission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionEntry_schema(ctx context.Context, field graphql.CollectedField, obj *model.PermissionEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionEntry_schema(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Schema, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.PermissionsSchema)
	fc.Result = res
	return ec.marshalNPermissionsSchema2ᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionsSchema(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionEntry_schema(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "read":
				return ec.fieldContext_PermissionsSchema_read(ctx, field)
			case "write":
				return ec.fieldContext_PermissionsSchema_write(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PermissionsSchema", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup__id(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bson.ObjectID)
	fc.Result = res
	return ec.marshalOOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋv2ᚋbsonᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type OID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup_title(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup_color(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup_color(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Color, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup_color(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup_permissions(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup_permissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.PermissionGroup().Permissions(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.PermissionEntry)
	fc.Result = res
	return ec.marshalNPermissionEntry2ᚕᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐPermissionEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup_permissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "permission":
				return ec.fieldContext_PermissionEntry_permission(ctx, field)
			case "schema":
				return ec.fieldContext_PermissionEntry_schema(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PermissionEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNDateTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type DateTime does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionGroup_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.PermissionGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionGroup_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNDateTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionGroup_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type DateTime does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionOptions_label(ctx context.Context, field graphql.CollectedField, obj *transport.PermissionOptions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionOptions_label(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Label, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionOptions_label(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionOptions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionOptions_value(ctx context.Context, field graphql.CollectedField, obj *transport.PermissionOptions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionOptions_value(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Value, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionOptions_value(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionOptions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionsSchema_read(ctx context.Context, field graphql.CollectedField, obj *models.PermissionsSchema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionsSchema_read(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Read, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionsSchema_read(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionsSchema",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PermissionsSchema_write(ctx context.Context, field graphql.CollectedField, obj *models.PermissionsSchema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PermissionsSchema_write(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Write, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PermissionsSchema_write(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PermissionsSchema",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ResourceEntry_feature(ctx context.Context, field graphql.CollectedField, obj *transport.ResourceEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ResourceEntry_feature(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Feature, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.Features)
	fc.Result = res
	return ec.marshalNFeatures2authᚑapiᚋinternalᚋcommonᚋtransportᚐFeatures(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ResourceEntry_feature(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ResourceEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Features does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ResourceEntry_meta(ctx context.Context, field graphql.CollectedField, obj *transport.ResourceEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ResourceEntry_meta(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Meta, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.FeatureMeta)
	fc.Result = res
	return ec.marshalNFeatureMeta2authᚑapiᚋinternalᚋcommonᚋtransportᚐFeatureMeta(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ResourceEntry_meta(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ResourceEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "read":
				return ec.fieldContext_FeatureMeta_read(ctx, field)
			case "write":
				return ec.fieldContext_FeatureMeta_write(ctx, field)
			case "resourceKey":
				return ec.fieldContext_FeatureMeta_resourceKey(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FeatureMeta", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchema_resources(ctx context.Context, field graphql.CollectedField, obj transport.UISchema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchema_resources(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.UISchema().Resources(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.UISchemaResource)
	fc.Result = res
	return ec.marshalNUISchemaResource2ᚕᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐUISchemaResourceᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchema_resources(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchema",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "resourceKey":
				return ec.fieldContext_UISchemaResource_resourceKey(ctx, field)
			case "features":
				return ec.fieldContext_UISchemaResource_features(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UISchemaResource", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchemaFeature_read(ctx context.Context, field graphql.CollectedField, obj *model.UISchemaFeature) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchemaFeature_read(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Read, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchemaFeature_read(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchemaFeature",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchemaFeature_write(ctx context.Context, field graphql.CollectedField, obj *model.UISchemaFeature) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchemaFeature_write(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Write, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchemaFeature_write(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchemaFeature",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchemaFeature_resourceKey(ctx context.Context, field graphql.CollectedField, obj *model.UISchemaFeature) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchemaFeature_resourceKey(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResourceKey, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.Resources)
	fc.Result = res
	return ec.marshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchemaFeature_resourceKey(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchemaFeature",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Resources does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchemaResource_resourceKey(ctx context.Context, field graphql.CollectedField, obj *model.UISchemaResource) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchemaResource_resourceKey(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResourceKey, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.Resources)
	fc.Result = res
	return ec.marshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchemaResource_resourceKey(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchemaResource",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Resources does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UISchemaResource_features(ctx context.Context, field graphql.CollectedField, obj *model.UISchemaResource) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UISchemaResource_features(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Features, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*transport.ResourceEntry)
	fc.Result = res
	return ec.marshalNResourceEntry2ᚕᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐResourceEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UISchemaResource_features(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UISchemaResource",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "feature":
				return ec.fieldContext_ResourceEntry_feature(ctx, field)
			case "meta":
				return ec.fieldContext_ResourceEntry_meta(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ResourceEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPermissionData_uiSchema(ctx context.Context, field graphql.CollectedField, obj *model.UserPermissionData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPermissionData_uiSchema(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UISchema, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(transport.UISchema)
	fc.Result = res
	return ec.marshalNUISchema2authᚑapiᚋinternalᚋcommonᚋtransportᚐUISchema(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPermissionData_uiSchema(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPermissionData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "resources":
				return ec.fieldContext_UISchema_resources(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UISchema", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPermissionData_menuItems(ctx context.Context, field graphql.CollectedField, obj *model.UserPermissionData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPermissionData_menuItems(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MenuItems, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*transport.MenuItems)
	fc.Result = res
	return ec.marshalNMenuItem2ᚕᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItemsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPermissionData_menuItems(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPermissionData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "title":
				return ec.fieldContext_MenuItem_title(ctx, field)
			case "resourceKey":
				return ec.fieldContext_MenuItem_resourceKey(ctx, field)
			case "childs":
				return ec.fieldContext_MenuItem_childs(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MenuItem", field.Name)
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var featureMetaImplementors = []string{"FeatureMeta"}

func (ec *executionContext) _FeatureMeta(ctx context.Context, sel ast.SelectionSet, obj *transport.FeatureMeta) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, featureMetaImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FeatureMeta")
		case "read":
			out.Values[i] = ec._FeatureMeta_read(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "write":
			out.Values[i] = ec._FeatureMeta_write(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "resourceKey":
			out.Values[i] = ec._FeatureMeta_resourceKey(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var listPermissionGroupsResponseImplementors = []string{"ListPermissionGroupsResponse"}

func (ec *executionContext) _ListPermissionGroupsResponse(ctx context.Context, sel ast.SelectionSet, obj *transport.ListPermissionGroupsResponse) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, listPermissionGroupsResponseImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ListPermissionGroupsResponse")
		case "docs":
			out.Values[i] = ec._ListPermissionGroupsResponse_docs(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "options":
			out.Values[i] = ec._ListPermissionGroupsResponse_options(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var menuItemImplementors = []string{"MenuItem"}

func (ec *executionContext) _MenuItem(ctx context.Context, sel ast.SelectionSet, obj *transport.MenuItems) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, menuItemImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MenuItem")
		case "title":
			out.Values[i] = ec._MenuItem_title(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "resourceKey":
			out.Values[i] = ec._MenuItem_resourceKey(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "childs":
			out.Values[i] = ec._MenuItem_childs(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var permissionEntryImplementors = []string{"PermissionEntry"}

func (ec *executionContext) _PermissionEntry(ctx context.Context, sel ast.SelectionSet, obj *model.PermissionEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, permissionEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PermissionEntry")
		case "permission":
			out.Values[i] = ec._PermissionEntry_permission(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "schema":
			out.Values[i] = ec._PermissionEntry_schema(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var permissionGroupImplementors = []string{"PermissionGroup"}

func (ec *executionContext) _PermissionGroup(ctx context.Context, sel ast.SelectionSet, obj *models.PermissionGroup) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, permissionGroupImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PermissionGroup")
		case "_id":
			out.Values[i] = ec._PermissionGroup__id(ctx, field, obj)
		case "title":
			out.Values[i] = ec._PermissionGroup_title(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "color":
			out.Values[i] = ec._PermissionGroup_color(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "permissions":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._PermissionGroup_permissions(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "createdAt":
			out.Values[i] = ec._PermissionGroup_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "updatedAt":
			out.Values[i] = ec._PermissionGroup_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var permissionOptionsImplementors = []string{"PermissionOptions"}

func (ec *executionContext) _PermissionOptions(ctx context.Context, sel ast.SelectionSet, obj *transport.PermissionOptions) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, permissionOptionsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PermissionOptions")
		case "label":
			out.Values[i] = ec._PermissionOptions_label(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "value":
			out.Values[i] = ec._PermissionOptions_value(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var permissionsSchemaImplementors = []string{"PermissionsSchema"}

func (ec *executionContext) _PermissionsSchema(ctx context.Context, sel ast.SelectionSet, obj *models.PermissionsSchema) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, permissionsSchemaImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PermissionsSchema")
		case "read":
			out.Values[i] = ec._PermissionsSchema_read(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "write":
			out.Values[i] = ec._PermissionsSchema_write(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var resourceEntryImplementors = []string{"ResourceEntry"}

func (ec *executionContext) _ResourceEntry(ctx context.Context, sel ast.SelectionSet, obj *transport.ResourceEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, resourceEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ResourceEntry")
		case "feature":
			out.Values[i] = ec._ResourceEntry_feature(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "meta":
			out.Values[i] = ec._ResourceEntry_meta(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var uISchemaImplementors = []string{"UISchema"}

func (ec *executionContext) _UISchema(ctx context.Context, sel ast.SelectionSet, obj transport.UISchema) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, uISchemaImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UISchema")
		case "resources":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._UISchema_resources(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var uISchemaFeatureImplementors = []string{"UISchemaFeature"}

func (ec *executionContext) _UISchemaFeature(ctx context.Context, sel ast.SelectionSet, obj *model.UISchemaFeature) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, uISchemaFeatureImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UISchemaFeature")
		case "read":
			out.Values[i] = ec._UISchemaFeature_read(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "write":
			out.Values[i] = ec._UISchemaFeature_write(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "resourceKey":
			out.Values[i] = ec._UISchemaFeature_resourceKey(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var uISchemaResourceImplementors = []string{"UISchemaResource"}

func (ec *executionContext) _UISchemaResource(ctx context.Context, sel ast.SelectionSet, obj *model.UISchemaResource) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, uISchemaResourceImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UISchemaResource")
		case "resourceKey":
			out.Values[i] = ec._UISchemaResource_resourceKey(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "features":
			out.Values[i] = ec._UISchemaResource_features(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPermissionDataImplementors = []string{"UserPermissionData"}

func (ec *executionContext) _UserPermissionData(ctx context.Context, sel ast.SelectionSet, obj *model.UserPermissionData) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPermissionDataImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPermissionData")
		case "uiSchema":
			out.Values[i] = ec._UserPermissionData_uiSchema(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "menuItems":
			out.Values[i] = ec._UserPermissionData_menuItems(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNFeatureMeta2authᚑapiᚋinternalᚋcommonᚋtransportᚐFeatureMeta(ctx context.Context, sel ast.SelectionSet, v transport.FeatureMeta) graphql.Marshaler {
	return ec._FeatureMeta(ctx, sel, &v)
}

func (ec *executionContext) unmarshalNFeatures2authᚑapiᚋinternalᚋcommonᚋtransportᚐFeatures(ctx context.Context, v any) (transport.Features, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := transport.Features(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNFeatures2authᚑapiᚋinternalᚋcommonᚋtransportᚐFeatures(ctx context.Context, sel ast.SelectionSet, v transport.Features) graphql.Marshaler {
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNMenuItem2ᚕᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItemsᚄ(ctx context.Context, sel ast.SelectionSet, v []*transport.MenuItems) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNMenuItem2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNMenuItem2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx context.Context, sel ast.SelectionSet, v *transport.MenuItems) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._MenuItem(ctx, sel, v)
}

func (ec *executionContext) marshalNPermissionEntry2ᚕᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐPermissionEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.PermissionEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPermissionEntry2ᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐPermissionEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPermissionEntry2ᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐPermissionEntry(ctx context.Context, sel ast.SelectionSet, v *model.PermissionEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PermissionEntry(ctx, sel, v)
}

func (ec *executionContext) marshalNPermissionGroup2authᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroup(ctx context.Context, sel ast.SelectionSet, v models.PermissionGroup) graphql.Marshaler {
	return ec._PermissionGroup(ctx, sel, &v)
}

func (ec *executionContext) marshalNPermissionGroup2ᚕauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroupᚄ(ctx context.Context, sel ast.SelectionSet, v []models.PermissionGroup) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPermissionGroup2authᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroup(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPermissionGroup2ᚖᚕauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroupᚄ(ctx context.Context, sel ast.SelectionSet, v *[]models.PermissionGroup) graphql.Marshaler {
	return ec.marshalNPermissionGroup2ᚕauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionGroupᚄ(ctx, sel, *v)
}

func (ec *executionContext) marshalNPermissionOptions2authᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptions(ctx context.Context, sel ast.SelectionSet, v transport.PermissionOptions) graphql.Marshaler {
	return ec._PermissionOptions(ctx, sel, &v)
}

func (ec *executionContext) marshalNPermissionOptions2ᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptionsᚄ(ctx context.Context, sel ast.SelectionSet, v []transport.PermissionOptions) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPermissionOptions2authᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptions(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPermissionOptions2ᚖᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptionsᚄ(ctx context.Context, sel ast.SelectionSet, v *[]transport.PermissionOptions) graphql.Marshaler {
	return ec.marshalNPermissionOptions2ᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐPermissionOptionsᚄ(ctx, sel, *v)
}

func (ec *executionContext) marshalNPermissionsSchema2ᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐPermissionsSchema(ctx context.Context, sel ast.SelectionSet, v *models.PermissionsSchema) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PermissionsSchema(ctx, sel, v)
}

func (ec *executionContext) marshalNResourceEntry2ᚕᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐResourceEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*transport.ResourceEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNResourceEntry2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐResourceEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNResourceEntry2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐResourceEntry(ctx context.Context, sel ast.SelectionSet, v *transport.ResourceEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ResourceEntry(ctx, sel, v)
}

func (ec *executionContext) unmarshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx context.Context, v any) (transport.Resources, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := transport.Resources(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNResources2authᚑapiᚋinternalᚋcommonᚋtransportᚐResources(ctx context.Context, sel ast.SelectionSet, v transport.Resources) graphql.Marshaler {
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNUISchema2authᚑapiᚋinternalᚋcommonᚋtransportᚐUISchema(ctx context.Context, sel ast.SelectionSet, v transport.UISchema) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UISchema(ctx, sel, v)
}

func (ec *executionContext) marshalNUISchemaResource2ᚕᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐUISchemaResourceᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.UISchemaResource) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUISchemaResource2ᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐUISchemaResource(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNUISchemaResource2ᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐUISchemaResource(ctx context.Context, sel ast.SelectionSet, v *model.UISchemaResource) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UISchemaResource(ctx, sel, v)
}

func (ec *executionContext) marshalOListPermissionGroupsResponse2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐListPermissionGroupsResponse(ctx context.Context, sel ast.SelectionSet, v *transport.ListPermissionGroupsResponse) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ListPermissionGroupsResponse(ctx, sel, v)
}

func (ec *executionContext) marshalOMenuItem2authᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx context.Context, sel ast.SelectionSet, v transport.MenuItems) graphql.Marshaler {
	return ec._MenuItem(ctx, sel, &v)
}

func (ec *executionContext) marshalOMenuItem2ᚕauthᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx context.Context, sel ast.SelectionSet, v []transport.MenuItems) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMenuItem2authᚑapiᚋinternalᚋcommonᚋtransportᚐMenuItems(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOUserPermissionData2ᚖauthᚑapiᚋinternalᚋgraphᚋmodelᚐUserPermissionData(ctx context.Context, sel ast.SelectionSet, v *model.UserPermissionData) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPermissionData(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
