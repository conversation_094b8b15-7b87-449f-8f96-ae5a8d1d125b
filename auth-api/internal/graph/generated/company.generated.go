// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/v2/bson"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _Company__id(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bson.ObjectID)
	fc.Result = res
	return ec.marshalNOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋv2ᚋbsonᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type OID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_epikCustomerId(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_epikCustomerId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EpikCustomerId, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_epikCustomerId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_companyState(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_companyState(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CompanyState, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.CompanyState)
	fc.Result = res
	return ec.marshalNCompanyState2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyState(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_companyState(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type CompanyState does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_name(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_logo(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_logo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Logo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_logo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_contactEmails(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_contactEmails(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ContactEmails, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalNString2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_contactEmails(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_contactPerson(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_contactPerson(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ContactPerson, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.CompanyContactPerson)
	fc.Result = res
	return ec.marshalNCompanyContactPerson2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyContactPerson(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_contactPerson(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_CompanyContactPerson_name(ctx, field)
			case "email":
				return ec.fieldContext_CompanyContactPerson_email(ctx, field)
			case "phone":
				return ec.fieldContext_CompanyContactPerson_phone(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type CompanyContactPerson", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_location(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_location(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Location, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.CompanyLocation)
	fc.Result = res
	return ec.marshalNCompanyLocation2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyLocation(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_location(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "city":
				return ec.fieldContext_CompanyLocation_city(ctx, field)
			case "state":
				return ec.fieldContext_CompanyLocation_state(ctx, field)
			case "address":
				return ec.fieldContext_CompanyLocation_address(ctx, field)
			case "zip":
				return ec.fieldContext_CompanyLocation_zip(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type CompanyLocation", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_enterprises(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_enterprises(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Enterprises, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]bson.ObjectID)
	fc.Result = res
	return ec.marshalNOID2ᚕgoᚗmongodbᚗorgᚋmongoᚑdriverᚋv2ᚋbsonᚐObjectIDᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_enterprises(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type OID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNDateTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type DateTime does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Company_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.Company) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Company_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNDateTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Company_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Company",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type DateTime does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyContactPerson_name(ctx context.Context, field graphql.CollectedField, obj *models.CompanyContactPerson) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyContactPerson_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyContactPerson_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyContactPerson",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyContactPerson_email(ctx context.Context, field graphql.CollectedField, obj *models.CompanyContactPerson) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyContactPerson_email(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Email, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyContactPerson_email(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyContactPerson",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyContactPerson_phone(ctx context.Context, field graphql.CollectedField, obj *models.CompanyContactPerson) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyContactPerson_phone(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Phone, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyContactPerson_phone(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyContactPerson",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyLocation_city(ctx context.Context, field graphql.CollectedField, obj *models.CompanyLocation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyLocation_city(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.City, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyLocation_city(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyLocation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyLocation_state(ctx context.Context, field graphql.CollectedField, obj *models.CompanyLocation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyLocation_state(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.State, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyLocation_state(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyLocation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyLocation_address(ctx context.Context, field graphql.CollectedField, obj *models.CompanyLocation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyLocation_address(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Address, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyLocation_address(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyLocation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CompanyLocation_zip(ctx context.Context, field graphql.CollectedField, obj *models.CompanyLocation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CompanyLocation_zip(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Zip, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CompanyLocation_zip(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CompanyLocation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ListCompanyResponse_pagination(ctx context.Context, field graphql.CollectedField, obj *transport.PaginatedResponse[models.Company]) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ListCompanyResponse_pagination(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Pagination, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(transport.PaginationResponseOptions)
	fc.Result = res
	return ec.marshalOPaginationResponse2authᚑapiᚋinternalᚋcommonᚋtransportᚐPaginationResponseOptions(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ListCompanyResponse_pagination(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ListCompanyResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "currentPage":
				return ec.fieldContext_PaginationResponse_currentPage(ctx, field)
			case "totalPages":
				return ec.fieldContext_PaginationResponse_totalPages(ctx, field)
			case "count":
				return ec.fieldContext_PaginationResponse_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PaginationResponse", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ListCompanyResponse_docs(ctx context.Context, field graphql.CollectedField, obj *transport.PaginatedResponse[models.Company]) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ListCompanyResponse_docs(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Docs, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Company)
	fc.Result = res
	return ec.marshalNCompany2ᚕᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ListCompanyResponse_docs(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ListCompanyResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Company__id(ctx, field)
			case "epikCustomerId":
				return ec.fieldContext_Company_epikCustomerId(ctx, field)
			case "companyState":
				return ec.fieldContext_Company_companyState(ctx, field)
			case "name":
				return ec.fieldContext_Company_name(ctx, field)
			case "logo":
				return ec.fieldContext_Company_logo(ctx, field)
			case "contactEmails":
				return ec.fieldContext_Company_contactEmails(ctx, field)
			case "contactPerson":
				return ec.fieldContext_Company_contactPerson(ctx, field)
			case "location":
				return ec.fieldContext_Company_location(ctx, field)
			case "enterprises":
				return ec.fieldContext_Company_enterprises(ctx, field)
			case "createdAt":
				return ec.fieldContext_Company_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_Company_updatedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Company", field.Name)
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputListCompanyInput(ctx context.Context, obj any) (transport.ListCompanyInput, error) {
	var it transport.ListCompanyInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"pagination", "query", "epikCustomerId", "name"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "pagination":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
			data, err := ec.unmarshalNPaginationInput2authᚑapiᚋinternalᚋcommonᚋtransportᚐPaginatedInputOptions(ctx, v)
			if err != nil {
				return it, err
			}
			it.Pagination = data
		case "query":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("query"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Query = data
		case "epikCustomerId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("epikCustomerId"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.EpikCustomerId = data
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var companyImplementors = []string{"Company"}

func (ec *executionContext) _Company(ctx context.Context, sel ast.SelectionSet, obj *models.Company) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, companyImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Company")
		case "_id":
			out.Values[i] = ec._Company__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "epikCustomerId":
			out.Values[i] = ec._Company_epikCustomerId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "companyState":
			out.Values[i] = ec._Company_companyState(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._Company_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "logo":
			out.Values[i] = ec._Company_logo(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "contactEmails":
			out.Values[i] = ec._Company_contactEmails(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "contactPerson":
			out.Values[i] = ec._Company_contactPerson(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "location":
			out.Values[i] = ec._Company_location(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "enterprises":
			out.Values[i] = ec._Company_enterprises(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._Company_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._Company_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var companyContactPersonImplementors = []string{"CompanyContactPerson"}

func (ec *executionContext) _CompanyContactPerson(ctx context.Context, sel ast.SelectionSet, obj *models.CompanyContactPerson) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, companyContactPersonImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CompanyContactPerson")
		case "name":
			out.Values[i] = ec._CompanyContactPerson_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "email":
			out.Values[i] = ec._CompanyContactPerson_email(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "phone":
			out.Values[i] = ec._CompanyContactPerson_phone(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var companyLocationImplementors = []string{"CompanyLocation"}

func (ec *executionContext) _CompanyLocation(ctx context.Context, sel ast.SelectionSet, obj *models.CompanyLocation) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, companyLocationImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CompanyLocation")
		case "city":
			out.Values[i] = ec._CompanyLocation_city(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "state":
			out.Values[i] = ec._CompanyLocation_state(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "address":
			out.Values[i] = ec._CompanyLocation_address(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "zip":
			out.Values[i] = ec._CompanyLocation_zip(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var listCompanyResponseImplementors = []string{"ListCompanyResponse"}

func (ec *executionContext) _ListCompanyResponse(ctx context.Context, sel ast.SelectionSet, obj *transport.PaginatedResponse[models.Company]) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, listCompanyResponseImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ListCompanyResponse")
		case "pagination":
			out.Values[i] = ec._ListCompanyResponse_pagination(ctx, field, obj)
		case "docs":
			out.Values[i] = ec._ListCompanyResponse_docs(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNCompany2ᚕᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.Company) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNCompany2ᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐCompany(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNCompany2ᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐCompany(ctx context.Context, sel ast.SelectionSet, v *models.Company) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Company(ctx, sel, v)
}

func (ec *executionContext) marshalNCompanyContactPerson2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyContactPerson(ctx context.Context, sel ast.SelectionSet, v models.CompanyContactPerson) graphql.Marshaler {
	return ec._CompanyContactPerson(ctx, sel, &v)
}

func (ec *executionContext) marshalNCompanyLocation2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyLocation(ctx context.Context, sel ast.SelectionSet, v models.CompanyLocation) graphql.Marshaler {
	return ec._CompanyLocation(ctx, sel, &v)
}

func (ec *executionContext) unmarshalNCompanyState2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyState(ctx context.Context, v any) (models.CompanyState, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.CompanyState(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNCompanyState2authᚑapiᚋinternalᚋdbᚋmodelsᚐCompanyState(ctx context.Context, sel ast.SelectionSet, v models.CompanyState) graphql.Marshaler {
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNListCompanyInput2authᚑapiᚋinternalᚋcommonᚋtransportᚐListCompanyInput(ctx context.Context, v any) (transport.ListCompanyInput, error) {
	res, err := ec.unmarshalInputListCompanyInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNListCompanyResponse2authᚑapiᚋinternalᚋcommonᚋtransportᚐPaginatedResponse(ctx context.Context, sel ast.SelectionSet, v transport.PaginatedResponse[models.Company]) graphql.Marshaler {
	return ec._ListCompanyResponse(ctx, sel, &v)
}

func (ec *executionContext) marshalNListCompanyResponse2ᚖauthᚑapiᚋinternalᚋcommonᚋtransportᚐPaginatedResponse(ctx context.Context, sel ast.SelectionSet, v *transport.PaginatedResponse[models.Company]) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ListCompanyResponse(ctx, sel, v)
}

func (ec *executionContext) marshalOCompany2ᚖauthᚑapiᚋinternalᚋdbᚋmodelsᚐCompany(ctx context.Context, sel ast.SelectionSet, v *models.Company) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Company(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
