package utils

import (
	"auth-api/internal/common/_err"
	"crypto/rand"
	"math/big"
)

// GenerateNumericCode creates a cryptographically secure random numeric code.
// If no length is provided, it defaults to 6 digits.
func GenerateNumericCode(length ...int) string {
	// Default length is 6
	codeLength := 6

	// If a custom length was provided, use it
	if len(length) > 0 && length[0] > 0 {
		codeLength = length[0]
	}

	const charset = "0123456789"
	result := make([]byte, codeLength)
	charsetLen := big.NewInt(int64(len(charset)))

	for i := range result {
		num := _err.Must(rand.Int(rand.Reader, charsetLen))
		result[i] = charset[num.Int64()]
	}

	return string(result)
}
func FirstOrDefault[T any](list []T, def T) T {
	if len(list) > 0 {
		return list[0]
	}
	return def
}
