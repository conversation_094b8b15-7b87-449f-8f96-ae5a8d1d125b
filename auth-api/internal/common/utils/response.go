package utils

import (
	"github.com/gofiber/fiber/v2"
)

type JSONResponse struct {
	Status string      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}

func firstOrDefault[T any](list []T, def *T) *T {
	if len(list) > 0 {
		return &list[0]
	}
	return def
}

func RespondJSON(ctx *fiber.Ctx, data interface{}, statusCode ...int) {
	defaultStatus := 200
	status := firstOrDefault(statusCode, &defaultStatus)

	ctx.Status(*status).JSON(JSONResponse{
		Status: "success",
		Data:   data,
	})
}
