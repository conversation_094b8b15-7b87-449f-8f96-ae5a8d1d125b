package transport

import (
	"auth-api/internal/db/models"
)

type Resources string
type Features string

type FeatureMeta struct {
	Read        bool      `json:"read"`
	Write       bool      `json:"write"`
	ResourceKey Resources `json:"resourceKey"`
}
type ResourceEntry struct {
	Feature Features    `json:"feature"`
	Meta    FeatureMeta `json:"meta"`
}

type UISchema map[Resources][]*ResourceEntry
type UISchemaMap map[Resources]map[Features]*FeatureMeta

type UserAccessAndPermissions struct {
	Permissions     map[models.Permission]models.PermissionsSchema `json:"permissions"`
	Companies       []string                                       `json:"companies"`
	AllCompanies    []string                                       `json:"allCompanies"`
	Enterprises     []string                                       `json:"enterprises"`
	Company         string                                         `json:"company"`
	EpikEngineering bool                                           `json:"epikEngineering"`
	Manager         bool                                           `json:"manager"`
}

type MenuItems struct {
	Title       string      `json:"title"`
	ResourceKey Resources   `json:"resourceKey"`
	Childs      []MenuItems `json:"childs,omitempty"`
}

const UserResource Resources = "USER"

const (
	UserFeatureListUser        Features = "USER_LIST"
	UserFeatureAdd             Features = "USER_ADD"
	UserFeatureUpdate          Features = "USER_UPDATE"
	UserFeatureSoftDelete      Features = "USER_SOFTDELETE"
	UserFeaturePermissionGroup Features = "USER_PERMISSIONGROUP"
	UserFeatureUserApproval    Features = "USER_USERAPPROVAL"
)

const PermissionGroupResource Resources = "PERMISSIONGROUP"

const (
	PermissionGroupFeatureList Features = "PERMISSIONGROUP_LIST"
)

const UserApprovalResource Resources = "USERAPPROVAL"

const (
	UserApprovalFeatureList Features = "USERAPPROVAL_LIST"
)

const CompanyResource Resources = "COMPANY"

const (
	CompanyFeatureList Features = "COMPANY_LIST"
	CompanyFeatureAdd  Features = "COMPANY_ADD"
)

const EdgeDevicesResource Resources = "EDGEDEVICES"

const (
	EdgeDevicesFeatureList Features = "EDGEDEVICES_LIST"
	EdgeDevicesFeatureAdd  Features = "EDGEDEVICES_ADD"
)

const (
	CallsResource   Resources = "CALLS"
	FAXResource     Resources = "FAX"
	NumbersResource Resources = "NUMBERS"

	NumberOrderResource   Resources = "NUMBERORDER"
	PortingResource       Resources = "PORTINGORDER"
	E911Resource          Resources = "E911"
	CarrierLookupResource Resources = "CARRIERLOOKUP"
)

type ValidateOperations int

const (
	ValidateRead  ValidateOperations = 1
	ValidateWrite ValidateOperations = 2
	ValidateBoth  ValidateOperations = 3
)

type PermissionOptions struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type ListPermissionGroupsResponse struct {
	Docs    *[]models.PermissionGroup `json:"docs"`
	Options *[]PermissionOptions      `json:"options"`
}
