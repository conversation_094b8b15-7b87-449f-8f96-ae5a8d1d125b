package transport

type PaginatedInputOptions struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"pageSize"`
}

type PaginationResponseOptions struct {
	CurrentPage int64 `json:"currentPage"`
	TotalPages  int64 `json:"totalPages"`
	Count       int64 `json:"count"`
}

type PaginatedResponse[T any] struct {
	Pagination PaginationResponseOptions `json:"pagination"`
	Docs       []*T                      `json:"docs"`
}
