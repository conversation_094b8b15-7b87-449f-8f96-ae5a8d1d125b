package transport

import (
	"auth-api/internal/db/models"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type ListCompanyInput struct {
	Pagination     PaginatedInputOptions `json:"pagination"`
	Query          *string               `json:"query"`
	EpikCustomerId *string               `json:"epikCustomerId"`
	Name           *string               `json:"name"`
}

type ListCompanyInputInternal struct {
	IsAllCompanies   bool
	Companies        *[]bson.ObjectID
	IsAllEnterprises bool
	Enterprises      *[]bson.ObjectID
}

type ListCompanyResponse = PaginatedResponse[models.Company]
