package transport

import (
	"auth-api/internal/db/models"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type ListUserInput struct {
	Pagination PaginatedInputOptions `json:"pagination"`
	Query      *string               `json:"query"`
	Company    *string               `json:"company"`
	Email      *string               `json:"email"`
	Name       *string               `json:"name"`
	Role       *string               `json:"role"`
}

type ListUserInputInternal struct {
	AllCompanies bool
	Companies    *[]bson.ObjectID
}

type ListUserResponse = PaginatedResponse[models.User]
