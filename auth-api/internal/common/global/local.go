package global

import (
	"auth-api/internal/config"
	"auth-api/internal/db/repository"
	"auth-api/internal/services"
	"context"
)

type Locals struct {
	Repo    *repository.Repositories
	Service *services.Services
	Config  *config.Config
}

func SetupLocals(ctx context.Context, config *config.Config) *Locals {
	repo := repository.SetupRepositories(ctx)
	return &Locals{
		Repo:    repo,
		Service: services.SetupServices(ctx, repo, config),
		Config:  config,
	}
}
