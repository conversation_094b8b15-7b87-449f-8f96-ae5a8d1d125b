package logger

import (
	"context"
	"os"

	"github.com/gofiber/fiber/v2/log"
	"github.com/sirupsen/logrus"
)

type Logger struct {
	*logrus.Logger
}

// method to satisfy gofiber logger
func (l *Logger) Debugw(_ string, _ ...interface{}) {
}
func (l *Logger) Errorw(_ string, _ ...interface{}) {
}
func (l *Logger) Fatalw(_ string, _ ...interface{}) {
}
func (l *Logger) Infow(_ string, _ ...interface{}) {
}
func (l *Logger) Panicw(_ string, _ ...interface{}) {
}
func (l *Logger) Tracew(_ string, _ ...interface{}) {
}
func (l *Logger) Warnw(_ string, _ ...interface{}) {
}
func (l *Logger) SetLevel(log.Level) {
}
func (l *Logger) WithContext(context.Context) log.CommonLogger {
	return l
}

//gofiber method ends

func NewLogger() *Logger {
	log := logrus.New()
	log.SetOutput(os.Stdout)
	log.SetLevel(logrus.InfoLevel)
	log.SetFormatter(&logrus.TextFormatter{
		ForceColors:               true,
		FullTimestamp:             true,
		TimestampFormat:           "2006-01-02 15:04:05",
		EnvironmentOverrideColors: true,
	})

	return &Logger{log}
}
