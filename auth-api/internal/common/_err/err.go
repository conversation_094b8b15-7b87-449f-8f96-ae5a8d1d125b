package _err

import (
	"fmt"
	"net/http"
)

type ErrorType string

const (
	ErrNotFound   ErrorType = "NOT_FOUND"
	ErrInternal   ErrorType = "INTERNAL"
	ErrBadRequest ErrorType = "BAD_REQUEST"
	ErrForbidden  ErrorType = "FORBIDDEN"
	Unauthorized  ErrorType = "INVALID_TOKEN_SIGNATURE"
)

type AppError struct {
	Code    ErrorType `json:"code"`
	Message string    `json:"message"`
	Status  int       `json:"status"`
}

var defaultErrorMeta = map[ErrorType]AppError{
	ErrNotFound:   {ErrNotFound, "Resource not found", http.StatusNotFound},
	ErrInternal:   {ErrInternal, "Internal server error", http.StatusInternalServerError},
	ErrBadRequest: {ErrBadRequest, "Invalid request", http.StatusBadRequest},
	ErrForbidden:  {ErrForbidden, "Forbidden", http.StatusForbidden},
	Unauthorized:  {Unauthorized, "Unauthorized", http.StatusUnauthorized},
}

func (e *AppError) Error() string {
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func ifEmpty(custom, fallback string) string {
	if custom != "" {
		return custom
	}
	return fallback
}

func New(code ErrorType, customMessage string) *AppError {
	meta := defaultErrorMeta[code]

	return &AppError{
		Code:    meta.Code,
		Message: ifEmpty(customMessage, meta.Message),
		Status:  meta.Status,
	}
}
func Must[T any](val T, err error) T {
	if err != nil {
		panic(err)
	}
	return val
}
