package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	Port         string
	GRPCPort     string
	MongoURI     string
	JWTSecretV1  string
	JWTSecret    string
	REDIS_PORT   string
	REDIS_SERVER string
	DEV_MODE     bool
}

var LoadedConfig Config

func Load() Config {
	_ = godotenv.Load()

	config := Config{
		Port:         getEnv("PORT", "8080"),
		GRPCPort:     getEnv("GRPCPort", "50051"),
		MongoURI:     getEnv("MONGO_DB_URI", "mongodb://localhost:27017/epikFax"),
		JWTSecret:    getEnv("SECRETV2", "supersecret"),
		JWTSecretV1:  getEnv("SECRET", "supersecret"),
		REDIS_PORT:   getEnv("REDIS_PORT", "6379"),
		REDIS_SERVER: getEnv("REDIS_SERVER", "localhost"),
		DEV_MODE:     getEnvBool("DEV_MODE", false),
	}

	// Validate critical configuration
	if !config.DEV_MODE {
		if config.JWTSecret == "supersecret" || config.JWTSecretV1 == "supersecret" {
			log.Fatal("⛔ Production environment detected but using default JWT secrets. Set SECRETV2 and SECRET env vars.")
		}

		if config.MongoURI == "mongodb://localhost:27017/epikFax" {
			log.Fatal("⛔ Production environment detected but using default MongoDB URI. Set MONGO_DB_URI env var.")
		}
	}

	LoadedConfig = config
	return config
}

func getEnvBool(key string, fallback bool) bool {
	if value := os.Getenv(key); value != "" && (value == "1" || value == "true" || value == "yes") {
		return true
	}
	log.Printf("⚠️  ENV %s not set, using default: %t", key, fallback)
	return fallback
}
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	log.Printf("⚠️  ENV %s not set, using default: %s", key, fallback)
	return fallback
}
