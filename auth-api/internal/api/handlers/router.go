package handlers

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/_err"
	"auth-api/internal/common/global"
	"auth-api/internal/common/utils"
	"auth-api/internal/config"
	"auth-api/internal/db"
	"auth-api/internal/graph/generated"
	graph "auth-api/internal/graph/resolvers"
	"context"
	"errors"
	"strings"
	"time"

	"auth-api/internal/common/logger"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	jwtware "github.com/gofiber/contrib/jwt"
	"github.com/gofiber/fiber/v2/middleware/adaptor"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/etag"
	"github.com/gofiber/fiber/v2/middleware/helmet"
	"github.com/gofiber/fiber/v2/middleware/limiter"
	"github.com/golang-jwt/jwt/v5"
	"github.com/vektah/gqlparser/v2/gqlerror"

	// "github.com/99designs/gqlgen/graphql/handler"
	// "github.com/99designs/gqlgen/graphql/playground"
	"github.com/gofiber/fiber/v2"
	fiberlog "github.com/gofiber/fiber/v2/log"
	logRequest "github.com/gofiber/fiber/v2/middleware/logger"
)

type Input struct {
	Query         string                 `query:"query"`
	OperationName string                 `query:"operationName"`
	Variables     map[string]interface{} `query:"variables"`
}

func SetupRoutes(cfg config.Config, locals *global.Locals) *fiber.App {
	baseLogger := logger.NewLogger()
	//creating fiber app with necessary middlewares
	app := fiber.New(fiber.Config{
		BodyLimit: 10 * 1024 * 1024, // 10MB
	})
	fiberlog.SetLogger(baseLogger)
	app.Use(logRequest.New(logRequest.Config{
		Format:     "${time} [${ip}]:${port}] ${locals:requestid} ${pid} ${status} - ${method} ${path}\n",
		TimeFormat: "02-Jan-2006 15:04:05",
	}))
	app.Use(cors.New(cors.Config{
		AllowCredentials: !locals.Config.DEV_MODE,
		AllowOrigins: func() string {
			if locals.Config.DEV_MODE {
				return "*"
			}
			return "https://*.epik.io"
		}(),
		AllowMethods:  "GET,POST,HEAD,PUT,DELETE,OPTIONS",
		AllowHeaders:  "Origin, Content-Type, Accept, Authorization, X-Request-ID",
		ExposeHeaders: "Content-Length, Content-Type",
		MaxAge:        86400, // 24 hours
	}))
	app.Use(func(c *fiber.Ctx) error {
		c.Set("X-Content-Type-Options", "nosniff")
		c.Set("X-Frame-Options", "DENY")
		c.Set("Referrer-Policy", "strict-origin-when-cross-origin")

		if !locals.Config.DEV_MODE {
			c.Set("Content-Security-Policy", "default-src 'self'")
			c.Set("X-Content-Type-Options", "nosniff")
			c.Set("X-Frame-Options", "DENY")
		}
		return c.Next()
	})
	app.Use(helmet.New())
	app.Use(compress.New(compress.Config{
		Level: compress.LevelBestSpeed,
	}))
	app.Use(etag.New())
	app.Use(limiter.New(limiter.Config{
		Max:        20,
		Expiration: time.Duration(10) * time.Second,
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.IP()
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(fiber.StatusTooManyRequests).JSON(fiber.Map{
				"error": "Too many requests",
			})
		},
	}))

	app.Use(middleware.RecoverMiddleware)

	//auth service base url

	api := app.Group("/apps/auth-api/")
	// api.Use("/*", func(c *fiber.Ctx) error {
	// 	baseLogger.Info("cook:", c.Cookies("v2-jwt"))
	// 	c.Next()
	// 	return nil
	// })
	api.Use("/", jwtware.New(jwtware.Config{
		ContextKey:  "token",
		TokenLookup: "cookie:v2-jwt",
		SigningKey:  jwtware.SigningKey{Key: []byte(locals.Config.JWTSecret), JWTAlg: jwtware.HS256},
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			if cfg.DEV_MODE && c.Path() == "/apps/auth-api/query" && c.JSON(c.Body()) == nil {
				c.Next()
				return nil
			}
			panic(_err.New(_err.ErrForbidden, ""))
		},
		SuccessHandler: func(c *fiber.Ctx) error {
			token := c.Locals("token").(*jwt.Token)
			claims, ok := token.Claims.(jwt.MapClaims)
			baseLogger.Info("token:", ok, claims)
			if !(ok && token.Valid) {
				panic(_err.New(_err.Unauthorized, ""))
			}

			var customClaims middleware.TokenUser
			customClaims.UserID = claims["id"].(string)
			customClaims.Email = claims["email"].(string)
			c.Locals("user", customClaims)
			c.Next()
			return nil
		},
		Filter: func(c *fiber.Ctx) bool {
			return strings.Contains(c.Request().String(), "apps/auth-api/user/token-validation-from-myepik")

		},
	}))
	// GRAPHQL API FOR QUERIES
	qlConfig := generated.Config{
		Resolvers: &graph.Resolver{locals},
	}
	srv := handler.New(generated.NewExecutableSchema(qlConfig))
	srv.SetErrorPresenter(func(ctx context.Context, e error) *gqlerror.Error {
		err := graphql.DefaultErrorPresenter(ctx, e)

		var myErr *_err.AppError
		if errors.As(e, &myErr) {
			err.Message = "Eeek!"
		}

		return err
	})

	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{})
	srv.AddTransport(transport.Websocket{
		KeepAlivePingInterval: 10 * time.Second,
	})
	srv.Use(extension.Introspection{})
	api.All("/query", adaptor.HTTPHandler(srv))
	playgroundHandler := playground.Handler("GraphQL Playground", "/apps/auth-api/query")
	api.All("/playground", adaptor.HTTPHandler(playgroundHandler))

	// REST API FOR MUTATIONS
	userRouter := api.Group("/user")
	UserRouter(userRouter, locals)

	companyRouter := api.Group("/company")
	UserRouter(companyRouter, locals)

	//404 and health check

	api.Get("/health", func(c *fiber.Ctx) error {
		status := "UP"
		checks := map[string]string{
			"api":      "UP",
			"database": "UP",
			"redis":    "UP",
		}

		// Check MongoDB connection
		if err := db.Client.Ping(c.Context(), nil); err != nil {
			checks["database"] = "DOWN"
			status = "DEGRADED"
		}

		// Check Redis connection
		db.Redis.Ping()

		statusCode := fiber.StatusOK
		if status != "UP" {
			statusCode = fiber.StatusServiceUnavailable
		}

		utils.RespondJSON(c, map[string]interface{}{
			"status": status,
			"checks": checks,
			"time":   time.Now().Format(time.RFC3339),
		}, statusCode)
		return nil
	})
	app.Use("/*", func(c *fiber.Ctx) error {
		utils.RespondJSON(c, map[string]interface{}{"notfound": true}, 404)
		return nil
	})

	return app
}
