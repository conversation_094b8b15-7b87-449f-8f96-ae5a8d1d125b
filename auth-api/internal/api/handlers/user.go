package handlers

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/global"
	"auth-api/internal/common/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

func UserRouter(router fiber.Router, locals *global.Locals) {
	// log := logger.NewLogger()
	router.Get("/token-validation-from-myepik", func(c *fiber.Ctx) error {
		token := middleware.ExtractBearerToken(c)
		newToken := locals.Service.User.LoginWithMyepikToken(&locals.Config.JWTSecretV1, &token)
		middleware.SetTokenCookie(c, *newToken, locals.Config.DEV_MODE)
		res := map[string]interface{}{}
		if locals.Config.DEV_MODE {
			res["token"] = newToken
		}
		utils.RespondJSON(c, res)
		return nil
	})
	router.Get("/me", func(c *fiber.Ctx) error {
		user := middleware.GetUserClaim(c)
		_user := locals.Service.User.GetById(&user.UserID)
		utils.RespondJSON(c, *_user)
		return nil
	})
	router.Get("/refresh-token", func(c *fiber.Ctx) error {
		token := c.Locals("token").(*jwt.Token)
		user := middleware.GetUserClaim(c)
		newToken := locals.Service.User.RefreshAccessToken(&user.UserID, token.Raw)
		middleware.SetTokenCookie(c, newToken, locals.Config.DEV_MODE)
		utils.RespondJSON(c, map[string]interface{}{})
		return nil
	})
	router.Get("/logout", func(c *fiber.Ctx) error {
		c.ClearCookie("v2-jwt")
		utils.RespondJSON(c, map[string]interface{}{})
		return nil
	})
}
