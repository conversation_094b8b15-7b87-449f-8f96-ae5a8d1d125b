package middleware

import (
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/common/utils"
	"auth-api/internal/config"
	"context"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

type Claims struct {
	UserID string `json:"id"`
	Email  string `json:"email"`
	jwt.RegisteredClaims
}

type TokenUser struct {
	UserID string `json:"id"`
	Email  string `json:"email"`
}

func SetTokenCookie(c *fiber.Ctx, token string, devMode bool) {
	c.<PERSON>ie(&fiber.Cookie{
		Name:     "v2-jwt",
		Value:    token,
		HTTPOnly: true,
		Secure:   !devMode, // Allow insecure cookie for HTTP in dev
		SameSite: "Lax",    // "Strict" blocks cross-subdomain in many cases
		Expires:  time.Now().Add(15 * time.Minute),
		Path:     "/apps/",
	})
}

func ExtractBearerToken(c *fiber.Ctx) string {
	// Try to get from Authorization header first
	bearerToken := c.Get("Authorization")
	if len(bearerToken) > 7 && bearerToken[:7] == "Bearer " {
		return bearerToken[7:]
	}

	panic(_err.New(_err.Unauthorized, ""))
}

func ExtractSessionToken(c *fiber.Ctx) string {
	token := c.Cookies("v2-jwt")
	if token != "" {
		return token
	}

	panic(_err.New(_err.Unauthorized, ""))
}

func ValidateToken(tokenString *string, secret ...string) *Claims {
	log := logger.NewLogger()
	secretToCompare := utils.FirstOrDefault(secret, config.LoadedConfig.JWTSecret)
	token, err := jwt.ParseWithClaims(*tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretToCompare), nil
	})

	if err != nil || !token.Valid {
		log.Error(err, token.Valid)
		panic(_err.New(_err.ErrForbidden, ""))
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		log.Error("Error while converting token to struct")
		panic(_err.New(_err.ErrForbidden, ""))
	}

	return claims
}

func GetUserClaim(c *fiber.Ctx) *TokenUser {
	claim := c.Locals("user").(TokenUser)
	return &claim
}
func GetUserClaimFromContext(c context.Context) *TokenUser {
	claim, ok := c.Value("user").(TokenUser)
	if !ok {
		panic(_err.New(_err.Unauthorized, ""))
	}
	return &claim
}
