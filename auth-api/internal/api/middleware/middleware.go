package middleware

import (
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"fmt"
	"net/http"
	"runtime/debug"

	"github.com/gofiber/fiber/v2"
)

func HandleServiceError() {
	if r := recover(); r != nil {
		switch err := r.(type) {
		case *_err.AppError:
			panic(err) // let it keep bubbling up to router
		case error:
			panic(_err.New(_err.ErrInternal, ""))
		default:
			panic(_err.New(_err.ErrInternal, "unexpected panic"))
		}
	}
}

func PrintPrettyStack(rec interface{}) {
	fmt.Printf("Panic: %v\n", rec)
	fmt.Printf("Stack trace:\n%s\n", debug.Stack())
}
func RecoverMiddleware(ctx *fiber.Ctx) error {
	log := logger.NewLogger()

	defer func() {
		if rec := recover(); rec != nil {
			// Skip abort panic
			if rec == http.ErrAbortHandler {
				panic(rec)
			}

			var appErr *_err.AppError

			// Classify error
			switch err := rec.(type) {
			case *_err.AppError:
				appErr = err
			case error:
				appErr = _err.New(_err.ErrInternal, err.Error())
			default:
				appErr = _err.New(_err.ErrInternal, "Unexpected error occurred")
			}
			PrintPrettyStack(rec)
			log.Error(appErr.Error())
			res := map[string]interface{}{
				"status":  appErr.Status,
				"data":    appErr,
				"message": appErr.Message,
			}
			ctx.Status(appErr.Status).JSON(res)
		}
	}()

	ctx.Next()
	return nil
}
