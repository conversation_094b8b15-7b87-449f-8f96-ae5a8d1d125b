package definitions

import (
	"auth-api/internal/common/logger"
	"context"
	"fmt"

	gherkin "auth-api/features/step-definitions/types"

	"github.com/cucumber/godog"
	"github.com/mitchellh/mapstructure"
)

/* Background setup */

func ConvertTableToStruct[T any](table *godog.Table, headers []string) ([]T, error) {
	if len(table.Rows) < 2 {
		return nil, fmt.Errorf("table must have headers and at least one row")
	}

	var result []T

	for _, row := range table.Rows[1:] {
		rowMap := make(map[string]any)
		for i, cell := range row.Cells {
			if i < len(headers) {
				key := headers[i]
				rowMap[key] = cell.Value
			}
		}
		var rowT T
		mapstructure.Decode(rowMap, &rowT)
		result = append(result, rowT)
	}

	return result, nil
}

func theFollowingEnterprisesExist(arg1 *godog.Table) error {
	log := logger.NewLogger()
	result, _ := ConvertTableToStruct[gherkin.GSEnterprise](arg1, gherkin.GSEnterpriseMap)
	log.Info(result)
	return godog.ErrPending
}

func theFollowingCompaniesExist(arg1 *godog.Table) error {
	log := logger.NewLogger()
	result, _ := ConvertTableToStruct[gherkin.GSCompany](arg1, gherkin.GSCompanyMap)
	log.Info(result)
	return godog.ErrPending
}

func theFollowingPerssionGroupExists(arg1 *godog.Table) error {
	log := logger.NewLogger()
	result, _ := ConvertTableToStruct[gherkin.GSPermissionGroup](arg1, gherkin.GSPermissionGroupMap)
	log.Info(result)
	return godog.ErrPending
}

func theFollowingUserPermissionsExist(arg1 *godog.Table) error {
	log := logger.NewLogger()
	result, _ := ConvertTableToStruct[gherkin.GSUserPermission](arg1, gherkin.GSUserPermissionMap)
	log.Info(result)
	return godog.ErrPending
}

func theFollowingUsersExist(arg1 *godog.Table) error {
	log := logger.NewLogger()
	result, _ := ConvertTableToStruct[gherkin.GSUser](arg1, gherkin.GSUserMap)
	log.Info(result)
	return godog.ErrPending
}

func granularPermissionReadAndWriteIsInAndNotIn(arg1, arg2, arg3 string) error {
	log := logger.NewLogger()
	log.Infof("granularPermissionReadAndWriteIsInAndNotIn %s %s %s", arg1, arg2, arg3)
	return godog.ErrPending
}

/* Background setup end */

func granularPermissionReadOnlyIsInAndNotIn(arg1, arg2, arg3 string) error {
	return godog.ErrPending
}

func granularPermissionShouldHaveReadAccessAlso(arg1 string) error {
	return godog.ErrPending
}

func iAmLoggedInAs(arg1 string) error {
	return godog.ErrPending
}

func iAttemptToAccessCompany(arg1 string) error {
	return godog.ErrPending
}

func iAttemptToAssignGranularPermissionWriteAccessTo(arg1, arg2 string) error {
	return godog.ErrPending
}

func iShouldBeAccess(arg1 string) error {
	return godog.ErrPending
}

func shouldHaveReadAndWriteAccessGrantedAndShouldHaveReadAndWriteAccessDenied(arg1, arg2 string) error {
	return godog.ErrPending
}

func shouldHaveReadOnlyAccessGrantedAndShouldHaveReadOnlyAccessDenied(arg1, arg2 string) error {
	return godog.ErrPending
}

func twoUsersAreLoggedInAnd(arg1, arg2 string) error {
	return godog.ErrPending
}

func InitializeScenario(ctx *godog.ScenarioContext) {
	log := logger.NewLogger()

	ctx.Before(func(ctx context.Context, sc *godog.Scenario) (context.Context, error) {
		log.Info("Starting scenario: ", ctx)
		return ctx, nil
	})

	ctx.Step(`^the following enterprises exist:$`, theFollowingEnterprisesExist)
	ctx.Step(`^the following companies exist:$`, theFollowingCompaniesExist)
	ctx.Step(`^the following permission groups exist:$`, theFollowingPerssionGroupExists)
	ctx.Step(`^the following user permissions exist:$`, theFollowingUserPermissionsExist)
	ctx.Step(`^the following users exist:$`, theFollowingUsersExist)

	ctx.Step(`^Granular permission "([^"]*)" read and write is in "([^"]*)" and not in "([^"]*)"$`, granularPermissionReadAndWriteIsInAndNotIn)
	ctx.Step(`^Granular permission "([^"]*)" read only is in "([^"]*)" and not in "([^"]*)"$`, granularPermissionReadOnlyIsInAndNotIn)
	ctx.Step(`^granular permission "([^"]*)" should have read access also$`, granularPermissionShouldHaveReadAccessAlso)
	ctx.Step(`^I am logged in as "([^"]*)"$`, iAmLoggedInAs)
	ctx.Step(`^I attempt to access company "([^"]*)"$`, iAttemptToAccessCompany)
	ctx.Step(`^I attempt to assign "([^"]*)" granular permission write access to "([^"]*)"$`, iAttemptToAssignGranularPermissionWriteAccessTo)
	ctx.Step(`^I should be "([^"]*)" access$`, iShouldBeAccess)
	ctx.Step(`^"([^"]*)" should have read and write access granted, and "([^"]*)" should have read and write access denied$`, shouldHaveReadAndWriteAccessGrantedAndShouldHaveReadAndWriteAccessDenied)
	ctx.Step(`^"([^"]*)" should have read only access granted, and "([^"]*)" should have read only access denied$`, shouldHaveReadOnlyAccessGrantedAndShouldHaveReadOnlyAccessDenied)
	ctx.Step(`^Two users are logged in "([^"]*)" and "([^"]*)"$`, twoUsersAreLoggedInAnd)
}
