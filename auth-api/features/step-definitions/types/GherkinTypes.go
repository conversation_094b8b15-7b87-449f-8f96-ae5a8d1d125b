package gherkin

type GSEnterprise struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

var GSEnterpriseMap = []string{"ID", "Name"}

type GSCompany struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Enterprises []string `json:"enterprises"`
}

var GSCompanyMap = []string{"ID", "Name", "Enterprises"}

type GSPermissionGroup struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Permissions []string `json:"permissions"`
}

var GSPermissionGroupMap = []string{"ID", "Name", "Permissions"}

type GSUser struct {
	ID          string   `json:"id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Type        string   `json:"type"`
	IsEpik      string   `json:"isEpik"`
	Permissions []string `json:"permissions"`
}

var GSUserMap = []string{"ID", "Username", "Email", "Type", "IsEpik", "Permissions"}

type GSUserPermission struct {
	UserID      string   `json:"userId"`
	GroupType   string   `json:"groupType"`
	Enterprises []string `json:"enterprises"`
	Company     string   `json:"company"`
}

var GSUserPermissionMap = []string{"UserID", "GroupType", "Enterprises", "Company"}
