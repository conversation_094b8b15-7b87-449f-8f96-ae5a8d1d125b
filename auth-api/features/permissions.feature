Feature: User Permission System
  As a system administrator
  I want to control access based on user permission groups
  So that users can only access appropriate resources

  Background:
    Given the following enterprises exist:
      | id    | name           |
      | ent-1 | Enterprise One |
      | ent-2 | Enterprise Two |
    And the following companies exist:
      | id     | name          | enterprises  |
      | comp-1 | Company One   | ent-1        |
      | comp-2 | Company Two   | ent-1, ent-2 |
      | comp-3 | Company Three | ent-2        |
    And the following permission groups exist:
      | id            | name              | permissions |
      | perm-group-1  | Permission Group 1|             |
      | perm-group-2  | Permission Group 2|             |
    And the following users exist:
      | id         | username | email             | type  | isEpik | permissions                   |
      | admin-user | admin    | <EMAIL> | admin | no     | admin-user-per                |
      | user-1     | user     | <EMAIL>  | user  | no     | ent-1-perm-group-1            |
      | user-2     | user2    | <EMAIL> | user  | no     | ent-1-perm-group-1,ent-2-perm-group-1 |
      | user-3     | user3    | <EMAIL> | user  | no     | ent-1-perm-group-2            |
      | epik-user  | epik     | <EMAIL>  | admin | yes    | admin-user-per                |
    And the following user permissions exist:
      | userId               | groupType       | enterprises | company |
      | admin-user-per       | perm-group-1    |             |         |
      | ent-1-perm-group-1   | perm-group-1    | ent-1       |         |
      | ent-2-perm-group-1   | perm-group-1    | ent-2       |         |
      | ent-1-perm-group-2   | perm-group-2    | ent-1       |         |
      | ent-2-perm-group-2   | perm-group-2    | ent-2       |         |
      | comp-1-perm-group-1  | perm-group-1    |             | comp-1  |
      | comp-2-perm-group-1  | perm-group-1    |             | comp-2  |
      | comp-1-perm-group-2  | perm-group-2    |             | comp-1  |
      | comp-2-perm-group-2  | perm-group-2    |             | comp-2  |

  # Admin tests

  Scenario: Admins can access any company
    Given I am logged in as "admin-user"
    When I attempt to access company "<company>"
    Then I should be "<result>" access

    Examples:
      | company | result  |
      | comp-1  | granted |
      | comp-2  | granted |
      | comp-3  | granted |

  # User tests

  Scenario: Users can access any company inside their enterprise
    Given I am logged in as "<user>"
    When I attempt to access company "<company>"
    Then I should be "<result>" access

    Examples:
      | user   | company | result  |
      | user-1 | comp-1  | granted |
      | user-1 | comp-2  | granted |
      | user-1 | comp-3  | denied  |
      | user-2 | comp-1  | granted |
      | user-2 | comp-2  | granted |
      | user-2 | comp-3  | granted |

  Scenario: When I assign write granular permission to a permission group, that group should have read permission also assigned
    Given I am logged in as "admin-user"
    When I attempt to assign "User Account Approver" granular permission write access to "perm-group-1"
    Then granular permission "User Account Approver" should have read access also

  # Granular permission tests(Read only)

  Scenario: If granular permission(read only) is assigned, user should be able to access(read only) that feature.
    Given Two users are logged in "user-1" and "user-3"
    When Granular permission "<granularPermission>" read only is in "perm-group-1" and not in "perm-group-2"
    Then "user-1" should have read only access <user1Access>, and "user-2" should have read only access <user2Access>


    Examples:
    | granularPermission            | user1Access | user2Access |
    | User Account Approver         | granted     | denied      |
    | Admin Dashboard Access        | granted     | denied      |
    | Bulk Number Action            | granted     | denied      |
    | Call Waiting                  | granted     | denied      |
    | Caller ID Masking             | granted     | denied      |
    | Carrier Access                | granted     | denied      |
    | Carrier Hub: Porting          | granted     | denied      |
    | Data Center Monitor           | granted     | denied      |
    | Data Center Registration      | granted     | denied      |
    | Delete EPI                    | granted     | denied      |
    | Device Monitoring             | granted     | denied      |
    | Documentation Tool            | granted     | denied      |
    | EPI Utilities                 | granted     | denied      |
    | EPI firmware update           | granted     | denied      |
    | Enable Alarm Relay Protocol   | granted     | denied      |
    | Engineering Lock              | granted     | denied      |
    | Enhanced Analyzer             | granted     | denied      |
    | Epik Updates                  | granted     | denied      |
    | Eth0/3 Disable                | granted     | denied      |
    | Events                        | granted     | denied      |
    | IP Phone Management           | granted     | denied      |
    | Integration                   | granted     | denied      |
    | International Dialing         | granted     | denied      |
    | Modify EPI                    | granted     | denied      |
    | NOC Wizard                    | granted     | denied      |
    | OOBM                          | granted     | denied      |
    | Omit 9                        | granted     | denied      |
    | PCAP Analyzer                 | granted     | denied      |
    | PCAP Lookup                   | granted     | denied      |
    | PM Tools                      | granted     | denied      |
    | Port Configuration Management | granted     | denied      |
    | Power Save                    | granted     | denied      |
    | REC Menu                      | granted     | denied      |
    | Registration Offset           | granted     | denied      |
    | TC Tools                      | granted     | denied      |
    | TTY Access Management         | granted     | denied      |
    | Warehouse QC Wizard           | granted     | denied      |
    | EPIK Engineering Toggle       | granted     | denied      |



  # Granular permission tests (read and write)

  Scenario: If granular permission(read and write) is assigned, user should be able to access(read and write) that feature.
    Given Two users are logged in "user-1" and "user-3"
    When Granular permission "<granularPermission>" read and write is in "perm-group-1" and not in "perm-group-2"
    Then "user-1" should have read and write access <user1Access>, and "user-2" should have read and write access <user2Access>


    Examples:
    | granularPermission            | user1Access | user2Access |
    | User Account Approver         | granted     | denied      |
    | Admin Dashboard Access        | granted     | denied      |
    | Bulk Number Action            | granted     | denied      |
    | Call Waiting                  | granted     | denied      |
    | Caller ID Masking             | granted     | denied      |
    | Carrier Access                | granted     | denied      |
    | Carrier Hub: Porting          | granted     | denied      |
    | Data Center Monitor           | granted     | denied      |
    | Data Center Registration      | granted     | denied      |
    | Delete EPI                    | granted     | denied      |
    | Device Monitoring             | granted     | denied      |
    | Documentation Tool            | granted     | denied      |
    | EPI Utilities                 | granted     | denied      |
    | EPI firmware update           | granted     | denied      |
    | Enable Alarm Relay Protocol   | granted     | denied      |
    | Engineering Lock              | granted     | denied      |
    | Enhanced Analyzer             | granted     | denied      |
    | Epik Updates                  | granted     | denied      |
    | Eth0/3 Disable                | granted     | denied      |
    | Events                        | granted     | denied      |
    | IP Phone Management           | granted     | denied      |
    | Integration                   | granted     | denied      |
    | International Dialing         | granted     | denied      |
    | Modify EPI                    | granted     | denied      |
    | NOC Wizard                    | granted     | denied      |
    | OOBM                          | granted     | denied      |
    | Omit 9                        | granted     | denied      |
    | PCAP Analyzer                 | granted     | denied      |
    | PCAP Lookup                   | granted     | denied      |
    | PM Tools                      | granted     | denied      |
    | Port Configuration Management | granted     | denied      |
    | Power Save                    | granted     | denied      |
    | REC Menu                      | granted     | denied      |
    | Registration Offset           | granted     | denied      |
    | TC Tools                      | granted     | denied      |
    | TTY Access Management         | granted     | denied      |
    | Warehouse QC Wizard           | granted     | denied      |
    | EPIK Engineering Toggle       | granted     | denied      |
