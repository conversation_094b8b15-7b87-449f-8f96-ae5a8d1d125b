Feature: Role-based UI access in User Management

  Background:
    Given the user management module is loaded

  ##############################################
  # SECTION: Users Component
  ##############################################

  Scenario Outline: Visibility of Add User button
    Given the logged-in user has role "<role>"
    Then the Add User button should be <visibility>

    Examples:
      | role                | visibility |
      | superAdmin          | visible    |
      | admin               | visible    |
      | enterpriseAdmin     | visible    |
      | supportEngineer     | visible    |
      | companyGroupAdmin   | visible    |
      | companyGroupManager | visible    |
      | user                | hidden     |
      | agent               | hidden     |

  Scenario Outline: Visibility of Export Users (CSV) icon
    Given the logged-in user has role "<role>"
    And no active name/email filter is applied
    Then the Export Users icon should be <visibility>

    Examples:
      | role                | visibility |
      | superAdmin          | visible    |
      | admin               | visible    |
      | enterpriseAdmin     | visible    |
      | companyGroupAdmin   | visible    |
      | companyGroupManager | hidden     |
      | user                | hidden     |

  Scenario Outline: Visibility of Bulk Add Users component
    Given the logged-in user has role "<role>"
    Then the AddBulkUsers component should be <visibility>

    Examples:
      | role    | visibility |
      | user    | visible    |
      | agent   | visible    |
      | admin   | hidden     |

  Scenario Outline: Visibility of PermissionGroups navigation icon
    Given the logged-in user has flag epikEngineering <value>
    Then the PermissionGroupsSvg button should be <visibility>

    Examples:
      | value | visibility |
      | true  | visible    |
      | false | hidden     |

  Scenario Outline: Visibility of filter dropdown options
    Given the logged-in user has role "<role>"
    Then the filter dropdown should include <filterOptions>

    Examples:
      | role                | filterOptions            |
      | superAdmin          | By Company, By Email, By Name, By Role |
      | supportEngineer     | By Company, By Email, By Name, By Role |
      | companyGroupAdmin   | By Company, By Email, By Name, By Role |
      | companyGroupManager | By Company, By Email, By Name, By Role |
      | admin               | By Email, By Role       |
      | user                | By Email, By Role       |

  Scenario Outline: Show approval requests icon
    Given the logged-in user has role "<role>" and isAccountApprover <flag>
    Then the approval requests icon should be <visibility>

    Examples:
      | role        | flag  | visibility |
      | superAdmin  | false | visible    |
      | user        | true  | visible    |
      | agent       | false | hidden     |

  Scenario Outline: Access to all user accounts across companies
    Given the logged-in user has role "<role>"
    Then they should be able to view user accounts for all companies

    Examples:
      | role             |
      | superAdmin       |
      | epikEngineering  |

  Scenario Outline: Access to multiple companies' user accounts
    Given the logged-in user has role "<role>"
    Then they should be able to view user accounts for multiple companies

    Examples:
      | role             |
      | superAdmin       |
      | epikEngineering  |

  Scenario Outline: Access to only their company's user accounts
    Given the logged-in user has role "<role>"
    Then they should be able to view only their own company's user accounts

    Examples:
      | role             |
      | admin            |
      | enterpriseAdmin  |
      | companyGroupAdmin |

  Scenario Outline: PermissionGroups route access
    Given the user clicks PermissionGroups icon
    Then user should be navigated to "/users/permissionsGroup"

  ##############################################
  # SECTION: Permissions Required (Inferred)
  ##############################################

  Rule: Permissions inferred from UI logic in Users component

  - canAddUser: To show AddUser button
  - canAddBulkUsers: Implied by non-admin roles (!isAdminRole)
  - canExportUsers: To enable CSV export icon when no filters are active
  - canViewPermissionGroups: Controlled by `epikEngineering` flag
  - canSeeRequests: Shown to superAdmin or isAccountApprover roles
  - canViewFilterOptions: Determines whether role can use Company/Role/Type filters
  - canViewRoleDropdown: Determines visibility of dropdown with roles from `filterRoleOptions`

  ##############################################
  # SECTION: PermissionsGroup Usage (Confirmed)
  ##############################################

  - Used in: Users Component
  - UI Trigger: PermissionGroupsSvg icon button
  - Condition: `isTrue(user.epikEngineering)`
  - Navigation: `/users/permissionsGroup`

  Required Permission:
  - canManagePermissionGroups (inferred from epikEngineering)


##############################################
  # SECTION: UsersList Component
  ##############################################

  Scenario Outline: Display of ID column
    Given the logged-in user has role "<role>"
    Then the ID column should be <visibility>

    Examples:
      | role                | visibility |
      | superAdmin          | visible    |
      | admin               | visible    |
      | companyGroupAdmin   | visible    |
      | companyGroupManager | visible    |
      | supportEngineer     | visible    |
      | user                | hidden     |
      | agent               | hidden     |

  Scenario Outline: Display of Actions column
    Given the logged-in user has role "<role>"
    Then the Actions column should be <visibility>

    Examples:
      | role                | visibility |
      | superAdmin          | visible    |
      | admin               | visible    |
      | companyGroupAdmin   | visible    |
      | companyGroupManager | visible    |
      | supportEngineer     | visible    |
      | user                | hidden     |
      | agent               | hidden     |

  Scenario: User row renders with correct props
    Given the table is rendering a user row
    Then each User component should receive the correct props for recovery, delete, status toggling, and role context

  ##############################################
  # SECTION: Permissions Required (Inferred)
  ##############################################

  Rule: The following permissions are likely enforced for role-based UI elements

  - canViewUserIdColumn: Required to view the ID column (via `utils.filterCustomer`)
  - canViewActionsColumn: Required to show the Actions column (admin-level roles)
  - canSortUsers: Required to trigger sorting on table headers

  ##############################################
  # SECTION: PermissionsGroup Usage (Confirmed)
  ##############################################

  (None detected in UsersList — only referenced in Users component)


##############################################
  # SECTION: User Component
  ##############################################

  Scenario Outline: Show edit button
    Given the logged-in user has role "<userAppRole>" and the row user has role "<rowUserRole>"
    And deleteMode is false
    Then the Edit button should be <visibility>

    Examples:
      | userAppRole         | rowUserRole         | visibility |
      | superAdmin          | admin               | visible    |
      | superAdmin          | companyGroupAdmin   | visible    |
      | admin               | user                | visible    |
      | companyGroupManager| companyGroupAdmin   | hidden     |

  Scenario Outline: Show delete button
    Given the logged-in user has role "<userAppRole>" and the row user has role "<rowUserRole>"
    Then the Delete button should be <visibility>

    Examples:
      | userAppRole         | rowUserRole         | visibility |
      | superAdmin          | user                | visible    |
      | admin               | admin               | hidden     |
      | companyGroupManager| superAdmin          | hidden     |
      | enterpriseAdmin     | companyGroupManager | hidden     |

  Scenario: Prevent disabling 2FA for privileged roles
    Given the row user has role "superAdmin"
    Then the toggle for disabling 2FA should be blocked with an error toast

  Scenario: Render last active fallback
    Given the lastLoggedIn is null
    Then the component should render "N/A" as the last activity

  ##############################################
  # SECTION: Permissions Required (Inferred)
  ##############################################

  Rule: The following permissions are likely enforced for role-based UI elements

  - canViewUserIdColumn: Required to view the ID column (via `utils.filterCustomer`)
  - canViewActionsColumn: Required to show the Actions column (admin-level roles)
  - canSortUsers: Required to trigger sorting on table headers
  - canEditUser: Required to show edit icon for a user row
  - canDeleteUser: Required to show delete icon for a user row
  - canToggle2FA: Required to enable/disable two-factor authentication

  ##############################################
  # SECTION: PermissionsGroup Usage (Confirmed)
  ##############################################

  (None detected in UsersList or User components — only referenced in Users component)

