FROM golang:1.23-alpine as builder

ENV GO111MODULE=on
ENV GOPROXY=https://proxy.golang.org,direct
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

WORKDIR /app

COPY auth-api/go.mod auth-api/go.sum ./
RUN go mod tidy

COPY auth-api/ ./

# Build the Go binary with optimizations
RUN go build -ldflags="-s -w" -o app ./cmd/api/

# --- Final stage ---
FROM alpine:3.18

# Add CA certificates and timezone data
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN adduser -D -H -h /app appuser
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/app .

# Use non-root user
USER appuser

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:${PORT:-8080}/apps/auth-api/health || exit 1

CMD ["./app"]
