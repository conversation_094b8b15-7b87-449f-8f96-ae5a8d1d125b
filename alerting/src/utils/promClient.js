// import mongoose from "mongoose";
// import * as client from 'prom-client';
// import AlertingService from "../services/AlertingService.js";

// const register = new client.Registry();
// client.collectDefaultMetrics({ register });

// const totalMongodbConnections = new client.Gauge({
//   name: "total_monodb_connections",
//   help: "Number of total MongoDB Conenctions",
//   registers: [register],
//   labelNames: ["MongoDD", "DBConnection"],
// });

// const waitingQueueItems = new client.Gauge({
//     name: "total_waiting_items",
//     help: "Number of total alerts in waiting Status",
//     registers: [register],
//     labelNames: ["Alerts", "WaitingStatus"],
// });
// const completedQueueItems = new client.Gauge({
//     name: "total_completed_items",
//     help: "Number of total alerts in completed Status",
//     registers: [register],
//     labelNames: ["Alerts", "CompletedStatus"],
// });
// const failedQueueItems = new client.Gauge({
//     name: "total_failed_items",
//     help: "Number of total alerts in failed Status",
//     registers: [register],
//     labelNames: ["Alerts", "FailedStatus"],
// });
// const delayedQueueItems = new client.Gauge({
//     name: "total_delayed_items",
//     help: "Number of total alerts in delayed Status",
//     registers: [register],
//     labelNames: ["Alerts", "DelayedStatus"],
// });

// const metricsHandler = async (req, res) => {
//   try {
//     if (req.query.key !== process.env.METRICS_KEY) {
//       console.log(`Invlaid Key metrics `);
//       res.set("Content-Type", register.contentType);
//       res.status(400).end();
//       return;
//     }

//     const {waitingJobs, completedJob, failedJobs, delayedJobs} = await AlertingService.getQueueMetrics();
    
//     waitingQueueItems.set(waitingJobs);
//     completedQueueItems.set(completedJob);
//     failedQueueItems.set(failedJobs);
//     delayedQueueItems.set(delayedJobs);

//     totalMongodbConnections.set(mongoose.connections.length);

//     res.set("Content-Type", register.contentType);
//     res.end(await register.metrics());
//   } catch (error) {
//     console.log(error);
//     res.status(500).end();
//   }
// };

// export default metricsHandler;
