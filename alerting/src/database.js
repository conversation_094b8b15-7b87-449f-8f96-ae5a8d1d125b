import { logger } from "epikio-common-v2";
import mongoose from "mongoose";

class DbConn {
  constructor() {
    this.connecting = false;
    this.timer = 0;
    this.reconnect = this.reconnect.bind(this);
    this.dbURI = process.env.MONGO_DB_URI;
    mongoose.connection.on("connected", this.connected.bind(this));
    mongoose.connection.on("disconnected", this.disconnected.bind(this));
    mongoose.connection.on("error", this.error.bind(this));
  }

  async init() {
    try {
      logger.info(this.dbURI);
      mongoose.set("autoIndex", false);
      await mongoose.connect(this.dbURI, {
        readPreference: "secondaryPreferred",
      });

      return Promise.resolve();
    } catch (err) {
      logger.info("Can not connect to database");
      logger.info(err.toString());
      return Promise.resolve();
    }
  }

  async reconnect() {
    this.timer += 1;
    if (this.timer === 5) {
      logger.info("CRITICAL FAILURE, CANT CONNECT TO DB");
      this.connecting = false;
      return Promise.resolve();
    }
    setTimeout(() => {
      this.connecting = false;
      mongoose.connect(this.dbURI);
    }, this.timer * 5000);
    return Promise.resolve();
  }

  connected() {
    logger.info(`Connected to DB`);
    this.timer = 0;
  }

  disconnected() {
    logger.info(`disconnected from DB, retrying connection`);
    if (!this.connecting) {
      this.connecting = true;
      this.reconnect();
    }
  }

  error(err) {
    logger.info(`Error occured ${err}, retrying connection`);
    if (!this.connecting) {
      this.connecting = true;
      this.reconnect();
    }
  }
}

export default DbConn;
