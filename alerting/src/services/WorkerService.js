import axios from "axios";
import { Worker } from "bullmq";
import { logger } from "epikio-common-v2";
import sendGrid from "../utils/sendGrid.js";
import mongoose from "mongoose";
import authenticator from "authenticator";
import QRCode from "qrcode";
import fs from "fs";
import path from "path";
import alertingLogModel from "../models/alertingLog.js";
import epikBoxAlertsModel from "../models/epikBoxAlerts.js";
import User from "../models/user.js";

const CACHED_2FA_FILES = [];

const get2faFiles = () => {
  if (CACHED_2FA_FILES.length < 6) {
    const currentDir = path.dirname(new URL(import.meta.url).pathname);
    const fileNames = [
      "../templates/img1.jpg",
      "../templates/img2.jpg",
      "../templates/img3.jpg",
      "../templates/credsEmail1.html",
      "../templates/credsEmail2.html",
      "../templates/credsEmail3.html",
    ];
    CACHED_2FA_FILES.push(
      ...fileNames.map((fileName) =>
        fs.readFileSync(path.join(currentDir, fileName), "base64")
      )
    );
  }

  return CACHED_2FA_FILES;
};

const logAlert = async (box, message, isError, errorMessage = null) => {
  return alertingLogModel.create({ box, message, isError, errorMessage });
};

const sendSms = async (box, numbers, text) => {
  if (!numbers || !numbers.length) {
    logger.error(`no number provided`);
    await logAlert(box, numbers, true, "no numbers found on box");
    return;
  }

  for (const number of numbers) {
    const data = {
      source: process.env.SMS_NUMBER,
      destination: number,
      messageText: text,
      apikey: process.env.SMS_API_KEY,
    };
    try {
      logger.info(`about to send sms ${number} ${text}`);

      const formBody = Object.keys(data)
        .map((key) => {
          return encodeURIComponent(key) + "=" + encodeURIComponent(data[key]);
        })
        .join("&");

      let result = await fetch("https://api.voicetel.com/v2/sms/", {
        method: "POST",
        body: formBody,
        headers: { "content-type": "application/x-www-form-urlencoded" },
      });
      result = await result.json();
      console.log(result);
      // result = JSON.parse(result);
      if (!result.message.transaction) {
        await logAlert(box, data, true, JSON.stringify(result, null, 2));
        logger.info("ERROR SENDING SMS, NO TRANSACTION ID");
        logger.info(JSON.stringify(result, null, 2));
      }

      await logAlert(box, data, false);
    } catch (error) {
      await logAlert(box, data, true, JSON.stringify(error, null, 2));
      logger.info(`ERROR SENDING SMS : ${error.stack}`);
    }
  }
};

const sendEmail = async (box, mail) => {
  try {
    if (!mail) {
      logger.error(`no mail entered`);
      await logAlert(box, mail, true, "no mail found on box");
      return;
    }
    logger.info(`sending email ${mail.to} ${mail.subject}`);
    await sendGrid.mail.send(mail);
    await logAlert(box, mail, false);
  } catch (error) {
    await logAlert(box, mail, true, JSON.stringify(error, null, 2));
    logger.info(`Error while sending email : ${error.stack}`);
  }
};
const sendSlackNotification = async (text, url = "") => {
  try {
    logger.info(`sending slack notification ${text}`);
    await fetch(
      url
        ? url
        : "*******************************************************************************",
      {
        method: "POST",
        body: JSON.stringify({ text }),
        headers: { "Content-Type": "application/json" },
      }
    );
    logger.info(`notification sent`);
  } catch (error) {
    logger.info(`Error while sending slack notification : ${error.stack}`);
  }
};

// const worker = new Worker(
//   "epik-alerting-queue",
//   async (job) => {
//     try {
//       await sendEmail(job.data.boxID, job.data.mail);
//       await sendSms(job.data.boxID, job.data.sms);

//       await epikBoxAlertsModel.updateOne(
//         { box: job.data.boxID },
//         { $inc: { counter: 1, alerts: 1 } }
//       );
//     } catch (error) {
//       logger.error(`Failed Job : ${error}`);
//     }
//   },
//   {
//     connection: {
//       host: process.env.REDIS_SERVER,
//       port: process.env.REDIS_PORT,
//       enableOfflineQueue: false,
//     },
//     concurrency: 0,
//   }
// );

// worker.on("error", (err) => {
//   logger.error(err);
// });

// const newUserWorker = new Worker(
//   "epik-new-user-alert-queue",
//   async (job) => {
//     try {
//       const currentDir = path.dirname(new URL(import.meta.url).pathname);
//       const filePath = path.join(currentDir, "img1.jpg");

//       await Promise.all([
//         sendCredentialsEmail(sendGrid, job.data.userEmail),
//         sendTwoFactorAppEmail(
//           job.data.userID,
//           true,
//           job.data.passwordPlainText
//         ),
//       ]);
//     } catch (error) {
//       logger.error(`Failed Job : ${error}`);
//     }
//   },
//   {
//     connection: {
//       host: process.env.REDIS_SERVER,
//       port: process.env.REDIS_PORT,
//       enableOfflineQueue: false,
//     },
//     concurrency: 0,
//   }
// );

// newUserWorker.on("error", (err) => {
//   logger.error(err);
// });

const dbcaWorker = new Worker(
  "epik-dbca-alerting-queue",
  async (job) => {
    try {
      logger.info(job.data);
      await sendEmail(job.data.boxID, job.data.mail);
      if (job.data.sms) {
        await sendSms(
          job.data.boxID,
          job.data.sms.numbers,
          job.data.sms.message
        );
      }
      return true;
    } catch (error) {
      logger.error(`Failed Job : ${error}`);
    }
  },
  {
    connection: {
      host: process.env.REDIS_SERVER,
      port: process.env.REDIS_PORT,
      enableOfflineQueue: false,
    },
    concurrency: 10,
  }
);

dbcaWorker.on("error", (err) => {
  logger.error(err);
});

const slackWorker = new Worker(
  "epik-slack-alerting-queue",
  async (job) => {
    try {
      logger.info(job.data);
      const {
        serialNumber,
        vpnAddress,
        epiIp,
        message,
        channel,
        text: originalText,
      } = job.data;
      const text = `${
        serialNumber ? `Device Serial Number: ${serialNumber}\n` : ""
      }${vpnAddress ? `Device IP: ${vpnAddress}\n` : ""}${
        epiIp ? `EPI IP: ${epiIp}\n` : ""
      }${message ? `Message: ${message}\n` : ""}`;
      await sendSlackNotification(
        originalText ? originalText : text,
        channel === "adv_alerts"
          ? "*******************************************************************************"
          : channel === "epik_dc_failover"
          ? "*******************************************************************************"
          : undefined
      );
      return true;
    } catch (error) {
      logger.error(`Failed Job : ${error}`);
    }
  },
  {
    connection: {
      host: process.env.REDIS_SERVER,
      port: process.env.REDIS_PORT,
      enableOfflineQueue: false,
    },
    concurrency: 10,
  }
);

slackWorker.on("error", (err) => {
  logger.error(err);
});

async function sendCredentialsEmail(sendgrid, email) {
  logger.info(`sending user credentials to email : ${email}`);

  const files = get2faFiles();
  let htmlContent = files[3].toString();
  htmlContent = htmlContent.replace("**username**", email);

  const usernameEmail = {
    to: email,
    from: process.env.NOTIFY_FROM,
    subject: `MyEPIK Portal User Account`,
    html: htmlContent,
  };

  await sendgrid.mail.send(usernameEmail);
}

async function sendTwoFactorAppEmail(userID, hasPassword = false, password) {
  const user = await User.findById(userID).lean();

  const qrImage = await generateOtpQrImage(user);

  if (!qrImage) {
    return {
      success: false,
    };
  }

  const emailFiles = get2faFiles();
  const attachments = [
    {
      filename: "QR_CODE.png",
      content: qrImage,
      encoding: "base64",
      cid: `${user.email}`,
    },
    {
      filename: "img1.jpg",
      content: emailFiles[0],
      encoding: "base64",
      cid: "img1",
    },
    {
      filename: "img2.jpg",
      content: emailFiles[1],
      encoding: "base64",
      cid: "img2",
    },
    {
      filename: "img3.jpg",
      content: emailFiles[2],
      encoding: "base64",
      cid: "img3",
    },
  ];

  if (hasPassword) {
    const subject = "MyEPIK Portal Access and 2FA Instructions";

    let htmlContent = emailFiles[4].toString();
    htmlContent = htmlContent.replace("**password**", password);
    htmlContent = htmlContent.replace("**2facode**", user.twoFactorKey);
    htmlContent = htmlContent.replace("**2fabarcode**", qrImage);

    //await UserService.sendEmail(user.email, htmlContent, subject, attachments);

    return {
      success: true,
    };
  }

  const subject = "MyEPIK Portal 2FA Instructions";
  let htmlContent = emailFiles[5].toString();
  htmlContent = htmlContent.replace("**2facode**", user.twoFactorKey);
  htmlContent = htmlContent.replace("**2fabarcode**", qrImage);

  await UserService.sendEmail(user.email, htmlContent, subject, attachments);

  return {
    success: true,
  };
}

async function generateOtpQrImage(user, generateNewKey = false) {
  const secretKey = generateNewKey
    ? user.twoFactorKey
    : await authenticator.generateKey();

  const img = await authenticator.generateTotpUri(
    secretKey,
    user.email,
    "Epik",
    "SHA1",
    6,
    30
  );

  const imageData = await QRCode.toDataURL(img);
  const qrImage = imageData.split(";base64,").pop();

  if (!generateNewKey) {
    await User.findByIdAndUpdate(user._id, { twoFactorKey: secretKey });
  }

  return qrImage;
}

export { /* worker, newUserWorker, */ dbcaWorker };
