import { Queue } from "bullmq";
import { logger, successResult } from "epikio-common-v2";
import epikBoxAlertsModel from "../models/epikBoxAlerts.js";
import epikBoxModel from "../models/epikBox.js";
import companyModel from "../models/company.js";
const queue = new Queue("epik-alerting-queue", {
  connection: {
    host: process.env.REDIS_SERVER,
    port: process.env.REDIS_PORT,
    enableOfflineQueue: false,
  },
});

const dbcaQueue = new Queue("epik-dbca-alerting-queue", {
  connection: {
    host: process.env.REDIS_SERVER,
    port: process.env.REDIS_PORT,
    enableOfflineQueue: false,
  },
});

queue.on("error", (err) => {
  logger.error(err);
});

class AlertingService {
  async getAlerting(req, res) {
    for await (const offlineBox of epikBoxAlertsModel
      .find({ counter: { $gte: 3 }, alerts: { $lt: 3 } })
      .populate({
        path: "box",
        populate: {
          path: "assignedTo",
        },
      })
      .cursor()) {
      const now = new Date();
      const allNotifyMails = offlineBox.box.alertCompanyMails
        ? [
            ...offlineBox.box.notifyEmails,
            ...offlineBox.box.assignedTo.contactEmails,
          ]
        : [...offlineBox.box.notifyEmails];

      const mail =
        allNotifyMails && allNotifyMails.length
          ? {
              to: allNotifyMails,
              from: process.env.NOTIFY_FROM,
              subject: `${offlineBox.box.displayName} has an issue`,
              text: `Epik box ${offlineBox.box.displayName} serial number ${
                offlineBox.box.serialNumber
              } assigned to ${
                offlineBox.box.assignedTo.name
              } lost registration ${new Date()}`,
            }
          : false;

      const sms =
        offlineBox.box.notifyNumbers && offlineBox.box.notifyNumbers.length
          ? {
              numbers: offlineBox.box.notifyNumbers,
              message: `Epik box ${offlineBox.box.displayName} lost registration ${now}`,
            }
          : false;

      await queue.add(
        offlineBox.box._id,
        {
          mail,
          sms,
          boxID: offlineBox.box._id,
          offlineNotification: true,
          type: "offline",
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 10000,
          },
        }
      );
    }
    const result = successResult;
    res.status(200).json(result);
  }
  async dbcaAlert({ serialNumber, subject, body, type }) {
    logger.info(`dbcaAlert ${serialNumber} ${subject} ${body} ${type}`);
    const box = await epikBoxModel
      .findOne({ serialNumber })
      .populate("assignedTo");
    logger.info(`box found in db -> ${box.serialNumber}`);
    if (!box) {
      logger.info(`${serialNumber} | ${subject} | ${body} | ${type} | Box not found.`);
      return false;
    }
    if(!box.dbcaAlerts){
      logger.info(`${serialNumber} | ${subject} | ${body} | ${type} | DBCA alerts are off for this device`);
      return false
    }
    logger.info(box.alertCompanyMails);
    logger.info(box.notifyEmails);
    logger.info(box.assignedTo && box.assignedTo.contactEmails);
    logger.info(box.notifyEmails);
    logger.info(box.notifyNumbers);
    const allNotifyMails =
      box.alertCompanyMails && box.assignedTo
        ? [...box.notifyEmails, ...box.assignedTo.contactEmails]
        : [...box.notifyEmails];

    const mail =
      allNotifyMails && allNotifyMails.length
        ? {
            to: allNotifyMails,
            from: process.env.NOTIFY_FROM,
            subject: subject,
            text: body,
          }
        : false;

    const sms =
      box.notifyNumbers && box.notifyNumbers.length
        ? {
            numbers: box.notifyNumbers,
            message: body,
          }
        : false;

    await dbcaQueue.add(
      `dbca-${box._id}`,
      {
        mail,
        sms,
        boxID: box._id,
        type,
      },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 10000,
        },
      }
    );
    return true;
  }

  async updateDeviceContactsAboutPowerSourceUpdate(req, res) {
    const { serialNumber } = req.params;
    if (!serialNumber) {
      logger.error(`Missing Box SerialNumber`);
      return res.status(400).json("Missing Box SerialNumber");
    }
    const box = await epikBoxModel
      .findOne({ serialNumber })
      .populate({ path: "assignedTo" })
      .lean();
    const now = new Date();
    const allNotifyMails = box.alertCompanyMails
      ? [...box.notifyEmails, ...box.assignedTo.contactEmails]
      : [...box.notifyEmails];

    const mail =
      allNotifyMails && allNotifyMails.length
        ? {
            to: allNotifyMails,
            from: process.env.NOTIFY_FROM,
            subject: `${box.displayName} power source has been changed`,
            text: `Epik box ${box.displayName} serial number ${box.serialNumber} assigned to ${box.assignedTo.name} power source updated at ${now}. New power source is ${box.powerSource}`,
          }
        : false;

    const sms =
      box.notifyNumbers && box.notifyNumbers.length
        ? {
            numbers: box.notifyNumbers,
            message: `Epik box ${box.displayName} power source updated at ${now}. New power source is ${box.powerSource}`,
          }
        : false;

    await queue.add(
      box._id,
      {
        mail,
        sms,
        boxID: box._id,
        offlineNotification: false,
        type: "powerSource",
      },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 10000,
        },
      }
    );
    const result = successResult;
    res.status(200).json(result);
  }
  async getQueueMetrics() {
    const [waitingJobs, completedJob, failedJobs, delayedJobs] =
      await Promise.all([
        queue.getWaitingCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
        queue.getDelayedCount(),
      ]);

    return {
      waitingJobs,
      completedJob,
      failedJobs,
      delayedJobs,
    };
  }
}

export default new AlertingService();
