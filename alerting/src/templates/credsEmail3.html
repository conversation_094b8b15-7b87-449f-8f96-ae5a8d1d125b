<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credentials myEpik</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #ddd;
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 200px;
        }
        h1 {
            color: #333;
        }
        p {
            font-size: 16px;
        }
        .credentials {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
        }
        .barcode {
            margin-top: 20px;
            text-align: center;
        }
        .barcode img {
            max-width: 300px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="data:image/png;base64,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********************************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" alt="Granite Epik Logo">
        </div>
        <h1>Two Factor Auth</h1>
        <p>Please follow the instructions for 2FA below</p>
        <div class="credentials">
            <p><strong>2FA Code:</strong> **2facode**</p>
        </div>
        <div>
            <p>2FA Instructions</p>
            Step 1 - Search “Google Authenticator” in your Google Play or App Store</br>
            Step 2 - Open app and select Begin Setup<br>
            Step 3 - Select Scan Barcode.</br>
            Step 4 - Scan QR Code.</br>
        </div>
        <div class="barcode">
            <img src="data:image/png;base64,**2fabarcode**" alt="Barcode">
        </div>
        <p>If you have any questions or need assistance, please contact our support team.</p>
        <div class="footer">
            This is an automated email. Please do not reply.
        </div>
    </div>
</body>
</html>