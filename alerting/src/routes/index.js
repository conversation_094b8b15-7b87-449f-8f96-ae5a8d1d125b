import { Router } from "express";
import AlertingService from "../services/AlertingService.js";
// import metricsHandler from "../utils/promClient.js";
import { successResult, withTrycatch } from "epikio-common-v2";

const router = new Router();

/**
 *
 * GET /api/ping
 *
 * @summary Get Status
 * @tags app
 * @return {} 200 - success responses - application/json
 */

router.get("/ping", (req, res) => {
  return res.send({ success: true });
});

router.get("/monitoring", withTrycatch(AlertingService.getAlerting));
router.get(
  "/updateDeviceContactsAboutPowerSourceUpdate/:serialNumber",
  withTrycatch(AlertingService.updateDeviceContactsAboutPowerSourceUpdate)
);
router.post(
  "/dbca",
  withTrycatch(async (req, res, next) => {
    const { body, serialNumber, subject, type } = req.body;
    const status = await AlertingService.dbcaAlert({
      body,
      serialNumber,
      subject,
      type,
    });
    const result = successResult;
    result.success = status;
    res.status(200).json(result);
  })
);
// router.get("/metrics", metricsHandler);

export default router;
