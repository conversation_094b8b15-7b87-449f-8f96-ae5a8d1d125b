{"name": "auth", "module": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "test:ci": "vitest run"}, "devDependencies": {"@types/bun": "latest", "supertest": "^6.3.4", "vitest": "^1.2.2"}, "dependencies": {"argon2": "^0.31.2", "body-parser": "^1.20.2", "email-validator": "^2.0.4", "epikio-common-v2": "^0.0.44", "express": "^4.18.2", "joi": "^17.11.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.0", "password-hash-and-salt": "^0.1.4", "ua-parser-js": "^1.0.37", "@sendgrid/mail": "^7.7.0", "authenticator": "^1.1.5", "axios": "^1.5.1", "bullmq": "^4.12.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-jsdoc-swagger": "^1.8.0", "fs-extra": "^11.2.0", "helmet": "^7.0.0", "libphonenumber-js": "^1.10.44", "path": "^0.12.7", "qrcode": "^1.5.3"}}