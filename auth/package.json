{"name": "auth", "module": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "test:ci": "vitest run"}, "devDependencies": {"@types/bun": "latest", "supertest": "^6.3.4", "vitest": "^1.2.2"}, "dependencies": {"argon2": "^0.31.2", "body-parser": "^1.20.2", "email-validator": "^2.0.4", "epikio-common-v2": "^0.0.44", "express": "^4.18.2", "joi": "^17.11.1", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.11.19", "mongoose": "^8.1.0", "mongoose-aggregate-paginate-v2": "^1.1.3", "mongoose-paginate-v2": "^1.9.0", "password-hash-and-salt": "^0.1.4", "ua-parser-js": "^1.0.37"}}