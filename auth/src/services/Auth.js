import <PERSON><PERSON> from "joi";
import { signinSchema } from "./validations/Auth.js";
import { CustomError, logger } from "epikio-common-v2";
import userModel from "../models/user.js";
import permissionGroupsModel from "../models/permissionGroups.js";
import UserSessionModel from "../models/userSession.js";
import UserLoginModel from "../models/userLogin.js";
import Password from "password-hash-and-salt";
import jwt from "jsonwebtoken";
import companyGroupModel from "../models/companyGroupModel.js";
import argon from "argon2";

export default class Auth {
  constructor() {
    this.signin = this.signin.bind(this);
    this.getIsUserAccountLocked = this.getIsUserAccountLocked.bind(this);
    this.verifyPasswordHash = this.verifyPasswordHash.bind(this);
    this.promisedVerify = this.promisedVerify.bind(this);
    this.getToken = this.getToken.bind(this);
  }
  /**
   *
   * @param {string} userId
   */
  async getToken(userId) {
    const getAccess = async (obj = {}) => {
      const { read, write } = obj;
      return read || write;
    };
    const log = logger.child({ module: "getToken" });
    log.info(userId);
    const user = await userModel.findOne({ _id: userId });
    const group = await companyGroupModel.findOne({
      groupSuperAdmins: { $in: [userId] },
    });
    const permissionG = await permissionGroupsModel.findOne({
      title: user?.permissionsGroup,
    });

    const {
      numberCarrierChangeAccess,
      dataCenterMonitor,
      ttyOptionsAccess,
      realTimeActivityEnabled,
      warehouseQcAccess,
      nocWizardAccess,
    } = permissionG;

    let userDoc = {
      id: user._id,
      email: user.email,
      phoneNumber: user.phoneNumber,
      enabled: user.enabled,
      numberCarrierChangeAccess: getAccess(numberCarrierChangeAccess),
      deviceMonitor: user.deviceMonitor,
      dataCenterMonitor: getAccess(dataCenterMonitor),
      ttyOptionsAccess: getAccess(ttyOptionsAccess),
      realTimeActivityEnabled: getAccess(realTimeActivityEnabled),
      company: user.company,
      isAdmin: user.isAdmin,
      role: user.role,
      readOnly: user.readOnly,
      groupId: group?._id,
      warehouseQcAccess: getAccess(warehouseQcAccess),
      warehouseQcRecAccess: user.warehouseQcRecAccess,
      nocWizardAccess: getAccess(nocWizardAccess),
      epikEngineering: user?.epikEngineering,
      nocLogsAccess: user?.nocLogsAccess,
    };
    const token = jwt.sign(userDoc, process.env.SECRET);
    return { ...userDoc, token };
  }
  /**
   * Authenticates a user based on the provided email and password.
   *
   * @param {Object} obj
   * @param {string} obj.email
   * @param {string} obj.password
   * @param {string} obj.ip
   * @param {string} obj.platform
   * @param {string} obj.deviceID
   */
  async signin({ email, password, deviceID, ip, platform }) {
    const log = logger.child({ module: "signin" });
    log.info({ email, password, deviceID, ip, platform });
    const validate = signinSchema.validate({ email, password });
    if (validate.error?.message) {
      throw new CustomError(validate.error.message, 400);
    }
    email = email.trim().toLowerCase();
    const user = await userModel
      .findOne({
        email,
        deleted: { $ne: true },
        enabled: true,
      })
      .lean();
    const session = new UserSessionModel({});
    if (!user) {
      session.loginSuccess = false;
      session.failureReason = "User not found with the given email";
      await session.save();
      log.error(`Login attempt failed for email ${email} from ${ip}`);
      throw new CustomError(session.failureReason);
    }
    if (user.enabled === false) {
      session.loginSuccess = false;
      session.failureReason =
        "Your Account has been disabled due to inactivity. Please contact Granite Support Desk to have your account re-activated. To avoid this in the future, please login to the MyEPIK portal at least once every 90 days.";
      await session.save();
      log.error(`Login attempt failed for email ${email} from ${ip}`);
      throw new CustomError(session.failureReason);
    }
    if (user.role === "epikBox") {
      session.loginSuccess = false;
      session.failureReason = "Invalid user role: epikbox";
      session.save();
      throw new CustomError("unauthorized");
    }

    const isUserLocked = await this.getIsUserAccountLocked(user._id);
    if (isUserLocked) {
      session.loginSuccess = false;
      session.failureReason = "Account Locked";
      session.save();
      throw new CustomError(
        "Your Account has been Locked for 15 minutes due to multiple wrong Attempts"
      );
    }

    const isPasswordVerified = await this.verifyPasswordHash({
      password,
      passwordHash: user.password,
    });

    if (!isPasswordVerified) {
      session.loginSuccess = false;
      session.user = user._id;
      session.failureReason = "Incorrect Password";

      await Promise.all([
        UserLoginModel.updateOne(
          { user: user._id },
          { $inc: { counter: 1 } },
          { upsert: true }
        ),
        session.save(),
      ]);

      log.error(`Login attempt failed for email ${email} from ${ip}`);
      throw new CustomError("password incorrect");
    }

    if (user.password.split("$")[0] === "pbkdf2") {
      const passwordNewHash = await this.generatePasswordHash(password);
      await userModel.updateOne(
        { _id: user._id },
        { password: passwordNewHash }
      );
    }

    await UserLoginModel.updateOne(
      { user: user._id },
      { $set: { counter: 0 } },
      { upsert: true }
    );

    // uncomment to enable password expiry
    // user = await this.getUserWithPasswordExpiryStatus(user);

    if (user.twoFactor) {
      const { email: userEmail, _id, phoneNumber, twoFactor } = user;
      return {
        email: userEmail,
        _id,
        twoFactor,
        phoneNumber,
      };
    }

    session.loginSuccess = true;
    session.user = user._id;
    session.save();

    await userModel.updateOne({ _id: user._id }, { lastLoggedIn: new Date() });
    return this.getToken(user._id);
  }
  /**
   *
   * @param {string} userID
   */
  async getIsUserAccountLocked(userID) {
    const userLoginSession = await UserLoginModel.findOne({
      user: userID,
    }).lean();

    const isUserLocked =
      !!userLoginSession &&
      userLoginSession.counter >= (process.env.MAX_PASSWORD_ATTEMPTS || 3);

    return isUserLocked;
  }
  /**
   *
   * @param {Object} obj
   * @param {string} obj.password
   * @param {string} obj.passwordHash
   */
  async verifyPasswordHash({ password, passwordHash }) {
    if (!password || !passwordHash) {
      return false;
    }

    const key = passwordHash.split("$");

    const algoType = key[0];

    if (algoType === "pbkdf2" && key.length === 4) {
      const pbkdf2Password = new Password(password);

      return this.promisedVerify({
        passwordObj: pbkdf2Password,
        hash: passwordHash,
      });
    }

    return argon.verify(passwordHash, password);
  }
  /**
   *
   * @param {Object} obj
   * @param {string} obj.passwordObj
   * @param {string} obj.hash
   */
  async promisedVerify({ passwordObj, hash }) {
    return new Promise((resolve, reject) => {
      passwordObj.verifyAgainst(hash, (error, verified) => {
        if (error) reject(new Error("Something went wrong!"));
        if (!verified) {
          resolve(false);
        } else {
          resolve(true);
        }
      });
    });
  }
}
