import { logger } from "epikio-common-v2";
import { app } from "./app.js";
import DbConn from "./database.js";

const start = async () => {
  if (!process.env.SECRET) {
    throw new Error("SECRET must be defined");
  }

  if (!process.env.MONGO_DB_URI) {
    throw new Error("MONGO_DB_URI must be defined");
  }
  const port = process.env.PORT || 3000;
  const hosts = (process.env.HOSTS || "0.0.0.0").split(",");
  const db = new DbConn();
  await db.init();

  console.log("Connected to MongoDB");
  hosts.forEach((host) => {
    app.listen(port, host, () => {
      logger.info(`Server is listening on http://${host}:${port}`);
    });
  });
};

start();
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
});
