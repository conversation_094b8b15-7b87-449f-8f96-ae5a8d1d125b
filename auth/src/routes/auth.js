import express from "express";
import { successResult, verifyToken, withTrycatch } from "epikio-common-v2";
import Auth from "../services/Auth.js";
import { UAParser } from "ua-parser-js";

const router = express.Router();

router.get(
  "/verify-token",
  withTrycatch(verifyToken),
  withTrycatch(async(req, res) => {
    const auth = new Auth();
    const user = await auth.getToken(req.user.id);
    const result = successResult;
    result.data = user;
    res.status(200).json(result);
  })
);

router.post(
  "/signin",
  withTrycatch(async (req, res, next) => {
    const { body, headers } = req;
    const { deviceID } = req.query;
    const ip = headers["x-forwarded-for"];
    const userAgent = headers["user-agent"];
    const { browser, os } = UAParser(userAgent);
    const platform = `${os.name} | ${browser.name}`;
    body.platform = platform;
    body.ip = ip;
    body.deviceID = deviceID;
    const auth = new Auth();
    const data = await auth.signin(body);
    const result = successResult;
    result.data = data;
    res.status(200).json(result);
  })
);

export default router;
