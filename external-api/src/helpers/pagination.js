async function paginate(model, page = 1, limit = 10, query = {}) {
    try {
      const skip = (page - 1) * limit;
      const records = await model.find(query).skip(skip).limit(limit).exec();
      const totalRecords = await model.countDocuments(query).exec();
      console.log("Total records count:", totalRecords);
      const totalPages = Math.ceil(totalRecords / limit);
      return {
        records,
        totalRecords,
        totalPages,
      };
    } catch (error) {
      console.error("Pagination error:", error);
      throw new Error("Pagination error");
    }
  }
  export default paginate;
  