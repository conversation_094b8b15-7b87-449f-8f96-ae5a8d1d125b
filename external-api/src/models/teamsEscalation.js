import mongoose from "mongoose";

const { Schema } = mongoose;

const teamsEscalationSchema = new Schema(
  {
    serialNumber: { type: String, trim: true, required: true },
    type: { type: String, default: "" },
    text: { type: String, default: "" },
    cwTicket: { type: String, default: "" },
    link: { type: String, default: "" },
  },
  {
    created_at: true,
  }
);

const teamsEscalationSchemaModel = mongoose.model("TeamsEscalation", teamsEscalationSchema);

export default teamsEscalationSchemaModel;
