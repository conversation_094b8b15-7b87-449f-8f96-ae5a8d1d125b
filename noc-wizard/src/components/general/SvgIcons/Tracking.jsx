import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function Tracking({
  tooltip,
  size,
  fill = "currentColor",
  stroke = "none",
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        fill={fill}
        size={size}
        disabled={disabled}
        onClick={onClick}
        viewBox="0 0 42 42"
        style={{ transform: "scale(1.05)" }}
      >
        <rect width="42" height="42" rx="6" fill="#4D9CD3" fillOpacity="0.15"/>
        <g clipPath="url(#clip0_300_1451)">
          <path d="M21 12C21.5523 12 22 12.4477 22 13V21C22 21.5523 21.5523 22 21 22C20.4477 22 20 21.5523 20 21V13C20 12.4477 20.4477 12 21 12Z" fill="#4D9CD3"/>
          <path d="M21 26C21.5523 26 22 25.5523 22 25C22 24.4477 21.5523 24 21 24C20.4477 24 20 24.4477 20 25C20 25.5523 20.4477 26 21 26Z" fill="#4D9CD3"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M12 21C12 16.0294 16.0294 12 21 12C25.9706 12 30 16.0294 30 21C30 25.9706 25.9706 30 21 30C16.0294 30 12 25.9706 12 21ZM21 14C17.134 14 14 17.134 14 21C14 24.866 17.134 28 21 28C24.866 28 28 24.866 28 21C28 17.134 24.866 14 21 14Z" fill="#4D9CD3"/>
        </g>
        <defs>
          <clipPath id="clip0_300_1451">
            <rect width="24" height="24" fill="white" transform="translate(9 9)"/>
          </clipPath>
        </defs>
      </SvgBase2>
    </Tooltip1>
  );
}
