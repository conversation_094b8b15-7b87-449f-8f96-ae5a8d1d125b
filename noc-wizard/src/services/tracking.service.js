import { fetchWrapper } from '@/api';
import { APIS } from '@/constants';

export const getNocTrackingData = async (params = {}) => {
  try {
    const { page = 1, pageSize = 10, search, ...filters } = params;
    
    // Construct query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('pageSize', pageSize);
    
    // Add search parameter if it exists
    if (search !== undefined && search !== null && search !== '') {
      queryParams.append('search', search);
    }
    
    // Add any other filters that have values
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });
    
    const response = await fetchWrapper({
      api: APIS.TOOLS,
      endpoint: `/tracking/noc-data?${queryParams.toString()}`,
      method: 'GET',
    });
    
    return {
      success: true,
      data: response.data || { items: [], pagination: { page, pageSize, totalItems: 0, totalPages: 0 } }
    };
  } catch (error) {
    console.error('Error fetching NOC tracking data:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch NOC tracking data'
    };
  }
};

export const getTicketDetails = async (ticketId) => {
  try {
    const response = await fetchWrapper({
      api: APIS.TOOLS,
      endpoint: `/tracking/noc-data/${ticketId}`,
      method: 'GET',
    });
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error(`Error fetching ticket details for ID ${ticketId}:`, error);
    return {
      success: false,
      error: error.message || 'Failed to fetch ticket details'
    };
  }
};