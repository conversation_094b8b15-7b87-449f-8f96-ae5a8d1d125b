import { useState, useEffect, useMemo } from 'react';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  flexRender,
  useReactTable,
} from '@tanstack/react-table';
import { Button, Dropdown, TextInput, Pagination, Spinner } from 'flowbite-react';
import { ChevronUp, ChevronDown, Search, Filter } from 'lucide-react';
import { LayoutWrapper, MainWrapper, DateTime } from '@/components';
import { getNocTrackingData } from '@/services/tracking.service';
import TicketDetailModal from './TicketDetailModal';
import './nocTracking.css';

export default function NocTracking() {
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [sorting, setSorting] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterValue, setFilterValue] = useState('');
  const [appliedFilters, setAppliedFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTicketId, setSelectedTicketId] = useState(null);
  const [showTicketDetail, setShowTicketDetail] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Build query parameters for filtering and pagination
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...appliedFilters
      };

      const { success, data, error } = await getNocTrackingData(params);

      if (success && data) {
        setData(data.items || []);
        setPagination(data.pagination || pagination);
      } else {
        console.error('Error fetching data:', error);
      }
    } catch (error) {
      console.error('Error fetching NOC tracking data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.page, pagination.pageSize]);

  // Apply filters when appliedFilters change
  useEffect(() => {
    // Reset to first page when filters change
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchData();
  }, [appliedFilters]);

  const handleTicketStatusChange = (ticketId, newStatus) => {
    // Update the status in the local data
    setData(prev => 
      prev.map(item => 
        item.id === parseInt(ticketId) 
          ? { ...item, status_name: newStatus } 
          : item
      )
    );
  };

  const handleRowClick = (ticketId) => {
    setSelectedTicketId(ticketId);
    setShowTicketDetail(true);
  };

  const columns = useMemo(
    () => [      {
        accessorKey: 'id',
        header: 'Ticket ID',
        cell: info => <div className="text-xs">{info.getValue()}</div>,
        sortable: true,
      },
      {
        accessorKey: 'summary',
        header: 'Summary',
        cell: info => (
          <div className="max-w-[280px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'company_name',
        header: 'Company',
        cell: info => (
          <div className="max-w-[120px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'site_name',
        header: 'Site',
        cell: info => (
          <div className="max-w-[150px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'status_name',
        header: 'Status',
        cell: info => {
          const status = info.getValue();
          // Map status to shorter display text if needed
          const statusMap = {
            'Resolved': 'Resolved',
            'New': 'New',
            'In Progress': 'In Progress',
            'On Hold': 'On Hold',
            'Closed': 'Closed'
          };
          const displayText = statusMap[status] || status;
          
          return (
            <div className="flex justify-center">
              <span 
                className={`px-1.5 py-0.5 rounded text-xs font-medium w-[90px] text-center whitespace-nowrap overflow-hidden text-ellipsis
                  ${status === 'Resolved' ? 'bg-green-100 text-green-800' : 
                    status === 'New' ? 'bg-blue-100 text-blue-800' : 
                    status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' : 
                    status === 'On Hold' ? 'bg-purple-100 text-purple-800' :
                    status === 'Closed' ? 'bg-gray-200 text-gray-800' :
                    'bg-gray-100 text-gray-800'}`
                }
                title={status}
              >
                {displayText}
              </span>
            </div>
          );
        },
        sortable: true,
      },
      {
        accessorKey: 'priority_name',
        header: 'Priority',
        cell: info => {
          const priority = info.getValue() || '';
          // Map priority to shorter display text
          const priorityMap = {
            'Priority 1 - Critical': 'P1-Critical',
            'Priority 2 - High': 'P2-High',
            'Priority 3 - Medium': 'P3-Medium',
            'Priority 4 - Low': 'P4-Low'
          };
          const displayText = priorityMap[priority] || priority;
          
          return (
            <div className="flex justify-center">
              <span 
                className={`px-1.5 py-0.5 rounded text-xs font-medium w-[90px] text-center whitespace-nowrap overflow-hidden text-ellipsis
                  ${priority.includes('Priority 1') ? 'bg-red-100 text-red-800' : 
                    priority.includes('Priority 2') ? 'bg-orange-100 text-orange-800' : 
                    priority.includes('Priority 3') ? 'bg-yellow-100 text-yellow-800' : 
                    priority.includes('Priority 4') ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'}`
                }
                title={priority}
              >
                {displayText}
              </span>
            </div>
          );
        },
        sortable: true,
      },
      {
        accessorKey: 'type_name',
        header: 'Type',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'site_name',
        header: 'Site',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'item_name',
        header: 'Item',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'source_name',
        header: 'Source',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'severity',
        header: 'Severity',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'closed_flag',
        header: 'Closed',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue() ? 'Yes' : 'No'}
          </div>
        ),
        sortable: true,
      },      {
        accessorKey: 'serial_number',
        header: 'Serial Number',
        cell: info => (
          <div className="max-w-[100px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'sla_name',
        header: 'SLA',
        cell: info => (
          <div className="max-w-[120px] truncate text-xs" title={info.getValue()}>
            {info.getValue()}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'sla_status',
        header: 'SLA Status',
        cell: info => {
          const slaStatus = info.getValue();
          return (
            <div className="flex justify-center">
              <span 
                className={`px-1.5 py-0.5 rounded text-xs font-medium w-[90px] text-center whitespace-nowrap overflow-hidden text-ellipsis
                  ${slaStatus === 'Resolved' ? 'bg-green-100 text-green-800' : 
                    slaStatus === 'Breached' ? 'bg-red-100 text-red-800' : 
                    slaStatus === 'At Risk' ? 'bg-orange-100 text-orange-800' : 
                    slaStatus === 'Responded' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'}`
                }
                title={slaStatus}
              >
                {slaStatus}
              </span>
            </div>
          );
        },
        sortable: true,
      },
      {
        accessorKey: 'escalation_level',
        header: 'Escalation',
        cell: info => {
          const level = info.getValue();
          if (!level || level === 0) return <div className="text-xs text-center">-</div>;
          
          return (
            <div className="flex justify-center">
              <span 
                className={`px-1.5 py-0.5 rounded text-xs font-medium w-[60px] text-center
                  ${level > 10 ? 'bg-red-100 text-red-800' : 
                    level > 5 ? 'bg-orange-100 text-orange-800' : 
                    'bg-yellow-100 text-yellow-800'}`
                }
                title={`Escalation Level: ${level}`}
              >
                Level {level}
              </span>
            </div>
          );
        },
        sortable: true,
      },
      {
        accessorKey: 'escalation_start_date_utc',
        header: 'Escalated',
        cell: info => (
          <div className="text-xs">
            {info.getValue() ? <DateTime date={info.getValue()} format="MMM D, YYYY h:mm A" /> : '-'}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'responded_hours',
        header: 'Respond',
        cell: info => (
          <div className="text-xs text-center">
            {info.getValue() ? `${info.getValue()}h` : '-'}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'resplan_hours',
        header: 'Plan',
        cell: info => (
          <div className="text-xs text-center">
            {info.getValue() ? `${info.getValue()}h` : '-'}
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'resolution_hours',
        header: 'Resolve',
        cell: info => (
          <div className="text-xs text-center">
            {info.getValue() ? `${info.getValue()}h` : '-'}
          </div>
        ),
        sortable: true,
      },      
      {
        accessorKey: 'created_at',
        header: 'Created',
        cell: info => (
          <div className="text-xs">
            <DateTime date={info.getValue()} format="MMM D, YYYY h:mm A" />
          </div>
        ),
        sortable: true,
      },
      {
        accessorKey: 'updated_at',
        header: 'Updated',
        cell: info => (
          <div className="text-xs">
            <DateTime date={info.getValue()} format="MMM D, YYYY h:mm A" />
          </div>
        ),
        sortable: true,
      },
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      globalFilter,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // Use client-side filtering only for the global filter
    // which is applied to the data already fetched from the server
    getFilteredRowModel: getFilteredRowModel(),
    // Enable manual pagination since we're doing server-side pagination
    manualPagination: true,
    pageCount: pagination.totalPages,
  });

  // Filter components with single field and type selector
  const FilterDropdowns = () => {
      const filterTypes = [
      { value: 'status_name', label: 'Status' },
      { value: 'priority_name', label: 'Priority' },
      { value: 'type_name', label: 'Type' },
      { value: 'company_name', label: 'Company' },
      { value: 'site_name', label: 'Site' },
      { value: 'serial_number', label: 'Serial Number' },
      { value: 'sla_name', label: 'SLA' },
      { value: 'sla_status', label: 'SLA Status' },
      { value: 'escalation_level', label: 'Escalation Level' },
    ];

    // Predefined options for select fields
    const selectOptions = {
      status_name: [
        { value: 'New', label: 'New' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'On Hold', label: 'On Hold' },
        { value: 'Resolved', label: 'Resolved' },
        { value: 'Closed', label: 'Closed' },
      ],
      priority_name: [
        { value: 'Priority 1 - Critical', label: 'Priority 1 - Critical' },
        { value: 'Priority 2 - High', label: 'Priority 2 - High' },
        { value: 'Priority 3 - Medium', label: 'Priority 3 - Medium' },
        { value: 'Priority 4 - Low', label: 'Priority 4 - Low' },
      ],      type_name: [
        { value: 'Wireless Voice', label: 'Wireless Voice' },
        { value: 'Network', label: 'Network' },
        { value: 'Hardware', label: 'Hardware' },
        { value: 'Software', label: 'Software' },
        { value: 'Other', label: 'Other' },
      ],
      sla_status: [
        { value: 'Resolved', label: 'Resolved' },
        { value: 'Breached', label: 'Breached' },
        { value: 'At Risk', label: 'At Risk' },
        { value: 'Responded', label: 'Responded' },
      ],
    };

    const handleApplyFilter = () => {
      if (filterType && filterValue) {
        // Create a filter object with just the selected filter type and value
        const newFilters = { ...appliedFilters, [filterType]: filterValue };
        setAppliedFilters(newFilters);
      }
      setShowFilters(false);
    };

    const handleClearFilters = () => {
      setFilterType('');
      setFilterValue('');
      setAppliedFilters({});
      setShowFilters(false);
    };

    return (
      <div className={`filter-container p-4 bg-white rounded-lg shadow-lg ${showFilters ? '' : 'hidden'}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="filter-type" className="block text-sm font-medium text-gray-700 mb-1">Filter By</label>
            <select
              id="filter-type"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={filterType}
              onChange={(e) => {
                setFilterType(e.target.value);
                setFilterValue(''); // Reset value when type changes
              }}
            >
              <option value="">Select Field</option>
              {filterTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="filter-value" className="block text-sm font-medium text-gray-700 mb-1">Value</label>
            {filterType && selectOptions[filterType] ? (
              <select
                id="filter-value"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={filterValue}
                onChange={(e) => setFilterValue(e.target.value)}
              >
                <option value="">Select {filterTypes.find(t => t.value === filterType)?.label}</option>
                {selectOptions[filterType].map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            ) : (
              <TextInput
                id="filter-value"
                placeholder={filterType ? `Enter ${filterTypes.find(t => t.value === filterType)?.label}...` : "Select a filter type first"}
                value={filterValue}
                onChange={(e) => setFilterValue(e.target.value)}
                disabled={!filterType}
              />
            )}
          </div>
        </div>
        
        <div className="mt-4 flex gap-2">
          <Button 
            size="sm" 
            onClick={handleClearFilters}
            color="gray"
          >
            Clear Filters
          </Button>
          <Button 
            size="sm" 
            onClick={handleApplyFilter}
            color="primary"
            disabled={!filterType || !filterValue}
          >
            Apply Filter
          </Button>
        </div>
      </div>
    );
  };

  return (
    <MainWrapper>
      <LayoutWrapper className="h-full flex flex-col p-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-xl font-semibold">NOC Tracking</h1>
          <div className="flex gap-2">
            <div className="flex">
              <TextInput
                id="search"
                type="text"
                placeholder="Global search..."
                required={false}
                value={globalFilter}
                onChange={(e) => {
                  const value = e.target.value;
                  setGlobalFilter(value);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    // When Enter is pressed, apply the search filter
                    const value = e.target.value.trim();
                    if (value) {
                      setAppliedFilters(prev => ({ ...prev, search: value }));
                    } else {
                      // If search is empty, remove search from filters
                      const { search, ...restFilters } = appliedFilters;
                      setAppliedFilters(restFilters);
                    }
                  }
                }}
                icon={Search}
                className="rounded-r-none"
              />
              <Button
                color="primary"
                className="rounded-l-none"
                onClick={() => {
                  const value = globalFilter.trim();
                  if (value) {
                    setAppliedFilters(prev => ({ ...prev, search: value }));
                  } else {
                    // If search is empty, remove search from filters
                    const { search, ...restFilters } = appliedFilters;
                    setAppliedFilters(restFilters);
                  }
                }}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
            
            <Button
              color="light"
              onClick={fetchData}
              title="Refresh data"
            >
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" />
              </svg>
            </Button>
            
            <Button
              color={Object.keys(appliedFilters).length > 0 ? "blue" : "light"}
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {Object.keys(appliedFilters).length > 0 ? 
                `Filters (${Object.keys(appliedFilters).length})` : 
                "Filters"}
            </Button>
          </div>
        </div>

        <FilterDropdowns />

        {/* Active Filters Display */}
        {Object.keys(appliedFilters).length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <span className="text-sm font-medium text-gray-700 mr-2">Active Filters:</span>
              <button 
                className="text-xs text-blue-600 hover:text-blue-800 underline"
                onClick={() => {
                  setAppliedFilters({});
                  setGlobalFilter('');
                  setFilterType('');
                  setFilterValue('');
                }}
              >
                Clear All
              </button>
            </div>
            <div className="flex flex-wrap">
              {Object.entries(appliedFilters).map(([key, value]) => (
                <div key={key} className="active-filter-badge">
                  <span className="font-medium">
                    {key === 'search' ? 'Search' : key.replace('_name', '').replace(/^\w/, c => c.toUpperCase())}:
                  </span>
                  <span className="ml-1">{value}</span>
                  <button 
                    aria-label={`Remove ${key} filter`}
                    onClick={() => {
                      const { [key]: _, ...restFilters } = appliedFilters;
                      setAppliedFilters(restFilters);
                      if (key === 'search') {
                        setGlobalFilter('');
                      }
                    }}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="relative overflow-x-auto shadow-md rounded-lg mt-2 flex-grow">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Spinner size="xl" />
            </div>
          ) : (
            <>              <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400 table-compact">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th 
                          key={header.id} 
                          className="px-3 py-2"
                          onClick={header.column.getToggleSortingHandler()}
                        >                          <div className="flex items-center text-xs">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <span className="ml-1">
                                {header.column.getIsSorted() === 'asc' ? (
                                  <ChevronUp className="h-3 w-3" />
                                ) : header.column.getIsSorted() === 'desc' ? (
                                  <ChevronDown className="h-3 w-3" />
                                ) : (
                                  <div className="h-3 w-3 opacity-30 flex flex-col">
                                    <ChevronUp className="h-1.5 w-1.5" />
                                    <ChevronDown className="h-1.5 w-1.5" />
                                  </div>
                                )}
                              </span>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (                      <tr 
                        key={row.id} 
                        className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                        onClick={() => handleRowClick(row.original.id)}
                      >{row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className="px-3 py-2">
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        ))}
                      </tr>
                    ))                  ) : (
                    <tr>
                      <td 
                        colSpan={columns.length} 
                        className="px-3 py-2 text-center text-gray-500 text-xs"
                      >
                        No records found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </>
          )}
        </div>

        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 dark:text-gray-400">
              Showing
            </span>
            <Dropdown label={pagination.pageSize.toString()}>
              {[10, 25, 50, 100].map((pageSize) => (
                <Dropdown.Item 
                  key={pageSize} 
                  onClick={() => setPagination(prev => ({ ...prev, pageSize, page: 1 }))}
                >
                  {pageSize}
                </Dropdown.Item>
              ))}
            </Dropdown>
            <span className="text-sm text-gray-700 dark:text-gray-400">
              entries of {pagination.totalItems} total
            </span>
          </div>

          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
            showIcons
          />
        </div>

        {/* Ticket Detail Modal */}
        {selectedTicketId && (
          <TicketDetailModal 
            ticketId={selectedTicketId}
            isOpen={showTicketDetail}
            onClose={() => setShowTicketDetail(false)}
            onStatusChange={handleTicketStatusChange}
          />
        )}
      </LayoutWrapper>
    </MainWrapper>
  );
}