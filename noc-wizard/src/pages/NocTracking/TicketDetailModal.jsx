import React, { useState, useEffect } from 'react';
import { Modal, Button, Spinner, TextInput, Textarea } from 'flowbite-react';
import { DateTime } from '@/components';
import { getTicketDetails } from '@/services/tracking.service';

function TicketDetailModal({ ticketId, isOpen, onClose, onStatusChange }) {
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && ticketId) {
      fetchTicketDetails();
    }
  }, [isOpen, ticketId]);
  const fetchTicketDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { success, data, error } = await getTicketDetails(ticketId);
      
      if (success && data) {
        setTicket(data?.data);
      } else {
        setError(error || 'Failed to fetch ticket details');
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error(err);
    } finally {
      setLoading(false);
    }  };

  return (
    <Modal show={isOpen} onClose={onClose} size="7xl">
      <Modal.Header>
        Ticket Details: {ticketId}
      </Modal.Header>
      <Modal.Body className="p-6">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Spinner size="xl" />
          </div>
        ) : error ? (
          <div className="text-red-600 p-4 text-center">
            <div className="text-lg font-bold mb-2">Error</div>
            <div>{error}</div>
          </div>
        ) : ticket ? (
          <div className="space-y-6">
            {/* Ticket Summary Section */}
            <div className="bg-gray-50 p-4 rounded-lg border">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Ticket ID</h3>
                  <p className="text-base font-medium">{ticket.id || ticketId}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Status</h3>
                  <p className="text-base font-medium">{ticket.status_name}</p>
                </div>
              </div>
              
              <div className="mt-4">
                <h3 className="text-sm font-semibold text-gray-600 mb-1">Summary</h3>
                <p className="text-base">{ticket.summary}</p>
              </div>
            </div>

            {/* Basic Information */}
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Company</h3>
                  <p>{ticket.company_name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Site</h3>
                  <p>{ticket.site_name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Serial Number</h3>
                  <p>{ticket.serial_number || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Priority</h3>
                  <p>{ticket.priority_name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Type</h3>
                  <p>{ticket.type_name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Sub Type</h3>
                  <p>{ticket.sub_type_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Work Type</h3>
                  <p>{ticket.work_type_name || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Item</h3>
                  <p>{ticket.item_name || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Country</h3>
                  <p>{ticket.country_name || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Ticket Details */}
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Ticket Details</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Board</h3>
                  <p>{ticket.board_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Team</h3>
                  <p>{ticket.team_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Department</h3>
                  <p>{ticket.department_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Service Location</h3>
                  <p>{ticket.service_location_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Source</h3>
                  <p>{ticket.source_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Severity</h3>
                  <p>{ticket.severity || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Impact</h3>
                  <p>{ticket.impact || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">SLA Name</h3>
                  <p>{ticket.sla_name || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">SLA Status</h3>
                  <p>{ticket.sla_status || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Agreement</h3>
                  <p>{ticket.agreement_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Created By</h3>
                  <p>{ticket.created_by || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Updated By</h3>
                  <p>{ticket.updated_by || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Contact Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Contact Name</h3>
                  <p>{ticket.contact_name || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Contact Phone</h3>
                  <p>{ticket.contact_phone_number || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Contact Email</h3>
                  <p>{ticket.contact_email_address || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Timing Information */}
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Timing Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Created</h3>
                  <p><DateTime date={ticket.created_at} format="MMM D, YYYY h:mm A" /></p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Updated</h3>
                  <p><DateTime date={ticket.updated_at} format="MMM D, YYYY h:mm A" /></p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Escalation Started</h3>
                  <p>{ticket.escalation_start_date_utc ? <DateTime date={ticket.escalation_start_date_utc} format="MMM D, YYYY h:mm A" /> : 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Respond Hours</h3>
                  <p>{ticket.responded_hours || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Resolution Plan Hours</h3>
                  <p>{ticket.resplan_hours || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Resolution Hours</h3>
                  <p>{ticket.resolution_hours || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Respond Minutes</h3>
                  <p>{ticket.respond_minutes || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Resolve Minutes</h3>
                  <p>{ticket.resolve_minutes || 'N/A'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Resolution Plan Minutes</h3>
                  <p>{ticket.res_plan_minutes || 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">In SLA</h3>
                  <p>{ticket.is_in_sla ? 'Yes' : 'No'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Escalation Level</h3>
                  <p>{ticket.escalation_level || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Status Information */}
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Status Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Status</h3>
                  <p>{ticket.status_name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Closed</h3>
                  <p>{ticket.closed_flag ? 'Yes' : 'No'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Approved</h3>
                  <p>{ticket.approved ? 'Yes' : 'No'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Has Child Ticket</h3>
                  <p>{ticket.has_child_ticket ? 'Yes' : 'No'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Has Merged Child Ticket</h3>
                  <p>{ticket.has_merged_child_ticket_flag ? 'Yes' : 'No'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-600 mb-1">Request For Change</h3>
                  <p>{ticket.request_for_change_flag ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500">No ticket data available</div>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  );
}

export default TicketDetailModal;
