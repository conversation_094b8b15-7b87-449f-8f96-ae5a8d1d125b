/* Custom styling for NOC Tracking page */
.filter-container {
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

/* Enhance table hover effects */
tr:hover {
  transition: background-color 0.15s ease;
}

/* Style the filtered cells to highlight them */
.filtered-cell {
  background-color: rgba(79, 70, 229, 0.1);
  border-radius: 4px;
  padding: 2px 4px;
}

/* Make the sorting indicators more visible */
th {
  cursor: pointer;
  user-select: none;
  font-size: 0.7rem;
  letter-spacing: 0.05em;
}

/* Compact table styling */
.table-compact th, 
.table-compact td {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

/* Badge styles for consistent appearance */
.badge {
  display: inline-block;
  min-width: 80px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Active filter badges */
.active-filter-badge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(30, 64, 175);
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.active-filter-badge:hover {
  background-color: rgba(59, 130, 246, 0.15);
}

.active-filter-badge button {
  margin-left: 0.375rem;
  font-size: 1rem;
  line-height: 1;
  cursor: pointer;
  opacity: 0.7;
}

.active-filter-badge button:hover {
  opacity: 1;
}

/* Ensure the table fills available space */
.overflow-x-auto {
  display: flex;
  flex-direction: column;
}

/* Compact date display */
.DateTime {
  font-size: 0.75rem;
  white-space: nowrap;
  font-variant-numeric: tabular-nums;
}

/* Style tooltips to show timezone info */
.DateTime-tooltip {
  font-size: 0.7rem;
  line-height: 1.3;
  min-width: 150px;
  max-width: 250px;
  padding: 4px 0;
}

.DateTime-tooltip div {
  margin-bottom: 2px;
}

/* Style the scrollbars to match the design */
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
