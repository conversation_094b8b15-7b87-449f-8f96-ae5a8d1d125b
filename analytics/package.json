{"name": "analytics-service", "version": "1.0.0", "description": "Analytics API service", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest"}, "dependencies": {"@clickhouse/client": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.2", "mongodb": "^5.3.0", "pino": "^9.6.0", "pino-http": "^10.4.0", "redis": "^5.0.1", "uuid": "^11.1.0", "zod": "^3.21.4"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.17.32", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "supertest": "^6.3.3", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6", "vitest": "^0.28.3"}}