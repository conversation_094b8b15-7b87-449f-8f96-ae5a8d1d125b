import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from './logger';
import dotenv from 'dotenv';
dotenv.config();

export interface JWTPayload {
  id: string;
  role: string;
  email: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

export const verifyToken = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    logger.warn('No token provided');
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
    req.user = decoded;
    logger.info({ userId: decoded.email }, 'Token verified successfully');
    next();
  } catch (error) {
    logger.error({ error }, 'Failed to verify token');
    return res.status(401).json({ error: 'Invalid token' });
  }
};
