import { AsyncLocalStorage } from 'async_hooks';
import pino from 'pino';
import { v4 as uuid } from 'uuid';
import { Request, Response, NextFunction } from 'express';
import dotenv from 'dotenv';
dotenv.config();

const context = new AsyncLocalStorage<Map<string, any>>();

const pinologger = pino({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
});

const logger = new Proxy(pinologger, {
  get(target, property, receiver) {
    target = context.getStore()?.get('logger') || target;
    return Reflect.get(target, property, receiver);
  },
});

const contextMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const child = pinologger.child({ requestId: uuid() });
  const store = new Map();
  store.set('logger', child);

  return context.run(store, next);
};

export {
  logger,
  contextMiddleware
}