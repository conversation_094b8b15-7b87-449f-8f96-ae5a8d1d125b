import { Request, Response, NextFunction } from 'express';
import { logger } from './logger';

function errorHandler(err: any, req: Request, res: Response, next: NextFunction) {
  console.error(err);
  logger.error({ err, req: { method: req.method, url: req.url } }, 'Error occurred');
  const status = err.status || 500;
  res.status(status).json({ error: err.message || 'Internal Server Error' });
}

export default errorHandler;
