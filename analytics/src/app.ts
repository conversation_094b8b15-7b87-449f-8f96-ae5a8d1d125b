import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { contextMiddleware } from './middleware/logger';
import { verifyToken } from './middleware/auth';
import analyticsRoutes from './routes/analytics.routes';
import errorHandler from './middleware/errorHandler';

const app = express();
app.set("trust proxy", true);
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(contextMiddleware);

app.use(verifyToken)
app.use('/apps/api/analytics', analyticsRoutes);

app.use(errorHandler);

export default app;
