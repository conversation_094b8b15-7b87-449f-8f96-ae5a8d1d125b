/**
 * Type definitions for analytics data structures
 */
interface TrafficByMonth {
  key_as_string: string;
  call_type: {
    buckets: Array<{
      key: string;
      doc_count: number;
    }>;
  };
}

interface DayEntry {
  key_as_string: string;
  date_key: string;
  line_types: {
    buckets: Array<{
      key: string;
      doc_count: number;
    }>;
  };
}

interface CountEntry {
  key_as_string: string;
  date_key: string;
  doc_count: number;
}

interface UsageEntry {
  key_as_string: string;
  doc_count: number;
  total_minutes: {
    value: number;
  };
}

interface DirectionCount {
  buckets: {
    inbound: {
        doc_count: number;
    };
    outbound: {
        doc_count: number;
    };
  };
}

interface AnalyticsData {
  section?: string;
  month_abbr?: string;
  outbound_count?: number;
  inbound_count?: number;
  day_bucket?: string;
  date_bucket?: string;
  callType?: string;
  total_rows?: number;
  total_duration?: number;
}

type DayName = 'Mon' | 'Tue' | 'Wed' | 'Thu' | 'Fri' | 'Sat' | 'Sun';

function sortByDayOfWeek(data: any, key: string): any{
  const dayOrder: DayName[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const dayIndexMap = new Map<DayName, number>(
    dayOrder.map((day, index) => [day, index])
  );

  return data.sort((a: any, b: any) => {
    return (dayIndexMap.get(a[key]) ?? 0) - (dayIndexMap.get(b[key]) ?? 0);
  });
}

function sortByDateKey(data: any[]): any[] {
  return [...data].sort((a, b) => {
    const dateA = new Date(a.date_key);
    const dateB = new Date(b.date_key);
    return dateA.getTime() - dateB.getTime();
  });
}

function sortBucketsByDocCount(buckets: Array<{ key: string; doc_count: number }>): Array<{ key: string; doc_count: number }> {
  return [...buckets].sort((a, b) => b.doc_count - a.doc_count);
}

/**
 * Transforms raw analytics data into structured aggregation objects
 * @param parsedData Array of analytics data to transform
 * @returns Structured aggregation objects
 */
export function transformAnalytics(parsedData: AnalyticsData[]): {
  agg1: {
    traffic_by_month: TrafficByMonth[];
    line_over_time: DayEntry[];
    inbound_count: CountEntry[];
    outbound_count: CountEntry[];
  };
  agg2: {
    total_usage: UsageEntry[];
  };
  agg3: {
    past_30_days: {
      direction_count: DirectionCount;
    };
  };
} {
  let agg1 = {
    traffic_by_month: [] as TrafficByMonth[],
    line_over_time: [] as DayEntry[],
    inbound_count: [] as CountEntry[],
    outbound_count: [] as CountEntry[],
  }

  let agg2 = {
    total_usage: [] as UsageEntry[]
  }

  let agg3 = {
    past_30_days: {
      direction_count: {} as DirectionCount
    }
  }

  for (let i = 0; i < parsedData.length; i++) {
    let data = parsedData[i];

    if(data?.section === 'traffic_by_month'){
      agg1.traffic_by_month.push({
        "key_as_string": data.month_abbr || '',
        "call_type": {
          "buckets": [
            {
              "key": "outbound",
              "doc_count": Number(data.outbound_count || 0)
            },
            {
              "key": "inbound",
              "doc_count": Number(data.inbound_count || 0)
            }
          ]
        }
      });
    }

    if(data?.section === 'line_over_time'){
        const dayBucket = data.day_bucket || '';
        let existingDayIndex = agg1.line_over_time.findIndex(day => day.key_as_string === dayBucket);

        if(existingDayIndex === -1) {
            const newDay: DayEntry = {
                "key_as_string": dayBucket,
                "date_key": data.date_bucket || '',
                "line_types": {
                    "buckets": [{
                        key: data.callType || '',
                        doc_count: Number(data.total_rows) || 0
                    }]
                }
            };

            agg1.line_over_time.push(newDay);
        } else {
            const updatedBuckets = [
              ...agg1.line_over_time[existingDayIndex].line_types.buckets,
              {
                key: data.callType || '',
                doc_count: Number(data.total_rows) || 0
              }
            ];

            agg1.line_over_time[existingDayIndex] = {
                "key_as_string": dayBucket,
                "date_key": data.date_bucket || '',
                "line_types": {
                    "buckets": sortBucketsByDocCount(updatedBuckets)
                }
            };
        }
    }

    if(data?.section === 'daily_direction_type'){
        if(data?.callType === 'inbound'){
            agg1.inbound_count.push({
                "key_as_string": data.day_bucket || '',
                "date_key": data.date_bucket || '',
                "doc_count": Number(data.total_rows || 0)
            });
        }
        
        if(data?.callType === 'outbound'){
            agg1.outbound_count.push({
                "key_as_string": data.day_bucket || '',
                "date_key": data.date_bucket || '',
                "doc_count": Number(data.total_rows || 0)
            });
        }
    }

    if(data?.section === 'daily_call_type'){
        if(data?.callType === 'inbound'){
            agg1.inbound_count.push({
                "key_as_string": data.day_bucket || '',
                "date_key": data.date_bucket || '',
                "doc_count": Number(data.total_rows || 0)
            });
        }
        
        if(data?.callType === 'outbound'){
            agg1.outbound_count.push({
                "key_as_string": data.day_bucket || '',
                "date_key": data.date_bucket || '',
                "doc_count": Number(data.total_rows || 0)
            });
        }
    }

    if(data?.section === 'total_usage'){
        agg2.total_usage.push({
            "key_as_string": data.day_bucket || '',
            "doc_count": data.total_rows || 0,
            "total_minutes": {
                value: data.total_duration || 0
            },
        });
    }

    if(data?.section === 'past_30_days'){
        agg3.past_30_days.direction_count = {
            buckets: {
                inbound : {
                    doc_count: Number(data.inbound_count || 0)

                },
                outbound: {
                    doc_count: Number(data.outbound_count || 0)
                }
            }
        }
    }
  }

  return {
    agg1: {
      traffic_by_month: agg1.traffic_by_month,
      line_over_time: sortByDateKey(agg1.line_over_time),
      inbound_count: sortByDateKey(agg1.inbound_count),
      outbound_count: sortByDateKey(agg1.outbound_count)
    },
    agg2,
    agg3,
  };
}

export function numbersTypeTransform(parsedData: {}) {
  const agg4 = {
    line_types: {
      buckets: [] as {
        key: string;
        doc_count: number;
      }[]
    }
  }

  for(let [key, value] of Object.entries(parsedData)){
    agg4.line_types.buckets.push({
      key: key,
      doc_count: Number(value)
    });
  }

  agg4.line_types.buckets = sortBucketsByDocCount(agg4.line_types.buckets);

  return agg4;
}