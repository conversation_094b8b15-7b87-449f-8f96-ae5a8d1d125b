import dotenv from 'dotenv';
import { envSchema, type EnvConfig } from './env.validation';

dotenv.config();

function validateConfig(config: Record<string, unknown>): EnvConfig {
    const result = envSchema.safeParse(config);

    if (!result.success) {
        console.error('❌ Invalid environment variables:', result.error.format());
        throw new Error('Invalid environment variables');
    }

    return result.data;
}

const config = validateConfig(process.env);

export default config;
