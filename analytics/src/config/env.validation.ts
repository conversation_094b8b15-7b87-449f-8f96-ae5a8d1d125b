import { z } from 'zod';

export const envSchema = z.object({
  MONGODB_URI: z.string(),
  
  REDIS_SERVER: z.string(),
  REDIS_PORT: z.string().transform(Number),
  
  CLICKHOUSE_HOST: z.string(),
  CLICKHOUSE_USER: z.string(),
  CLICKHOUSE_PASSWORD: z.string(),
  
  PORT: z.string().transform(Number).default("3000"),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  JWT_SECRET: z.string(),
});

export type EnvConfig = z.infer<typeof envSchema>;
