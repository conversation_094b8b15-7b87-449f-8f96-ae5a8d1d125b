import app from './app';
import config from './config';
import mongoRepo from './services/mongo.repository';
import clickhouseRepository from './services/clickhouse.repository';

async function start() {
  try {
    await mongoRepo.connect();
    await clickhouseRepository.connect();    console.log('Connected to MongoDB');
    app.listen(config.PORT, () => {
      console.log(`Analytics service listening on port ${config.PORT}`);
    });
  } catch (err) {
    console.error('Failed to start analytics service', err);
    process.exit(1);
  }
}

start();
