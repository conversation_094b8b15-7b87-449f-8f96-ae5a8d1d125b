import { createClient } from 'redis';
import config from '../config';

class RedisRepository {
    private client;

    constructor() {
        this.client = createClient({
            url: `redis://${config.REDIS_SERVER}:${config.REDIS_PORT}`
        });

        this.client.on('error', err => console.error('Redis Client Error', err));
        this.client.connect();
    }

    async get(key: string) {
        try {
            const value = await this.client.get(key);
            return value ? JSON.parse(value) : null;
        } catch (err) {
            console.error('Redis get error:', err);
            return null;
        }
    }

    async set(key: string, value: any, expireInSeconds = 3600) {
        try {
            await this.client.set(key, JSON.stringify(value), {
                EX: expireInSeconds
            });
        } catch (err) {
            console.error('Redis set error:', err);
        }
    }

    async del(key: string) {
        try {
            await this.client.del(key);
            return true;
        } catch (err) {
            console.error('Redis delete error:', err);
            return false;
        }
    }

    async clearAllDashboardKeys() {
        try {
            const keys = await this.client.keys('dashboard:*');
            if (keys.length > 0) {
                await this.client.del(keys);
                return true;
            }
            return false;
        } catch (err) {
            console.error('Redis clear dashboard keys error:', err);
            return false;
        }
    }

    getDashboardKey(userId: string, isSuperAdmin: boolean) {
        return isSuperAdmin ? 'dashboard:superadmin' : `dashboard:user:${userId}`;
    }
}

export default new RedisRepository();
