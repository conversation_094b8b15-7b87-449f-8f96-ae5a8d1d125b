import { Router, Request, Response, NextFunction } from 'express';
import { z, ZodError } from 'zod';
import clickhouseRepo from '../services/clickhouse.repository';
import mongoRepo from '../services/mongo.repository';
import redisRepository from '../services/redis.repository';
import { MetricsQueryParams, LastValueQueryParams } from '../types';
import {transformAnalytics, numbersTypeTransform} from '../utils/transformAnalytics';

type ColumnValueMapper = (value: string) => string;

const COLUMN_DATA_TYPES_MAPPING: Record<string, ColumnValueMapper> = {
  timestamp: (value: string) => value,
  signalStrength: (value: string) => `toInt8(${value})`,
  cpuTempCelsius: (value: string) => `toFloat32(${value})`,
  lanLatencyMs: (value: string) => `toFloat32(${value})`,
  devicePowerW: (value: string) => `toFloat32(${value})`,
  uptimeSeconds: (value: string) => `toUInt32(${value})`,
  apiOnline: (value: string) => `toBool(${value})`,
  registered: (value: string) => `toBool(${value})`,
}

const router = Router();

// router.post('/device/metrics', async (req: Request, res: Response, next: NextFunction) => {
//   try {
//     const schema = z.object({ deviceId: z.string(), startTime: z.string(), pageSize: z.number() });
//     const { startTime, deviceId, pageSize } = schema.parse(req.body);
//     const result = await clickhouseRepo.queryRows(`SELECT * FROM epikbox_data.epik_box_metrics WHERE serialNumber = '${deviceId}' AND timestamp >= '${startTime}'ORDER BY timestamp ASC LIMIT ${pageSize}`);
//     return res.json(result);
//   } catch (err) {
//     if (err instanceof ZodError) {
//       res.status(400).json({ error: err.errors });
//     } else {
//       next(err);
//     }
//   }
// });

router.post('/device/metrics', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const schema = z.object({
      deviceId: z.string(),
      startTime: z.string(),
      endTime: z.string(),
      columns: z.array(z.string()),
      compareColumns: z.array(z.string()).optional(),
      whereConditions: z.array(
        z.object({
          column: z.string(),
          value: z.union([z.string(), z.number(), z.boolean()])
        })
      ).optional()
    });

    const validatedData = schema.parse(req.body);
    
    const params: MetricsQueryParams = {
      serialNumber: validatedData.deviceId,
      startTimestamp: validatedData.startTime,
      endTimestamp: validatedData.endTime,
      columns: validatedData.columns,
      compareColumns: validatedData.compareColumns,
      whereConditions: validatedData.whereConditions
    };

    const result = await clickhouseRepo.getMetricsData(params);
    return res.json(result);
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      next(err);
    }
  }
});

router.post('/device/last-value', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const schema = z.object({
      deviceId: z.string(),
      columns: z.array(z.string())
    });

    const validatedData = schema.parse(req.body);
    
    const params: LastValueQueryParams = {
      serialNumber: validatedData.deviceId,
      columns: validatedData.columns
    };

    const result = await clickhouseRepo.getLastUnchangedValue(params);
    return res.json(result);
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      next(err);
    }
  }
});

router.post('/device/all-data', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const schema = z.object({
      deviceId: z.string(),
      pageSize: z.number().int().positive(),
      sortField: z.string().optional(),
      sortDirection: z.enum(['asc', 'desc']).optional(),
      page: z.number().int().positive(),
      filters: z.record(z.string()).optional()
    });
    
    const { 
      deviceId, 
      pageSize, 
      sortField = 'timestamp', 
      sortDirection = 'DESC',
      page = 1,
      filters
    } = schema.parse(req.body);

    const offset = (page - 1) * pageSize;
    const orderByClause = `ORDER BY ${sortField} ${sortDirection}`;
    const filterClause = filters && Object.keys(filters)?.length 
      ? `AND ${Object.keys(filters)
          .map(key => {
            const mappingFn = COLUMN_DATA_TYPES_MAPPING[key as string];
            return mappingFn 
              ? `${key} = ${mappingFn(filters[key])}` 
              : `${key} = '${filters[key]}'`
          })
          .join(' AND ')}`
      : '';
    let whereClause = `WHERE serialNumber = '${deviceId}'`;

    if(filters) {
      whereClause += ` ${filterClause}`;
    }

    const dataQuery = `
      SELECT *
      FROM epikbox_data.epik_box_metrics
      ${whereClause}
      ${orderByClause}
      LIMIT ${pageSize}
      OFFSET ${offset};  
    `;

    const countQuery = `
      SELECT count(*) as count
      FROM epikbox_data.epik_box_metrics
      ${whereClause};
    `;

    const [data, [countResult]] = await Promise.all([
      clickhouseRepo.queryRows(dataQuery),
      clickhouseRepo.queryRows(countQuery)
    ]);

    const totalItems = parseInt(countResult.count, 10);

    return res.json({ 
      data, 
      totalCount: totalItems,
      totalPages: Math.ceil(totalItems / 10),
    });
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      next(err);
    }
  }
});

router.get('/mongo', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const schema = z.object({ collection: z.string() });
    const { collection } = schema.parse(req.query);
    const docs: any[] = [];
    for await (const doc of mongoRepo.collectionStream(collection)) {
      docs.push(doc);
    }
    res.json(docs);
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      next(err);
    }
  }
});

router.get('/dashboard', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = await mongoRepo.getUserById(req?.user?.id as string);

    if (!user || user?.role === "restbin" || user?.role === "epikBox") {
      return res.status(404).json({ error: 'User not found' });
    }

    const isSuperAdmin = user.role === 'superAdmin';
    const cacheKey = redisRepository.getDashboardKey(user._id, isSuperAdmin);

    const cachedData = await redisRepository.get(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }

    const userCompanies = await mongoRepo.getCompaniesByUserId(user._id);

    const [result, data, boxDetails] = await Promise.all([
      clickhouseRepo.getCdrSummary(userCompanies), 
      mongoRepo.getNumberCountsByType(userCompanies), 
      mongoRepo.getEpikBoxesDetails(user._id)
    ]);

    const responseData = {...transformAnalytics(result), agg4: numbersTypeTransform(data), boxDetails};

    await redisRepository.set(cacheKey, responseData);

    return res.json(responseData);
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      try {
        const cacheKey = redisRepository.getDashboardKey(req?.user?.id as string, req?.user?.role === 'superAdmin');
        const cachedData = await redisRepository.get(cacheKey);
        if (cachedData) {
          return res.json(cachedData);
        }
      } catch (cacheErr) {
      }
      next(err);
    }
  }
});

router.get('/clear-cache', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = await mongoRepo.getUserById(req?.user?.id as string);

    if (user.role !== 'superAdmin') {
      return res.status(403).json({ error: 'Only superAdmin can clear all cache' });
    }

    const cleared = await redisRepository.clearAllDashboardKeys();
    if (!cleared) {
      return res.status(500).json({ error: 'Failed to clear cache' });
    }

    return res.json({ message: 'All dashboard cache cleared successfully' });
  } catch (err: any) {
    if (err instanceof ZodError) {
      res.status(400).json({ error: err.errors });
    } else {
      next(err);
    }
  }
});

export default router;
