import { BaseDocument } from '@/types/mongodb';
import { FastifyInstance } from 'fastify';
import { BaseModel } from './base.model';
import { ObjectType, Field, ID, InputType } from 'type-graphql';
import { IsOptional, IsString } from 'class-validator';
import { ObjectId } from 'mongodb';

@ObjectType()
export class EpikBoxDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  serialNumber: string;

  @Field(() => String)
  vpnAddress: string;

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => Date, { nullable: true })
  lastSeen?: Date;

  @Field(() => Date, { nullable: true })
  creationDate?: Date;

  @Field(() => Date, { nullable: true })
  lastUpdated?: Date;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;
}

// Input Types
@InputType()
export class EpikBoxFilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  displayName?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  company?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  number?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  macAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  epiNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  shipingNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  imei?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sim?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdon?: string;
}

@InputType()
export class UpdateEpikBoxInput
  implements Partial<Pick<EpikBoxDocument, 'serialNumber' | 'vpnAddress' | 'status' | 'lastSeen'>>
{
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  lastSeen?: Date;
}

export class EpikBoxModel extends BaseModel<EpikBoxDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'epikboxes');
  }
}
