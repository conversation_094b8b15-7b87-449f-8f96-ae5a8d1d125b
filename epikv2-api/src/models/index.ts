import { config } from '@/config/environment';
import { createModuleLogger } from '@/utils/logger';
import mongodb from '@fastify/mongodb';
import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { EpikBoxModel, EpikBoxDocument } from './epikbox';
import { BaseModel } from './base.model';

const logger = createModuleLogger('models');

export enum ModelName {
  EPIK_BOX = 'epikboxes',
}

// Type mapping for document types
type DocumentTypeMap = {
  [ModelName.EPIK_BOX]: EpikBoxDocument;
};

export type ModelInstanceMap = {
  [K in ModelName]: BaseModel<DocumentTypeMap[K]>
};

const modelTypeMap: {
  [K in ModelName]: new (fastify: FastifyInstance) => BaseModel<DocumentTypeMap[K]>
} = {
  [ModelName.EPIK_BOX]: EpikBoxModel,
};


const modelRegistry: Partial<ModelInstanceMap> = {};

let isInitialized = false;

export function getModel<T extends ModelName>(name: T): ModelInstanceMap[T] {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  const model = modelRegistry[name] as ModelInstanceMap[T];
  if (!model) {
    const error = new Error(`Model ${name} not found in registry`);
    logger.error(error);
    throw error;
  }

  return model;
}

export function getAllModels(): ModelInstanceMap {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  return modelRegistry as ModelInstanceMap;
}

function initializeModels(fastify: FastifyInstance): void {
  if (isInitialized) {
    logger.warn('Models already initialized, skipping initialization');
    return;
  }

  if (!fastify.mongo || !fastify.mongo.db) {
    const error = new Error(
      'MongoDB not available. Register MongoDB plugin before initializing models'
    );
    logger.error(error);
    throw error;
  }

  try {
    const modelNames = Object.values(ModelName);

    modelNames.forEach(name => {
      // Get the constructor and create a new instance
      const ModelConstructor = modelTypeMap[name];
      modelRegistry[name] = new ModelConstructor(fastify);
      logger.debug(`Initialized model: ${name}`);
    });

    isInitialized = true;
    logger.info('All models initialized successfully');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize models');
    throw new Error(`Model initialization failed: ${(error as Error).message}`);
  }
}

export const mongodbPlugin = fp(async function (fastify: FastifyInstance) {
  await fastify.register(mongodb, {
    forceClose: true,
    url: config.mongoUri,
    database: 'epikFax',
    maxPoolSize: 100,
    connectTimeoutMS: 5000,
  });

  initializeModels(fastify);

  fastify.log.info('MongoDB connected and models initialized');
});

export { BaseModel } from './base.model';
export * from './epikbox';
