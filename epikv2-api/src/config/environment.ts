import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const envSchema = z.object({
  DEV_MODE: z.string().default(''),
  PORT: z.string().transform(Number).default('3000'),
  HOST: z.string().default('0.0.0.0'),
  MONGO_DB_URI: z.string().default('mongodb://localhost:27017/epikFax'),
  REDIS_SERVER: z.string().default('localhost'),
  REDIS_PORT: z.string().default('6379'),
  SECRET: z.string({ required_error: 'JWT secret is required' }),
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().default('15 minutes'),
  CORS_ORIGINS: z.string().default('http://localhost:3000,http://localhost:5173,https://wapi.epikadmin.com.epik.io,https://my.epik.io,https://wapi.epik.io,http://*.epik.io,https://*.epik.io'),
  AUTH_GRPC_PORT: z.string().default('50051'),
});

const env = envSchema.parse(process.env);

export const config = {
  devMode: [1, '1', true, 'true'].includes(env.DEV_MODE),
  port: env.PORT,
  host: env.HOST,
  mongoUri: env.MONGO_DB_URI,
  redisUri: `redis://${env.REDIS_SERVER}:${env.REDIS_PORT}`,
  secret: env.SECRET,

  bcryptRounds: env.BCRYPT_ROUNDS,
  rateLimitMax: env.RATE_LIMIT_MAX,
  rateLimitWindow: env.RATE_LIMIT_WINDOW,

  corsOrigins: env.CORS_ORIGINS.split(',').map(origin => origin.trim()),

  authGrpcUri: `auth-api-svc.default.svc.cluster.local:${env.AUTH_GRPC_PORT}`,
} as const;

export type Config = typeof config;
