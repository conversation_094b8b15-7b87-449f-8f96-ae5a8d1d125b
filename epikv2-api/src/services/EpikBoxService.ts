import { getAllModels, ModelInstanceMap } from '@/models';
import { EpikBoxDocument, EpikBoxFilterInput } from '@/models/epikbox';
import { ListEpikBoxPaginatedResult, PaginationInput } from '@/types';
import { Filter } from 'mongodb';

export interface UpdateEpikBoxData {
  serialNumber?: string;
  vpnAddress?: string;
  status?: string;
  lastSeen?: Date;
}

export class EpikBoxService {
  models: ModelInstanceMap;

  constructor(models?: ModelInstanceMap) {
    this.models = models ?? getAllModels();
  }
  async listEpikBoxes(
    filter?: EpikBoxFilterInput,
    pagination?: PaginationInput
  ): Promise<InstanceType<typeof ListEpikBoxPaginatedResult>> {
    const mongoFilter: Filter<EpikBoxDocument> = {};
    if (filter?.serialNumber) {
      mongoFilter.serialNumber = filter.serialNumber;
    }
    if (filter?.vpnAddress) {
      mongoFilter.vpnAddress = filter.vpnAddress;
    }
    if (filter?.status) {
      mongoFilter.status = filter.status;
    }

    return this.models.epikboxes.findWithPagination(mongoFilter, {
      page: pagination?.page || 1,
      pageSize: pagination?.pageSize || 20,
    });
  }

  async updateEpikBox(id: string, data: UpdateEpikBoxData): Promise<EpikBoxDocument | null> {
    return await this.models.epikboxes.update(id, data);
  }
}
