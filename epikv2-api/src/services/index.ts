import { FastifyInstance } from 'fastify';
import { EpikBoxService } from './EpikBoxService';
import fp from 'fastify-plugin';

export type Services = {
  epikBoxService: EpikBoxService;
};

export const createServices = (fastify: FastifyInstance) => {
  const epikBoxService = new EpikBoxService();
  const services: Services = {
    epikBoxService,
  };
  fastify.decorate('services', services);
};



export const servicesPlugin = fp(async function (fastify: FastifyInstance) {
  createServices(fastify);
});
