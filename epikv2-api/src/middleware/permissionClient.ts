import { config } from '@/config/environment';
import {
  AppError,
  ForbiddenError,
  UnauthorizedError,
  ValidationError,
} from '@/middleware/errorHandler';
import { Features, ListAccessResponse } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';
import { Client } from 'grpc-reflection-js';

const permLogger = createModuleLogger('permissionClient');

export class DynamicPermissionClient {
  private client: any = null;
  private serverAddress: string;
  private isConnected = false;
  private connectionAttempts = 0;
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000;

  constructor(serverAddress?: string) {
    this.serverAddress = serverAddress || config.authGrpcUri;
  }

  async connect(retry = true): Promise<void> {
    if (this.isConnected) {
      return;
    }

    if (this.connectionAttempts >= this.MAX_RETRIES) {
      throw new Error(`Failed to connect to gRPC service after ${this.MAX_RETRIES} attempts`);
    }

    const reflectionClient = new Client(this.serverAddress, grpc.credentials.createInsecure());

    try {
      const services = await reflectionClient.listServices();
      const permissionServiceName = services.find((service: string | void) =>
        service?.toLowerCase().includes('permissionservice')
      );
      if (!permissionServiceName) {
        throw new Error('PermissionService not found on the server');
      }

      const root = await reflectionClient.fileContainingSymbol(permissionServiceName);
      permLogger.debug(JSON.stringify(root.toJSON()));
      const packageDefinition = protoLoader.fromJSON(root.toJSON());
      const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
      const permissionService = (protoDescriptor.auth as any)?.PermissionService;
      this.client = new permissionService(this.serverAddress, grpc.credentials.createInsecure());
      this.isConnected = true;
      this.connectionAttempts = 0;
      permLogger.info(`Successfully connected to gRPC service at ${this.serverAddress}`);
    } catch (error) {
      this.connectionAttempts++;
      permLogger.error(`Failed to connect to gRPC service at ${this.serverAddress}: ${error}`);

      if (retry && this.connectionAttempts < this.MAX_RETRIES) {
        permLogger.info(
          `Retrying connection (attempt ${this.connectionAttempts + 1}/${this.MAX_RETRIES})...`
        );
        await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        return this.connect();
      }

      throw error;
    }
  }

  private async makeRequest<T>(
    method: string,
    params: Record<string, any>,
    responseField?: string
  ): Promise<T> {
    await this.connect();

    return new Promise((resolve, reject) => {
      this.client[method](params, (error: any, response: any) => {
        permLogger.debug({ method, params, response }, 'gRPC request');
        if (responseField && response[responseField]) {
          resolve(response[responseField]);
          return;
        } else if (responseField || error) {
          reject(error || 'Unable to validate');
          return false;
        }
        resolve(response);
      });
    });
  }

  async validateUserPermission(
    userId: string,
    featureKey: string,
    operation: number
  ): Promise<any> {
    return this.makeRequest<any>(
      'validateUserPermission',
      { user_id: userId, feature_key: featureKey, operation },
      'has_permission'
    );
  }

  async validateCompanyAccess(userId: string, companyId: string): Promise<Boolean> {
    return this.makeRequest<any>(
      'validateCompanyAccess',
      { user_id: userId, company_id: companyId },
      'has_access'
    );
  }

  async validateEnterpriseAccess(userId: string, enterpriseId: string): Promise<Boolean> {
    return this.makeRequest<any>(
      'validateEnterpriseAccess',
      { user_id: userId, enterprise_id: enterpriseId },
      'has_access'
    );
  }

  async listAllowedCompanies(userId: string): Promise<any> {
    return this.makeRequest<any>('ListUserAccessableCompaniesWithEnterprises', { user_id: userId });
  }

  async listAllowedEnterprises(userId: string): Promise<any> {
    return this.makeRequest<any>('ListUserAccessableEnterprises', { user_id: userId });
  }
}

const permissionClient = new DynamicPermissionClient();

async function safeAuthOperation<T = Boolean>(
  operation: () => Promise<T>,
  context: string
): Promise<T> {
  try {
    return await operation();
  } catch (_err) {
    permLogger.error({ _err, context }, `Failed to ${context}`);
    return false as T;
  }
}

export const permissionPlugin = fp(async function (fastify: FastifyInstance) {
  try {
    await permissionClient.connect();
  } catch (error) {
    fastify.log.warn('Permission service not available, will retry on first request');
  }

  fastify.decorate(
    'requirePermission',
    async (request: FastifyRequest, featureKey: string, operation: number): Promise<Boolean> => {
      const res = await safeAuthOperation(async () => {
        return await permissionClient.validateUserPermission(
          request.user.id,
          featureKey,
          operation
        );
      }, 'validate permission');
      return res;
    }
  );

  fastify.decorate('requirePermissionHook', (featureKey: Features, operation: number) => {
    return async (request: FastifyRequest, _: FastifyReply) => {
      const res = await fastify.requirePermission(request, featureKey, operation);
      if (!res) {
        throw new ForbiddenError(`Missing required permission: ${featureKey}`);
      }
    };
  });

  fastify.decorate(
    'requireCompanyAccess',
    async (request: FastifyRequest, companyIdParam: string = 'companyId'): Promise<Boolean> => {
      const companyId = companyIdParam;

      if (!companyId) {
        return false;
      }

      return await safeAuthOperation(async () => {
        const hasAccess = await permissionClient.validateCompanyAccess(request.user.id, companyId);
        return hasAccess;
      }, 'validate company access');
    }
  );

  fastify.decorate(
    'requireEnterpriseAccess',
    async (
      request: FastifyRequest,
      enterpriseIdParam: string = 'enterpriseId'
    ): Promise<Boolean> => {
      const enterpriseId = enterpriseIdParam;

      if (!enterpriseId) {
        return false;
      }

      return await safeAuthOperation(async () => {
        const hasAccess = await permissionClient.validateEnterpriseAccess(
          request.user.id,
          enterpriseId
        );
        return hasAccess;
      }, 'validate enterprise access');
    }
  );
  fastify.decorate(
    'listAllowedCompanies',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeAuthOperation(async () => {
        const allcompanies = await permissionClient.listAllowedCompanies(request.user.id);
        return {
          ids: allcompanies.ids || [],
          isAll: allcompanies.is_all ?? false,
        };
      }, 'list all companies');
    }
  );
  fastify.decorate(
    'listAllowedEnterprises',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeAuthOperation(async () => {
        const allEnterprises = await permissionClient.listAllowedEnterprises(request.user.id);
        return {
          ids: allEnterprises.ids || [],
          isAll: allEnterprises.is_all ?? false,
        };
      }, 'list all companies');
    }
  );
});
