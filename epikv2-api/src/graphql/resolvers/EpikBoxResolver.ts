import { EpikBoxDocument, EpikBoxFilterInput } from '@/models/epikbox';
import { EpikBoxService } from '@/services/EpikBoxService';
import {
  Features,
  ListEpikBoxPaginatedResult,
  PaginationInput,
  ValidateOperations,
} from '@/types/index';
import { Arg, Ctx, Query, Resolver, Authorized } from 'type-graphql';
import type { GraphqlContext } from '@/types';
import { createModuleLogger } from '@/utils/logger';
const logger = createModuleLogger('EpikBoxResolver');

@Resolver(EpikBoxDocument)
export class EpikBoxResolver {
  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => ListEpikBoxPaginatedResult)
  async ListEpikBoxes(
    @Arg('filter', () => EpikBoxFilterInput, { nullable: true }) filter: EpikBoxFilterInput,
    @Arg('pagination', () => PaginationInput, { nullable: true }) pagination: PaginationInput,
    @Ctx() { fastify, request, reply }: GraphqlContext
  ): Promise<InstanceType<typeof ListEpikBoxPaginatedResult>> {
    logger.info('ListEpikBoxes');
    logger.debug({ filter, pagination }, 'ListEpikBoxes');
    const allowed = await fastify.listAllowedCompanies(request);
    logger.info(allowed.ids);
    return fastify.services.epikBoxService.listEpikBoxes(filter, pagination);
  }
}
