# Implementation Steps

1. Create the new directory structure:
   ```bash
   mkdir -p auth-api/cmd/{api,grpc,migrate}
   mkdir -p auth-api/internal/api/{handlers,middleware}
   mkdir -p auth-api/internal/common/{errors,logger,utils}
   mkdir -p auth-api/internal/db/{models,repository}
   mkdir -p auth-api/internal/graph/{generated,resolvers,schema}
   mkdir -p auth-api/internal/grpc/{server,client}
   mkdir -p auth-api/internal/services
   mkdir -p auth-api/pkg/httputil
   mkdir -p auth-api/proto
   mkdir -p auth-api/migrations
   mkdir -p auth-api/tests/{integration,e2e}
   ```

2. Move and refactor existing files:
   - Move main.go to cmd/api/main.go
   - Move grpc.go to cmd/grpc/main.go
   - Create a new migration entry point in cmd/migrate/main.go
   - Move router files to internal/api/handlers/
   - Extract middleware to internal/api/middleware/
   - Move error handling to internal/common/errors/
   - Keep config in internal/config/
   - Keep models in internal/db/models/
   - Keep repositories in internal/db/repository/
   - Organize GraphQL files in internal/graph/
   - Move gRPC server implementation to internal/grpc/server/
   - Keep services in internal/services/

3. Update imports in all files to reflect the new structure