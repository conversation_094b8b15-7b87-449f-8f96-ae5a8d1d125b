import moment from 'moment';

const timeFormat = 'h:mm A';
const dateFormat = 'MMM D';
const dateYearFormat = 'MMM D, YYYY';

function validDateString(dateString) {
  return !isNaN(new Date(dateString).getTime());
}

/**
 * Formats a date string based on its relative time to now.
 *
 * dateString - GMT style date string
 *
 * Returns { display: string, tooltip: string }
 */
export const formatRelativeDate = (dateString, format = "normal") => {
  if (!validDateString(dateString)) {
    return { display: "-", tooltip: "Invalid date/time" };
  }

  const date = moment(dateString);
  const now = moment();

  const dateIsToday = now.isSame(date, 'day');
  const dateIsYesterday = moment(now).subtract(1, 'days').isSame(date, 'day');
  const diffMonths = now.diff(date, 'months');
  const diffYears = now.diff(date, 'years');

  // Display logic:
  // - Today: show time (e.g., "3:45 PM")
  // - Yesterday: show "Yesterday"
  // - Within a month: show month and day (e.g., "Apr 15")
  // - 1-11 months ago: show "X mo"
  // - 1+ years ago: show "X yr"
  let display;
  if (format === "short") {
    if (dateIsToday) {
      display = date.format(timeFormat);
    } else if (dateIsYesterday) {
      display = 'Yest.';
    } else if (diffMonths < 1) {
      display = diffYears === 0 ? date.format(dateFormat) : date.format(dateYearFormat)
    } else if (diffYears < 1) {
      display = `${diffMonths} mo`;
    } else {
      display = `${diffYears} yr`;
    }
  } else {
    if (dateIsToday) {
      display = date.format(timeFormat);
    } else if (dateIsYesterday) {
      display = 'Yesterday';
    } else if (diffMonths < 1) {
      display = diffYears === 0 ? date.format(dateFormat) : date.format(dateYearFormat)
    } else if (diffYears < 1) {
      display = `${diffMonths} month${diffMonths > 1 ? 's' : ''} ago`;
    } else {
      display = `${diffYears} year${diffYears > 1 ? 's' : ''} ago`;
    }
  }

  // Full tooltip with precise date/time information
  const fullDateTime = date.format(`${dateYearFormat} [at] ${timeFormat}`);
  const tooltip = dateIsToday ? date.fromNow() : fullDateTime;

  return { display, tooltip };
};

/**
 * Formats a date string for table display:
 * - Today: shows time (e.g., "3:45 PM")
 * - This year: shows date (e.g., "Feb 20")
 * - Previous years: shows date with year (e.g., "Feb 20, 2023")
 * @param {string} dateString - GMT style date string
 * @returns {string} Formatted date or time string
 */
export const formatTableDate = (dateString) => {
  if (!validDateString(dateString)) {
    return "-";
  }

  const date = moment(dateString);
  const now = moment();

  const isToday = now.isSame(date, 'day');
  const isCurrentYear = now.isSame(date, 'year');

  if (isToday) {
    return date.format(timeFormat);
  }

  return isCurrentYear ? date.format(dateFormat) : date.format(dateYearFormat);
};
