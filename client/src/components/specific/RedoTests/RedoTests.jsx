import React, { useState } from "react";
import { CustomModal, Refresh, Text1, MenuIcon } from "@/components";
import { useMutate } from "@/hooks";

function RedoTests({ device, btnOnly = false, refetch = () => {} }) {
  const text = "Redo tests";
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { mutateAsync } = useMutate();

  const executeTest = () => {
    mutateAsync({
      endpoint: `/qc/devices/${device.serial}/tests`,
      method: "POST",
      successMsg: "Running tests",
    }).then(() => {
      refetch();
    });
  };

  const handleClick = () => {
    // Check if device is already running a test - don't do anything
    if (device.status === 'running') {
      return;
    }

    // If device is offline, show confirmation dialog
    if (device.online === "offline") {
      setIsModalOpen(true);
    } else {
      // Otherwise, proceed with the test immediately
      executeTest();
    }
  };

  const handleConfirmTest = () => {
    setIsModalOpen(false);
    executeTest();
  };

  const renderUI = () => {
    const stroke = !device.passed ? "var(--red)" : "currentColor";
    const isDisabled = device.status === 'running';
    const disabledStroke = "#bdbcb0";
    const tooltipText = isDisabled ? "Test already running" : text;

    if (btnOnly) {
      return (
        <MenuIcon
          icon={
            <Refresh
              stroke={isDisabled ? disabledStroke : (device.online !== 'online' ? disabledStroke : stroke)}
              tooltip={tooltipText}
              onClick={handleClick}
            />
          }
        />
      );
    }

    return (
      <div
        className={`flex items-center ${isDisabled ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
        onClick={handleClick}
      >
        <MenuIcon
          icon={
            <Refresh
              stroke={isDisabled ? disabledStroke : stroke}
              tooltip={tooltipText}
            />
          }
        />
        <Text1>{text}</Text1>
      </div>
    );
  };

  return (
    <>
      {renderUI()}

      <CustomModal
        isOpen={isModalOpen}
        title="Run test"
        primaryButtonText="Run test anyway"
        secondaryButtonText="Cancel"
        onPrimaryButtonClick={handleConfirmTest}
        onSecondaryButtonClick={() => setIsModalOpen(false)}
        onClose={() => setIsModalOpen(false)}
      >
        <p className="mb-2">This device appears to be offline.</p>
      </CustomModal>
    </>
  );
}

export default RedoTests;
