import React from 'react';
import { useFetch } from '@/hooks/useFetch';
import { Toolt<PERSON>, Card } from "flowbite-react";
import "./DailyShippedDevices.css";

const instanceOptions = {
    id: "daily-id",
    override: true,
};

function pluralized(count, singular, plural) {
  return `${count == 0 ? 'No' : count} ${count == 1 ? singular : plural}`;
}

const isPreviousDate = (date) => {
  const startDate = new Date('2024-05-01');
  return date < startDate;
};

const isFutureDate = (date) => {
  const endDate = new Date('2025-05-01');
  return date >= endDate;
};

const Abbreviated = ({ word }) => {
  return (
    <div className="absolute top-0 left-0 whitespace-nowrap">
      {word[0]}
      <span className="sm:inline hidden">{word[1]}{word[2]}</span>
      <span className="xl:inline hidden">{word.slice(3)}</span>
    </div>
  );
};

const generateGradientColor = (level) => {
  if (level < 0) level = 0;
  if (level > 1) level = 1;
  const hue = 110 + level * 60;
  const lightness = 95 - level * 75;
  return `hsl(${hue}, 80%, ${lightness}%)`;
}

const ColorLegend = ({ upperBound }) => {
  const steps = 5;
  const stepSize = upperBound / (steps - 1);

  return (
    <div className="flex space-x-4">
      {[...Array(steps)].map((_, i) => {
        const level = (i * stepSize) / upperBound;
        const color = generateGradientColor(level);
        const label = i == 0 ? '1' : i * stepSize;

        return (
          <div key={i} className="flex flex-col items-center space-y-1">
            <div className="w-4 h-4 rounded-full" style={{ backgroundColor: color }}></div>
            <div className="text-xs text-gray-500">{label}</div>
          </div>
        );
      })}
    </div>
  );
};

const countToColor = (count) => {
  return generateGradientColor(count / 200);
};

const monthHeaders = [
  { name: 'May', colSpan: 4, year: 2024 },
  { name: 'Jun', colSpan: 5, year: 2024 },
  { name: 'Jul', colSpan: 4, year: 2024 },
  { name: 'Aug', colSpan: 5, year: 2024 },
  { name: 'Sep', colSpan: 4, year: 2024 },
  { name: 'Oct', colSpan: 4, year: 2024 },
  { name: 'Nov', colSpan: 5, year: 2024 },
  { name: 'Dec', colSpan: 4, year: 2024 },
  { name: 'Jan', colSpan: 4, year: 2025 },
  { name: 'Feb', colSpan: 4, year: 2025 },
  { name: 'Mar', colSpan: 5, year: 2025 },
  { name: 'Apr', colSpan: 4, year: 2025 },
];

function DailyShippedDevices() {
  const { data: shippedDevices, isLoading } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=2024-05-01&endDate=2025-05-01`,
  });

  const weekDays = ['', 'Mon', '', 'Wed', '', 'Fri', ''];
  const weeksOfYear = Array.from({ length: 53 }, (_, i) => i);

  // Get the day of the week for May 1, 2024
  const may1DayOfWeek = new Date(2024, 4, 1).getDay();
  const prevDays = may1DayOfWeek === 0 ? 6 : may1DayOfWeek - 1;

  return (
    <Card className="bg-[--chrome] border-none dark:bg-gray-800 shadow-none">
      <h3 className="text-sm text-gray-600 my-0 font-normal dark:text-gray-200">
        Daily shippable devices
      </h3>
      <table className="w-full border-separate border-spacing-x-.5 border-spacing-y-.5 xl:border-spacing-x-1 xl:border-spacing-y-1">
        <tbody>
          <tr>
            <td></td>
            {monthHeaders.map((month, idx) => (
              <td key={idx} colSpan={month.colSpan} className="text-sm text-gray-500 relative h-5">
                <div className="absolute top-0 left-0">
                  <Abbreviated word={`${month.name} ${month.year}`} />
                </div>
              </td>
            ))}
          </tr>
          {weekDays.map((day, dayIndex) => (
            <tr key={day}>
              <td className="text-right text-sm text-gray-500 relative w-4 sm:w-10">
                <div className="absolute top-0 left-0">
                  <Abbreviated word={day} />
                </div>
              </td>
              {weeksOfYear.map((week) => {
                const dayOffset = week * 7 + dayIndex - prevDays - 1;
                const baseDate = new Date(2024, 4, 1);
                baseDate.setDate(baseDate.getDate() + dayOffset);

                const dateString = baseDate.toISOString().split('T')[0];
                const count = !isLoading ? (shippedDevices?.dailyCounts?.[dateString]?.count ?? 0) : 0;
                const displayDateString = baseDate.toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric'
                });

                const tooltipText = isLoading ? '' :
                  `${pluralized(count, 'device', 'devices')} on ${displayDateString}`;

                const noData = isPreviousDate(baseDate) || isFutureDate(baseDate);
                const bgColor = noData ? '' : count === 0 ? 'white' : countToColor(count);

                return (
                  <td
                    key={dateString}
                    className="rounded-sm outline-offset-2"
                    style={{ backgroundColor: bgColor }}
                  >
                    <Tooltip
                      content={noData ? displayDateString : tooltipText}
                      style="dark"
                      arrow={false}
                      animation="duration-300"
                      className="pointer-events-none bg-stone-500"
                      instanceOptions={instanceOptions}
                    >
                      <div className="rounded-sm hover:outline outline-stone-600 outline-offset-2">
                        {noData ? (
                          <svg className="w-full h-4">
                            <rect className="h-4 fill-gray-400/10" width="100%" fill="none" />
                          </svg>
                        ) : (
                          <svg className="w-full h-4">
                            <rect className="h-4" width="100%" fill="none" />
                          </svg>
                        )}
                      </div>
                    </Tooltip>
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
      <div className="ml-12">
        <ColorLegend upperBound={200} />
      </div>
    </Card>
  );
}

export default DailyShippedDevices;
