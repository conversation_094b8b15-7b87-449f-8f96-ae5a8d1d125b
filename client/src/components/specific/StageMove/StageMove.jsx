import React from "react";
import {
  <PERSON>,
  ThumbsDown,
  Wren<PERSON>,
} from "lucide-react";
import {
  Archive,
  ChevronDoubleLeft,
  ChevronDoubleRight,
  CustomModal,
  Text1,
  MenuIcon,
  Tooltip1,
} from "@/components";
import { useMutate } from "@/hooks";

function StageMove({ from, target, device, btnOnly = false, refetch = () => {} }) {
  const { mutateAsync } = useMutate();
  const [alertMsg, setAlertMsg] = React.useState();

  // Check if the move action should be disabled
  const isDisabled = from === "software" && target === "hardware" && !device.passed;

  const getSuccessMsg = () => {
    switch (target) {
      case "failure":
        return "Device moved to failed bin.";
      case "repair":
        return "Device moved to repair bin.";
      case "engineering":
        return "Device moved to engineering bin.";
      default:
        return `Device moved to ${target} stage.`;
    }
  };

  const getErrorMsg = () => {
    switch (target) {
      case "failure":
        return "Cannot move device to failed bin.";
      case "repair":
        return "Cannot move device to repair bin.";
      case "engineering":
        return "Cannot move device to engineering bin.";
      default:
        return `Cannot move device to ${target} stage.`;
    }
  };

  const getText = () => {
    switch (target) {
      case "failure":
        return "Move to failed bin";
      case "repair":
        return "Move to repair bin";
      case "engineering":
        return "Move to engineering";
      case "archive":
        return "Archive";
      default:
        return `Move to ${target} stage`;
    }
  };

  const handleClick = () => {
    if (isDisabled) {
      return;
    }

    switch (target) {
      case "hardware":
        if (from === "software" && !device.passed) {
          return;
        }
        break;
      case "failure":
        setAlertMsg(
          `Are you sure you want to move ${device.serial} to failed bin?`
        );
        return;
      case "repair":
        setAlertMsg(
          `Are you sure you want to move ${device.serial} to repair bin?`
        );
        return;
      default:
        setAlertMsg();
    }

    moveToStage();
  };

  const moveToStage = () => {
    mutateAsync({
      endpoint: `/qc/stages/${target}/devices`,
      method: "PUT",
      body: { serial: device.serial },
      successMsg: getSuccessMsg(),
      errorMsg: getErrorMsg(),
    }).then(() => {
      refetch();
    }).finally(() => {
      setAlertMsg();
    });
  };

  const getIcon = () => {
    const stroke = isDisabled ? "gray" : "currentColor";

    switch (target) {
      case "software":
        return (
          <ChevronDoubleLeft size="md" stroke={stroke} />
        );
      case "hardware":
        return from === "software" ? (
          <ChevronDoubleRight size="md" stroke={stroke} />
        ) : (
          <ChevronDoubleLeft size="md" stroke={stroke} />
        );
      case "failure":
        return (
          <ThumbsDown size={20} stroke={stroke} />
        );
      case "repair":
        return (
          <Wrench size={20} stroke={stroke} />
        );
      case "engineering":
        return (
          <Rocket size={20} stroke={stroke} />
        );
      case "archive":
        return (
          <Archive stroke={stroke} />
        );
      default:
        return null;
    }
  };

  const renderUI = () => {
    const className = isDisabled ? "opacity-50" : "";

    if (btnOnly) {
      return (
        <Tooltip1 content={getText()} placement="top">
          <MenuIcon className={className} icon={getIcon()} />
        </Tooltip1>
      );
    }

    return (
      <div className="flex items-center">
        <MenuIcon className={className} icon={getIcon()} />
        <Text1 className={className}>
          {getText()}
        </Text1>
      </div>
    );
  };

  return (
    <>
      <div
        onClick={handleClick}
        style={{ pointerEvents: isDisabled ? "none" : "auto" }}
      >
        {renderUI()}
      </div>
      {alertMsg && (
        <CustomModal
          title="Move device"
          isOpen={true}
          onPrimaryButtonClick={moveToStage}
          onSecondaryButtonClick={() => setAlertMsg()}
        >
          {alertMsg}
        </CustomModal>
      )}
    </>
  );
}

export default StageMove;
