import React from 'react';
import { Globe, Boxes, Router } from 'lucide-react';
import { useStore } from '@/store';
import { Dropdown } from 'flowbite-react';

const WaffleIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="-4 -4 33 33"
    fill="currentColor"
    {...props}
  >
    <rect x="2" y="2" width="5" height="5" rx=".5" />
    <rect x="10" y="2" width="5" height="5" rx=".5" />
    <rect x="18" y="2" width="5" height="5" rx=".5" />
    <rect x="2" y="10" width="5" height="5" rx=".5" />
    <rect x="10" y="10" width="5" height="5" rx=".5" />
    <rect x="18" y="10" width="5" height="5" rx=".5" />
    <rect x="2" y="18" width="5" height="5" rx=".5" />
    <rect x="10" y="18" width="5" height="5" rx=".5" />
    <rect x="18" y="18" width="5" height="5" rx=".5" />
  </svg>
);

const WaffleButton = () => {
  return (
    <button className="inline-flex items-center justify-center rounded-md hover:bg-gray-100 p-1 transition-colors duration-100">
      <WaffleIcon className="h-6 w-6 text-logoGray" />
    </button>
  );
};

const MenuItem = ({ icon: Icon, label, href, openInNew, isCurrentApp }) => {
  return (
    <div className="px-0.5">
      <a
        href={isCurrentApp ? '#' : href}
        target={openInNew && !isCurrentApp ? "_blank" : "_self"}
        rel={openInNew ? "noopener noreferrer" : undefined}
        className={`block p-2 rounded-md no-underline cursor-pointer hover:bg-gray-100 transition-colors duration-200 ${
          isCurrentApp ? 'text-granite-500' : 'text-logoGray'
        }`}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center p-2">
            <Icon className={`h-5 w-5 ${isCurrentApp ? 'text-granite-500' : 'text-logoGray'}`} />
          </div>
          <span className="font-medium text-sm">{label}</span>
        </div>
      </a>
    </div>
  );
};

// Menu items configuration
const menuItems = [
  {
    id: "portal",
    icon: Globe,
    label: "MyEPIK Portal",
    href: "https://my.epik.io/",
    openInNew: false,
  },
  {
    id: "qc",
    icon: Boxes,
    label: "QC Warehouse Wizard",
    permission: "qcWizardAccess",
    href: "https://my.epik.io/apps/qc",
    openInNew: true,
  },
  {
    id: "noc",
    icon: Router,
    label: "NOC Wizard",
    permission: "nocWizardAccess",
    href: "https://my.epik.io/apps/noc",
    openInNew: true,
  }
];

const AppsMenu = ({ currentApp }) => {
  const user = useStore((state) => state.user);

  return (
    <Dropdown
      label=""
      dismissOnClick={true}
      renderTrigger={WaffleButton}
      className="rounded-lg rounded-tl-none shadow-xl px-0.5"
    >
      <div className="w-64">
        <div className="p-2">
          <p className="text-xs font-bold text-logoGray select-none">My apps</p>
        </div>

        {menuItems.map((item, index) => (
          (!item.permission || true || user.data?.[item.permission]) && (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              openInNew={item.openInNew}
              isCurrentApp={currentApp === item.id}
            />
          )
        ))}
      </div>
    </Dropdown>
  );
};

export default AppsMenu;
