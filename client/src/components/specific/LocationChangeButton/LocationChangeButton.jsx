import React, { useState, useEffect, useRef } from 'react';
import {
  CustomModal,
  FriendlySerialNumber,
  RackIcon,
  Tooltip1,
  Text1,
  MenuIcon,
} from '@/components';
import { useMutate } from '@/hooks';

function LocationChangeButton({
  device,
  onLocationUpdated = () => {},
  btnOnly = false,
  closeDropdown, // Accept the closeDropdown callback from the parent
}) {
  const { mutateAsync } = useMutate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [serial, setSerial] = useState('');
  const [location, setLocation] = useState('');
  const [serialError, setSerialError] = useState('');
  const [locationError, setLocationError] = useState('');
  const serialInputRef = useRef(null);
  const locationInputRef = useRef(null);

  const handleOpenModal = (e) => {
    e.stopPropagation(); // Prevent event bubbling

    // Delay opening the modal to allow the dropdown to close
    setTimeout(() => {
      if (typeof closeDropdown === 'function') {
        closeDropdown();
      }
      setIsModalOpen(true);
      setSerialError('');
      setLocationError('');
      setLocation('');
      if (device) {
        setSerial(device);
      } else {
        setSerial('');
      }
      // Focus the location input after modal opens
      setTimeout(() => {
        locationInputRef.current?.focus();
      }, 100);
    }, 0);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSerial('');
    setLocation('');
    setSerialError('');
    setLocationError('');
  };

  const isValidSerial = (value) => {
    if (!value) return false;
    return (value.startsWith('20') && value.length === 10) || value.length === 12;
  };

  const isValidLocation = (value) => {
    return /^[A-Z][0-9]{2} [A-Z][0-9]$/.test(value);
  };

  const updateLocation = async () => {
    if (!isValidSerial(serial)) {
      setSerialError('Invalid serial number');
      return;
    }
    if (!isValidLocation(location)) {
      setLocationError('Invalid location format');
      return;
    }
    try {
      const response = await mutateAsync({
        endpoint: `/qc/devices/${serial}/location/${location}`,
        method: 'PUT',
      });
      if (response.error) {
        throw new Error(response.error);
      }
      handleCloseModal();
      onLocationUpdated();
    } catch (err) {
      if (err.message === 'Device not found') {
        setSerialError(`Device not found`);
        setSerial('');
        serialInputRef.current?.focus();
      } else {
        setLocationError(err.message || 'An error occurred while updating the location');
      }
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      locationInputRef.current?.focus();
    }
  }, [isModalOpen]);

  useEffect(() => {
    if (device) {
      setSerial(device);
    }
  }, [device]);

  useEffect(() => {
    if (isValidLocation(location)) {
      setLocationError('');
      serialInputRef.current?.focus();
    }
  }, [location]);

  useEffect(() => {
    if (isValidSerial(serial) && isValidLocation(location)) {
      updateLocation();
    }
  }, [serial]);

  const handleSerialInputChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    setSerial(value);
    setSerialError('');
    if (value.length === 12 && value.startsWith('20')) {
      setSerialError('Invalid 12-digit serial number format');
    } else if (value.length > 12) {
      setSerialError('Serial number cannot exceed 12 digits');
    }
  };

  const handleLocationInputChange = (e) => {
    let value = e.target.value.toUpperCase();
    value = value.replace(/\s+/g, '');
    if (value.length >= 3) {
      value = value.slice(0, 3) + ' ' + value.slice(3);
    }
    value = value.replace(/\s+/g, ' ').trim();
    setLocation(value);
    setLocationError('');
  };

  const renderButton = () => {
    return (
      <Tooltip1 content={btnOnly ? "Update device location" : ""} placement="bottom-left">
        <div className="flex items-center cursor-pointer">
          <MenuIcon icon={<RackIcon />} />
          {btnOnly || <Text1>Rack location...</Text1>}
        </div>
      </Tooltip1>
    );
  };

  return (
    <div>
      {/* Wrap the button click handler to also stop propagation and close the dropdown */}
      <div onClick={handleOpenModal}>
        {renderButton()}
      </div>
      {isModalOpen && (
        <CustomModal
          title="Update rack location"
          size="2xl"
          isOpen={true}
          onClose={handleCloseModal}
          primaryButtonText="Update"
          onPrimaryButtonClick={updateLocation}
          secondaryButtonText="Cancel"
          onSecondaryButtonClick={handleCloseModal}
        >
          <div className="space-y-4">
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                Rack location
              </label>
              <input
                type="text"
                id="location"
                ref={locationInputRef}
                value={location}
                onChange={handleLocationInputChange}
                className={`mt-1 block w-full rounded-md shadow-sm focus:ring focus:ring-opacity-50 ${
                  locationError
                    ? 'border-red-300 focus:border-red-300 focus:ring-red-200'
                    : 'border-gray-300 focus:border-blue-300 focus:ring-blue-200'
                }`}
                maxLength={6}
              />
              {locationError && <p className="mt-1 text-sm text-red-600">{locationError}</p>}
            </div>
            <div>
              <label htmlFor="serial" className="block text-sm font-medium text-gray-700">
                Device serial number
              </label>
              {device ? (
                <div className="py-2">
                  <FriendlySerialNumber serial={serial} />
                </div>
              ) : (
                <input
                  type="text"
                  id="serial"
                  ref={serialInputRef}
                  value={serial}
                  onChange={handleSerialInputChange}
                  className={`mt-1 block w-full rounded-md shadow-sm focus:ring focus:ring-opacity-50 ${
                    serialError
                      ? 'border-red-300 focus:border-red-300 focus:ring-red-200'
                      : 'border-gray-300 focus:border-blue-300 focus:ring-blue-200'
                  }`}
                  maxLength={12}
                />
              )}
              {serialError && <p className="mt-1 text-sm text-red-600">{serialError}</p>}
            </div>
          </div>
        </CustomModal>
      )}
    </div>
  );
}

export default LocationChangeButton;
