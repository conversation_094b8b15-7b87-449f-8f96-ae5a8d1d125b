import React, { useState, useEffect, useMemo } from 'react';
import { useFetch } from '@/hooks';

export const DeviceProductionCalendar = () => {
  const [viewMode, setViewMode] = useState('stacked'); // 'v3', 'v4', 'stacked'
  const [calendarData, setCalendarData] = useState({});
  const [hoveredDay, setHoveredDay] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Calculate date range (1 year back from today)
  const endDate = new Date();
  endDate.setHours(23, 59, 59, 999);
  const endDateStr = formatISODate(endDate);

  const startDate = new Date(endDate);
  startDate.setFullYear(startDate.getFullYear() - 1);
  startDate.setHours(0, 0, 0, 0);
  const startDateStr = formatISODate(startDate);

  // Fetch data for all devices (only used for total count in tooltip)
  const { data: allDevicesData, isLoading: isLoadingAll } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}`,
  });

  // Fetch data for Gen 3.5 devices
  const { data: gen3Data, isLoading: isLoadingGen3 } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}&gen=3`,
  });

  // Fetch data for Gen 4 devices
  const { data: gen4Data, isLoading: isLoadingGen4 } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}&gen=4`,
  });

  // Process data when all API calls complete
  useEffect(() => {
    if (isLoadingAll || isLoadingGen3 || isLoadingGen4) return;
    if (allDevicesData && gen3Data && gen4Data) {
      // Combine data from different API responses
      const combinedData = createCombinedData(gen3Data, gen4Data);
      setCalendarData(combinedData);
    }
  }, [allDevicesData, gen3Data, gen4Data, isLoadingAll, isLoadingGen3, isLoadingGen4]);

  // Helper to create combined data structure
  const createCombinedData = (gen3Data, gen4Data) => {
    const combinedData = {};

    // Process all dates in the range
    const currentDate = new Date(startDate);
    const endDateObj = new Date(endDate);

    while (currentDate <= endDateObj) {
      const dateStr = formatISODate(currentDate);

      // Get counts from API data or default to 0
      const gen3Count = gen3Data?.dailyCounts?.[dateStr]?.count || 0;
      const gen4Count = gen4Data?.dailyCounts?.[dateStr]?.count || 0;

      combinedData[dateStr] = {
        date: new Date(currentDate),
        v3: gen3Count,
        v4: gen4Count
      };

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return combinedData;
  };

  // Color scales for different device types
  const getColor = (value, type) => {
    if (type === 'v3.5') {
      if (value === 0) return '#ebedf0';
      if (value < 4) return '#b8d9ed'; // custom blue-200
      if (value < 8) return '#76b3da'; // custom blue-400
      if (value < 12) return '#4d9cd3'; // custom blue-500
      return '#4088bf'; // custom blue-600
    } else if (type === 'v4') {
      if (value === 0) return '#ebedf0';
      if (value < 4) return '#ddd6fe'; // purple-200
      if (value < 8) return '#a78bfa'; // purple-400
      if (value < 12) return '#8b5cf6'; // purple-500
      return '#7c3aed'; // purple-600
    } else { // stacked
      if (value === 0) return '#ebedf0';
      if (value < 8) return '#bbf7d0'; // green-200
      if (value < 16) return '#4ade80'; // green-400
      if (value < 24) return '#22c55e'; // green-500
      return '#16a34a'; // green-600
    }
  };

  // Get the color for a specific day based on viewMode
  const getDayColor = (day) => {
    if (!day) return '#ebedf0';

    if (viewMode === 'v3') {
      return getColor(day.v3, 'v3.5');
    } else if (viewMode === 'v4') {
      return getColor(day.v4, 'v4');
    } else {
      return getColor(day.v3 + day.v4, 'stacked');
    }
  };

  // Create tooltip content for a day
  const createTooltipContent = (day) => {
    if (!day) return null;

    // Format the date for display in a more readable format
    const displayDate = formatDisplayDate(day.date);

    return (
      <>
        <div className="pb-2 mb-2 border-b border-stone-400 whitespace-nowrap">
          {displayDate}
        </div>
        {day.v3 > 0 && (
          <p className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ backgroundColor: '#4d9cd3' }}></span>
              G3.5:
            </span>
            <span className="ml-2">{day.v3}</span>
          </p>
        )}
        {day.v4 > 0 && (
          <p className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ backgroundColor: '#8b5cf6' }}></span>
              G4:
            </span>
            <span className="ml-2">{day.v4}</span>
          </p>
        )}
        {(day.v3 + day.v4) > 0 ? (
          <p className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ backgroundColor: '#22c55e' }}></span>
              Total:
            </span>
            <span className="ml-2">{day.v3 + day.v4}</span>
          </p>
        ) : (
          <p className="whitespace-nowrap">No devices</p>
        )}
      </>
    );
  };

  // Calendar dimensions and responsive sizing
  const containerRef = React.useRef(null);

  // Start with a reasonable default width instead of 0
  const [containerWidth, setContainerWidth] = useState(4000);

  useEffect(() => {
    if (containerRef.current) {
      const updateWidth = () => {
        const newWidth = containerRef.current.clientWidth;
        setContainerWidth(newWidth);
      };

      // Initial measurement with a slight delay to ensure DOM is ready
      setTimeout(updateWidth, 0);

      // Update on resize
      window.addEventListener('resize', updateWidth);
      return () => window.removeEventListener('resize', updateWidth);
    }
  }, []);

  // Calculate dimensions based on container width
  const weekCount = 53;
  const dayCount = 7;
  const weekLabelWidth = 30;
  const monthLabelHeight = 20;

  // Maximum width for very large screens
  const maxWidth = 900;
  // Minimum width for small screens
  const minWidth = 480;

  // Determine if we should stretch or center
  const shouldStretch = containerWidth < maxWidth;
  const shouldScroll = containerWidth < minWidth;
  const shouldCenter = containerWidth >= maxWidth;

  // Calculate available width for the calendar
  const availableWidth = shouldStretch ? containerWidth - weekLabelWidth : maxWidth - weekLabelWidth;

  // Calculate cell size based on available width
  const cellSize = shouldScroll
    ? Math.max(9, Math.min(16, Math.floor((minWidth - weekLabelWidth) / weekCount) * 0.85))
    : Math.max(9, Math.min(20, Math.floor((availableWidth / weekCount) * 0.85)));

  const cellMargin = 2;
  const cellTotal = cellSize + cellMargin;

  // Calculate final dimensions
  const width = shouldScroll
    ? Math.max(minWidth, weekCount * cellTotal + weekLabelWidth)
    : shouldStretch
      ? containerWidth
      : maxWidth;

  const height = dayCount * cellTotal + monthLabelHeight;

  // Prepare weeks and months data for rendering
  const { weeks, monthLabels } = useMemo(() => {
    // Create week grid with correct date assignments
    const weeks = [];
    const now = new Date();
    const yearAgo = new Date(now);
    yearAgo.setFullYear(yearAgo.getFullYear() - 1);
    yearAgo.setHours(0, 0, 0, 0);

    // Find the start of the week containing the date from a year ago
    const dayOfWeek = yearAgo.getDay();
    yearAgo.setDate(yearAgo.getDate() - dayOfWeek);

    // Prepare month labels
    const monthLabels = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    let lastMonth = -1;
    let weekIndex = 0;

    // Generate 53 weeks of data
    while (weekIndex < weekCount) {
      const week = [];

      // For each day of the week (0-6)
      for (let day = 0; day < dayCount; day++) {
        const date = new Date(yearAgo);
        date.setDate(yearAgo.getDate() + (weekIndex * 7) + day);
        const dateStr = formatISODate(date);

        // Track month changes for labels
        const month = date.getMonth();
        if (month !== lastMonth) {
          lastMonth = month;
          monthLabels.push({
            month: months[month],
            x: weekIndex * cellTotal + weekLabelWidth,
          });
        }

        // Add day data to the week
        week.push({
          date: dateStr,
          dayData: calendarData[dateStr] || { date, v3: 0, v4: 0 }
        });
      }

      weeks.push(week);
      weekIndex++;
    }

    return { weeks, monthLabels };
  }, [calendarData, cellTotal]);

  // Handle loading state
  if (isLoadingAll || isLoadingGen3 || isLoadingGen4) {
    return (
      <div className="w-full bg-white rounded shadow p-4">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading device production data...</div>
        </div>
      </div>
    );
  }

  const daysOfWeek = ['', 'Mon', '', 'Wed', '', 'Fri', ''];

  // Handle cell mouse events - track for tooltip display
  const handleCellMouseEnter = (e, day) => {
    setHoveredDay(day);
    const rect = e.target.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top
    });
  };

  const handleCellMouseLeave = () => {
    setHoveredDay(null);
  };

  return (
    <div className="w-full rounded p-4 relative overflow-hidden bg-stone-100">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-md font-normal text-stone-600">Device production calendar</h2>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-1 text-sm relative rounded-t-md ${
              viewMode === 'stacked'
                ? 'bg-green-500 text-white'
                : 'text-gray-800 hover:text-green-500 bg-stone-200'
            }`}
            onClick={() => setViewMode('stacked')}
            style={{
              ...(viewMode !== 'stacked' && {
                paddingBottom: '4px',
                borderBottom: '3px solid #22c55e' // green-500
              })
            }}
          >
            All devices
          </button>
          <button
            className={`px-4 py-1 text-sm relative rounded-t-md ${
              viewMode === 'v3'
                ? 'bg-[#4d9cd3] text-white'
                : 'text-gray-800 hover:text-[#3572a3] bg-stone-200'
            }`}
            onClick={() => setViewMode('v3')}
            style={{
              ...(viewMode !== 'v3' && {
                paddingBottom: '4px',
                borderBottom: '3px solid #4d9cd3' // custom blue-500
              })
            }}
          >
            Gen 3.5
          </button>
          <button
            className={`px-4 py-1 text-sm relative rounded-t-md ${
              viewMode === 'v4'
                ? 'bg-purple-500 text-white '
                : 'text-gray-800 hover:text-purple-500 bg-stone-200'
            }`}
            onClick={() => setViewMode('v4')}
            style={{
              ...(viewMode !== 'v4' && {
                paddingBottom: '4px',
                borderBottom: '3px solid #8b5cf6' // purple-500
              })
            }}
          >
            Gen 4
          </button>
        </div>
      </div>

      <div className="relative">
        <div
          className={`overflow-x-auto ${shouldScroll ? '' : 'overflow-x-hidden'} ${shouldCenter ? 'flex justify-center' : ''}`}
          ref={containerRef}
          style={{ scrollbarWidth: 'thin' }}
        >
          <div
            className={`mt-2 ${shouldCenter ? 'mx-auto' : ''}`}
            style={{
              width: shouldCenter ? maxWidth : '100%',
              maxWidth: '100%',
              minWidth: shouldScroll ? minWidth : 'auto'
            }}
          >
            <svg
              width={width}
              height={height}
              className={shouldStretch ? 'w-full' : ''}
              preserveAspectRatio="xMidYMid meet"
            >
              {/* Month labels */}
              {monthLabels.map((label, index) => {
                // Calculate scaled x position for month labels
                const scaleFactor = shouldStretch ? (width - weekLabelWidth) / (weekCount * cellTotal) : 1;
                const scaledX = shouldStretch
                  ? weekLabelWidth + (label.x - weekLabelWidth) * scaleFactor
                  : label.x;

                return (
                  <text
                    key={`month-${index}`}
                    x={scaledX}
                    y={monthLabelHeight - 5}
                    fontSize="12"
                    fill="#666"
                  >
                    {label.month}
                  </text>
                );
              })}

              {/* Day of week labels */}
              {daysOfWeek.map((day, index) => (
                <text
                  key={`day-${index}`}
                  x={weekLabelWidth - 5}
                  y={(index * cellTotal) + (cellSize * 0.75) + monthLabelHeight}
                  fontSize="12"
                  fill="#666"
                  textAnchor="end"
                  alignmentBaseline="middle"
                >
                  {day}
                </text>
              ))}

              {/* Calendar cells */}
              {weeks.map((week, weekIndex) => {
                // Calculate scaled x position for cells
                const scaleFactor = shouldStretch ? (width - weekLabelWidth) / (weekCount * cellTotal) : 1;
                const cellX = shouldStretch
                  ? weekLabelWidth + (weekIndex * cellTotal) * scaleFactor
                  : (weekIndex * cellTotal) + weekLabelWidth;

                // Calculate scaled cell width
                const scaledCellSize = shouldStretch ? cellSize * scaleFactor : cellSize;

                return (
                  <React.Fragment key={`week-${weekIndex}`}>
                    {week.map((day, dayIndex) => {
                      const cellY = (dayIndex * cellTotal) + monthLabelHeight;

                      return (
                        <rect
                          key={`day-${weekIndex}-${dayIndex}`}
                          x={cellX}
                          y={cellY}
                          width={Math.max(1, scaledCellSize)}
                          height={cellSize}
                          rx={2}
                          fill={getDayColor(day.dayData)}
                          onMouseEnter={(e) => handleCellMouseEnter(e, day.dayData)}
                          onMouseLeave={handleCellMouseLeave}
                          className="cursor-pointer transition-colors duration-150"
                        />
                      );
                    })}
                  </React.Fragment>
                );
              })}
            </svg>
          </div>
        </div>

        {/* Custom Tooltip */}
        {hoveredDay && (
          <div
            className="fixed bg-stone-500 text-white rounded-md shadow-md p-3 text-sm z-50 transform -translate-y-full -translate-x-1/2"
            style={{
              left: `${tooltipPosition.x}px`,
              top: `${tooltipPosition.y - 10}px`,
            }}
          >
            {createTooltipContent(hoveredDay)}
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="flex justify-center items-center mt-4 mb-2">
        <span className="text-xs mr-2">Less</span>
        {Array.from({ length: 5 }).map((_, i) => {
          let color;
          if (viewMode === 'v3') {
            color = ['#ebedf0', '#b8d9ed', '#76b3da', '#4d9cd3', '#4088bf'][i]; // Custom Blues
          } else if (viewMode === 'v4') {
            color = ['#ebedf0', '#ddd6fe', '#a78bfa', '#8b5cf6', '#7c3aed'][i]; // Purples
          } else {
            color = ['#ebedf0', '#bbf7d0', '#4ade80', '#22c55e', '#16a34a'][i]; // Greens
          }

          return (
            <div
              key={i}
              className="w-3 h-3 mx-1 rounded-sm"
              style={{ backgroundColor: color }}
            ></div>
          );
        })}
        <span className="text-xs ml-2">More</span>
      </div>
    </div>
  );
};

// Helper function to format date as YYYY-MM-DD for API lookups
function formatISODate(date) {
  return date.toISOString().split('T')[0];
}

// Helper function to format date as "Month Day, Year" for display
export function formatDisplayDate(date) {
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(date).toLocaleDateString('en-US', options);
}
