import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>Circle,
} from "lucide-react";
import {
  SimCardIcon,
  FriendlySerialNumber,
} from '@/components';
import {
  RobustICCID,
} from '@/constants';
import "./DeviceHistoryModal.css";

// Section component with title
const InfoSection = ({ title, icon: Icon, children, className = "bg-stone-50"}) => {
  return (
    <div className={`${className} p-3 rounded-lg`}>
      <div className="flex items-center mb-3 pb-2 border-b border-gray-200">
        <Icon size={16} className="mr-2 text-gray-600" />
        <span className="text-gray-700 text-md">{title}</span>
      </div>
      <div className="ml-6">{children}</div>
    </div>
  );
};

// Property row component
const PropertyRow = ({ label, value, error = false }) => {
  return (
    <div className="flex items-start mb-2 w-full">
      <div className="text-sm text-gray-500 w-32 flex-shrink-0">{label}</div>
      <div className={`text-sm ${error ? 'text-red-500' : 'text-gray-700'}`}>
        {value || 'N/A'}
      </div>
    </div>
  );
};

// Test Failure Section component
const TestFailureSection = ({ device }) => {
  if (!device || device.status !== "failed" || !device.testResults) {
    return null;
  }

  // Find the failed test that caused the failure
  const failedTest = device.testResults.find(test => test.status === "failed");

  if (!failedTest) {
    return null;
  }

  return (
    <InfoSection title={"Automated checks"} icon={XCircle}>
      <PropertyRow
        label="Recent failure"
        value={
          <>
            <span>{(failedTest.label || failedTest.name)}</span>
            {failedTest.reason && (
              <span>: {failedTest.reason}</span>
            )}
          </>
        }
        error={true}
      />
    </InfoSection>
  );
};

// Main DeviceInfoSection component
const DeviceInfoSection = ({ device }) => {
  if (!device) {
    return <div>No device data available</div>;
  }

  const {
    serial,
    sims = [],
    vpnAddress,
    location,
    model,
    modelInfo = {},
    uptime,
    status
  } = device;

  return (
    <div className="p-0 space-y-3">
      {/* Test Failure Section - only shown when there's a failed test */}
      {status === "failed" && <TestFailureSection device={device} />}

      {/* SIM Cards Section */}
      <InfoSection title="SIM cards" icon={() => <SimCardIcon className="mr-2" size={18} />}>
        {sims.length === 0 ? (
          <div className="text-red-500 text-sm">No SIM cards found (in database)</div>
        ) : (
          <>
            {sims.map((sim, index) => (
              <PropertyRow
                key={index}
                label={`SIM ${index + 1}`}
                value={
                  <div className="flex items-center">
                    {sim ? (
                      <RobustICCID iccid={sim} />
                    ) : (
                      <div className="flex items-center text-red-500">
                        <AlertTriangle size={14} className="mr-1 flex-shrink-0" />
                        <span>Missing</span>
                      </div>
                    )}
                  </div>
                }
                error={!sim}
              />
            ))}
          </>
        )}
      </InfoSection>

      {/* Device Info Section */}
      <InfoSection title="Device" icon={Cpu}>
        <PropertyRow label="Serial number" value={<FriendlySerialNumber serial={serial} />} />
        <PropertyRow label="VPN address" value={vpnAddress} />
        <PropertyRow label="Model" value={model} />
        {modelInfo && (
          <>
            <PropertyRow
              label="Ports"
              value={modelInfo.ports ? `${modelInfo.ports} ports` : undefined}
            />
            <PropertyRow label="EPI type" value={modelInfo.epi} />
            <PropertyRow
              label="PRI support"
              value={modelInfo.pri ? 'Yes' : 'No'}
            />
          </>
        )}
        {uptime && <PropertyRow label="Uptime" value={uptime} />}
      </InfoSection>
    </div>
  );
};

export default DeviceInfoSection;
