import React, { useState, useEffect, useRef } from "react";
import {
  CustomModal,
  FriendlySerialNumber,
  Avatar1,
  RackIcon,
} from "@/components";
import { useMutate } from "@/hooks";
import { useStore } from "@/store";
import { APIS } from "@/constants";
import {
  Columns3,
  CheckCheck,
  CheckCircle2,
  Calendar,
  Info,
  MessageSquare,
  MinusCircle,
  Play,
  SendHorizontal as Send,
  Clock,
  XCircle,
  TriangleAlert,
} from "lucide-react";
import { toColors } from "@/constants";
import "./DeviceHistoryModal.css";
import DeviceInfoSection from "./DeviceInfoSection";

// Format time only - for when we already know the date from context
const formatTimeOnly = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
};

// Helper function to add opacity to hex color
const addOpacityToHex = (hexColor, opacity = 0.1) => {
  // Remove # if present
  const hex = hexColor.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Return rgba string with opacity
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// Time Display Component - reusable for all events
const TimeDisplay = ({ eventTime }) => {
  return (
    <span className="text-[12px] text-gray-500 flex-shrink-0 mx-2 text-nowrap">
      {formatTimeOnly(eventTime)}
    </span>
  );
};

// Format date with smart year handling
const formatDate = (date) => {
  const today = new Date();
  const isCurrentYear = date.getFullYear() === today.getFullYear();

  // Options for date formatting
  const options = {
    month: 'short',
    day: 'numeric',
  };

  // Only add year if it's not the current year
  if (!isCurrentYear) {
    options.year = 'numeric';
  }

  return date.toLocaleDateString('en-US', options);
};

const DateGroup = ({ date }) => {
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  const isToday = date.getDate() === today.getDate() &&
                  date.getMonth() === today.getMonth() &&
                  date.getFullYear() === today.getFullYear();

  const isYesterday = date.getDate() === yesterday.getDate() &&
                      date.getMonth() === yesterday.getMonth() &&
                      date.getFullYear() === yesterday.getFullYear();

  const isCurrentYear = date.getFullYear() === today.getFullYear();

  let displayDate;
  if (isToday) {
    displayDate = 'Today';
  } else if (isYesterday) {
    displayDate = 'Yesterday';
  } else {
    // Format based on whether it's the current year or not
    displayDate = date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: isCurrentYear ? undefined : 'numeric' // Omit the year if it's the current year
    });
  }

  return (
    <div className="flex items-center pt-1 pb-2">
      <div className="h-px bg-gray-200 flex-grow"></div>
      <div className="inline-block px-2.5 py-1 rounded-full border border-1 border-gray-200 bg-white">
        <div className="text-[13px] text-gray-500 font-medium flex items-center">
          <Calendar size={12} className="mr-1" />
          {displayDate}
        </div>
      </div>
      <div className="h-px bg-gray-200 flex-grow"></div>
    </div>
  );
};

// Event Content Component - handles different event types
const EventContent = ({ event }) => {
  const { type, data, user } = event;
  const userName = user?.name || user?.email || 'System';
  const userColors = toColors(userName);

  // Special styling for notes as chat bubbles
  if (type === 'note' || (type === 'log' && data?.level === 'note')) {
    return (
      <div>
        {data?.message && (
          <div
            className="font-normal rounded-2xl text-sm inline-block max-w-96 px-3 py-1.5"
            style={{
              color: userColors.dark,
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              backgroundColor: addOpacityToHex(userColors.light, .5),
            }}
          >
            {decodeURIComponent(data.message)}
          </div>
        )}
      </div>
    );
  }

  // Handle other event types as before
  switch (type) {
    case 'inspection':
      return (
        <div className="break-words max-w-full">
          <div className="font-medium">Hardware inspection {data?.passed ? 'passed' : 'failed'}</div>
          {data?.checks && (
            <div className="text-sm text-gray-500">
              {Object.entries(data.checks).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-1">
                  {value ? (
                    <CheckCircle2 size={14} className="text-green-500 flex-shrink-0" />
                  ) : data?.passed ? (
                    // If inspection passed but this item failed, mark as N/A
                    <MinusCircle size={14} className="text-gray-400 flex-shrink-0" />
                  ) : (
                    <XCircle size={14} className="text-red-500 flex-shrink-0" />
                  )}
                  <span className="break-words">
                    {getInspectionLabel(key)}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      );

    case 'stage':
      return (
        <div className="font-medium break-words max-w-full">Moved to {friendlyStageName(data?.stage)}</div>
      );

    case 'location':
      return (
        <div className="font-medium break-words max-w-full">Rack location changed to {data?.location}</div>
      );

    case 'test':
      return (
        <div className="break-words max-w-full">
          <div className="font-medium">
            Software test {data?.attempt} {data?.status === 'passed' ? 'passed' : data?.status}
          </div>
          {data?.results && data.results.length > 0 && (
            <div className="text-sm text-gray-500 mt-1">
              {data.results.map((result) => (
                <div key={result._id} className="flex items-start space-x-1 mt-1">
                  {result.passed ? (
                    <CheckCircle2 size={14} className="text-green-500 mt-1 flex-shrink-0" />
                  ) : (
                    <XCircle size={14} className="text-red-500 mt-1 flex-shrink-0" />
                  )}
                  <div className="break-words max-w-96">
                    <div>{stepNameOf(result.name)}</div>


                    {result.reason && (
                      <div className="text-xs text-gray-400">{result.reason}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      );

    case 'log':
      return (
        <div className="break-words max-w-full">
          {data?.message && (
            <div className="font-normal break-words">
              {decodeURIComponent(data.message)}
            </div>
          )}
        </div>
      );

    default:
      return <div className="font-medium break-words max-w-full">{type} event</div>;
  }
};

const TimelineEvent = ({ event, prevEvent }) => {
  const { eventTime, user } = event;

  // First, normalize the user information - critical for System user
  const userName = user?.name || user?.email || 'System';
  const userId = user?._id || 'system-user'; // Use a consistent ID for System when missing
  const userColors = toColors(userName);

  // For the previous event, also normalize the user information
  const prevUserName = prevEvent?.user?.name || prevEvent?.user?.email || 'System';
  const prevUserId = prevEvent?.user?._id || 'system-user';

  const isSameUser = prevEvent &&
  (userId === prevUserId || (userName === 'System' && prevUserName === 'System'));

  // Define if they're on the same day
  const isSameDay = prevEvent &&
    new Date(prevEvent.eventTime).toDateString() === new Date(eventTime).toDateString();

  // Only hide avatar if both conditions are true
  const shouldHideAvatar = isSameUser && isSameDay && event.type === 'log';

  return (
    <div
      className={`flex items-start px-2 rounded-lg ${event.type === 'log' ? "" : "py-0 pt-2 mb-1"} pb-1.5 ${shouldHideAvatar ? 'pl-12' : 'pl-4'}`}
      style={{
        // Hide the background color for logs, which are user notes.
        backgroundColor: event.type === 'log' ? '' : ''
      }}
    >
      {!shouldHideAvatar && (
        <Avatar1 text={userName} colors={userColors} />
      )}
      <div className="flex-1 min-w-0 w-full">
        <div className="flex justify-between items-start">
          <div className={`flex-grow mr-4 ${event.type === 'log' ? "px-2" : "p-2 pt-1.5"} overflow-hidde text-sm rounded-lg`}>
            <EventContent event={event} user={user} />
          </div>
          <div className="pt-0">
            <TimeDisplay eventTime={eventTime} />
          </div>
        </div>
      </div>
    </div>
  );
};

function DeviceHistoryModal({ isOpen, device, deviceIssues = 1, onAdd, onClose }) {
  const deviceID = device.serial;
  const [showTimeline, setShowTimeline] = useState(true);
  const { mutateAsync } = useMutate(undefined);
  const user = useStore((state) => state.user.data);
  const [deviceData, setDeviceData] = useState(null);
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(false);
  const [view, setView] = useState('loading'); // Start with loading state
  const [eventCounts, setEventCounts] = useState({
    all: 0,
    notes: 0,
    test: 0,
    location: 0,
    stage: 0,
    inspection: 0
  });
  const [notation, setNotation] = useState('');
  const [notationError, setNotationError] = useState('');
  const notationInputRef = useRef(null);
  const timelineEndRef = useRef(null);

  // Add state to track viewport height and manage responsive behavior
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  const [isCompactView, setIsCompactView] = useState(false);

  // Count timeline events by type
  const updateEventCounts = React.useCallback(() => {
    const counts = {
      all: timeline.length,
      notes: 0,
      test: 0,
      location: 0,
      stage: 0,
      inspection: 0
    };

    timeline.forEach(event => {
      if (event.type === 'log' && event.data?.level === 'note') {
        counts.notes++;
      } else if (event.type === 'note') {
        counts.notes++;
      } else if (counts[event.type] !== undefined) {
        counts[event.type]++;
      }
    });

    setEventCounts(counts);
  }, [timeline]);

  // Determine default view based on data
  const setDefaultView = (timelineData, issuesCount) => {
    // Check if there are notes
    const hasNotes = timelineData.some(event =>
      (event.type === 'log' && event.data?.level === 'note') || event.type === 'note'
    );

    if (hasNotes) {
      setView('notes');
      setShowTimeline(true);
    } else if (issuesCount > 0) {
      setView('device-info');
      setShowTimeline(false);
    } else {
      setView('all');
      setShowTimeline(true);
    }
  };

  // Fetch device history
  const fetchDeviceHistory = async () => {
    if (!deviceID) return;

    setLoading(true);
    try {
      // Try to get history from either API format
      try {
        // First try the QC format
        const response = await mutateAsync({
          endpoint: `/qc/devices/${deviceID}/history`,
          method: 'GET',
        });

        if (response.error) {
          throw new Error(response.error);
        }

        setDeviceData(response.device);
        setTimeline(response.timeline || []);

        // Set default view based on data
        setDefaultView(response.timeline || [], deviceIssues);
      } catch (err) {
        // If that fails, try the MyEpik format
        const response = await mutateAsync({
          api: APIS.MYEPIK,
          endpoint: `/v2/boxes/${deviceID}/notes`,
          method: 'GET',
        });

        if (response.error) {
          throw new Error(response.error);
        }

        // Transform the response to match our timeline format
        const transformedTimeline = response.notes.map(note => ({
          type: 'note',
          eventTime: note.created,
          data: {
            message: note.note
          },
          user: {
            _id: note.userId,
            name: note.userName || note.userEmail,
            email: note.userEmail
          }
        }));

        setDeviceData({
          id: deviceID,
          serialNumber: deviceID,
        });
        setTimeline(transformedTimeline || []);

        // Set default view based on data
        setDefaultView(transformedTimeline || [], deviceIssues);
      }

      // Scroll to bottom after a small delay to ensure the content is rendered
      setTimeout(() => {
        timelineEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 300); // Increased timeout to ensure content is fully rendered with new sorting
    } catch (err) {
      console.error('Failed to load device history:', err);
      // If there's an error and no data, default to timeline
      setView('all');
      setShowTimeline(true);
    } finally {
      setLoading(false);
    }
  };

  // Add a note
  const addNote = async () => {
    if (!notation.trim()) {
      setNotationError('Please enter a note');
      return;
    }

    try {
      let response;

      // Check which API format to use based on the success of our previous fetch
      if (timeline.some(event => event.type === 'note')) {
        // Using MyEpik format
        const formData = new FormData();
        formData.append("boxId", deviceID);
        formData.append("note", notation.trim());
        formData.append("type", "user");

        response = await mutateAsync({
          api: APIS.MYEPIK,
          endpoint: `/v2/boxes/${deviceID}/notes`,
          method: "POST",
          body: formData,
          successMsg: "Note added successfully.",
          errorMsg: "Failed to add note.",
        });
      } else {
        // Using QC format
        const encodedMessage = encodeURIComponent(notation.trim());
        response = await mutateAsync({
          endpoint: '/qc/logs',
          method: 'POST',
          body: {
            device: deviceID,
            user: user.id,
            level: 'note',
            message: encodedMessage,
          },
          successMsg: "Note added successfully.",
          errorMsg: "Failed to add note.",
        });
      }

      if (response.error) {
        throw new Error(response.error);
      }

      // Add the new note to the timeline
      const newNote = {
        type: timeline.some(event => event.type === 'note') ? 'note' : 'log',
        eventTime: new Date().toISOString(),
        data: {
          message: notation.trim(),
          level: 'note'
        },
        user: {
          _id: user.id,
          name: user.name,
          email: user.email,
        },
      };

      setTimeline(prev => [...prev, newNote]);
      setNotation('');

      // Scroll to the bottom after adding a new note - using longer timeout for new sorting order
      setTimeout(() => {
        timelineEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 300);

      if (onAdd) onAdd();
    } catch (err) {
      setNotationError(err.message || 'An error occurred while adding the note');
    }
  };

  // Add a state to track line count
  const [lineCount, setLineCount] = useState(1);
  const [messageChars, setMessageChars] = useState(0);

  // Modify handleNotationChange to count lines
  const handleNotationChange = (e) => {
    const value = e.target.value;
    setNotation(value);

    // Count the number of lines
    const lines = value.split('\n');
    setLineCount(lines.length);
    setMessageChars(value.length);
  };

  // Handle enter key in notation input
  const handleNotationKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      addNote();
    }
  };

  // Group timeline events by date
  const groupTimelineByDate = () => {
    const filtered = timeline.filter(event => {
      if (view === 'all') return true;
      if (view === 'notes') return (event.type === 'log' && event.data?.level === 'note') || event.type === 'note';
      return event.type === view;
    });

    const groups = filtered.reduce((acc, event) => {
      const date = new Date(event.eventTime);
      const dateKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;

      if (!acc[dateKey]) {
        acc[dateKey] = {
          date,
          events: []
        };
      }

      acc[dateKey].events.push(event);
      return acc;
    }, {});

    // Sort each group's events by time (oldest to newest)
    Object.values(groups).forEach(group => {
      group.events.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));
    });

    // Sort groups by date (oldest first)
    return Object.values(groups).sort((a, b) => a.date - b.date);
  };

  // Event listener to track window height
  useEffect(() => {
    const handleResize = () => {
      setWindowHeight(window.innerHeight);

      // Set compact view when height is below a certain threshold (e.g., 500px)
      // This would typically be when a keyboard appears on mobile/tablet
      setIsCompactView(window.innerHeight < 500 && !['device-info'].includes(view));
    };

    window.addEventListener('resize', handleResize);

    // Initial check
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [view]);

  // Keep your existing effects
  useEffect(() => {
    if (isOpen && deviceID) {
      fetchDeviceHistory();
    }
  }, [isOpen, deviceID]);

  // Reset to loading state when modal opens
  useEffect(() => {
    if (isOpen) {
      setView('loading');
      // REMOVED: Don't autofocus to prevent keyboard popup on tablets
    }
  }, [isOpen]);

  useEffect(() => {
    updateEventCounts();
  }, [timeline, updateEventCounts]);

  const filteredGroups = groupTimelineByDate();
  const isLoadingView = view === 'loading';

  const FilterButton = ({ type, label, icon, warning = false, count, warningClasses}) => {
    const Icon = icon;
    const isWarning = warning && count > 0;
    const displayCount = count !== undefined ? count : eventCounts[type] || 0;
    const isActive = view === type;

    return (
      <div>
        <button
          onClick={() => {
            setView(type);
            // Toggle timeline visibility based on whether device-info is selected
            setShowTimeline(type !== 'device-info');
          }}
          className={`
            flex items-center justify-center py-1 px-2.5 h-7 w-full border-b-2 rounded-t-md
            pt-1.5 hover:border-b-blue-500
            ${isActive ? 'border-b-blue-500' : 'border-b-gray-200'}
            text-xs whitespace-nowrap
          `}
        >
          <div className="flex items-center justify-center">
            {/* Force consistent font-weight spacing with an invisible bold copy */}
            <span className="sr-only font-bold">{label}</span>
            {label}
            {displayCount > 0 && (
              <span className={`
                ml-1 flex-shrink-0 text-[12px]
                ${isWarning
                  ? `inline-flex items-center justify-center ${warningClasses} w-4 h-4 rounded-full scale-90`
                  : 'text-stone-400'
                }
              `}>
                {displayCount}
              </span>
            )}
          </div>
        </button>
      </div>
    );
  };

  // Create tabs component for header
  const Tabs = (
    <div className="flex ml-4">
      <FilterButton
        type="device-info"
        label="Info"
        icon={Info}
        warning={true}
        count={deviceIssues}
        warningClasses="bg-red-500 text-white"
      />
      <FilterButton
        type="notes"
        label="Notes"
        icon={MessageSquare}
        warning={true}
        count={eventCounts.notes}
        warningClasses="bg-yellow-200 text-black"
      />
      <FilterButton type="all" label="Timeline" icon={Clock}/>
      <FilterButton type="test" label="Tests" icon={Play} />
      <FilterButton type="inspection" label="Inspections" icon={CheckCheck} />
      <FilterButton type="stage" label="Stages" icon={Columns3} />
      <FilterButton type="location" label="Locations" icon={() => <RackIcon className="scale-[.65]" />} />
    </div>
  );

  // Create custom modal content
  // Note: I'm not embedding the note input in the footer prop anymore
  // but will include it directly in the component structure
  return (
    <CustomModal
      headerSize="sm"
      title={
        <div className="flex flex-col w-full">
          <div className="flex items-center text-[1rem] font-normal pb-4">
            <span className="font-bold">Device info</span>
            <span className="text-gray-500 mx-1.5">/</span>
            <span className="">
              {device.location}
              <span className="mx-1.5 text-gray-500">/</span>
              <FriendlySerialNumber serial={deviceID} />
            </span>
          </div>
          {/* Add tabs below the title */}
          <div className="relative">
            <div className="absolute -left-6 -bottom-4">
              <div className="mt-2 -mb-1 font-normal text-black">
                {Tabs}
              </div>
            </div>
          </div>
        </div>
      }
      size="fullscreen"
      isOpen={isOpen}
      onClose={onClose}
      // Don't use the footer prop of CustomModal, we'll add our own footer
      hideFooter={true}
    >
      {/* Main container with no padding */}
      <div className={`flex flex-col ${isCompactView || "h-[calc(80vh-70px)]"} max-h-[calc(80vh-70px)] mx-auto w-[600px] overflow-hidden p-0 m-0`}>
        {/* Main content area with zero padding - conditionally hide when in compact view */}
        <div className={`${isCompactView ? 'hidden' : 'flex-grow'} h-full overflow-hidden p-0 m-0`}>
          {/* Scrollable area with no padding */}
          {showTimeline ? (
            <div className="deviceHistoryScrollBar h-full overflow-y-auto p-0 m-0 pr-1 rounded-md bg-stone-50">
              {loading || isLoadingView ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                </div>
              ) : timeline.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-500 text-sm">
                    No history found for this device
                  </div>
                </div>
              ) : filteredGroups.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-500 text-sm">
                    No {view !== 'all' ? view === 'notes' ? 'notes' : `${view} events` : 'events'} found for this device
                  </div>
                </div>
              ) : (
                <div className="w-full flex flex-col">
                  {filteredGroups.map((group, groupIndex) => (
                    <div key={`group-${groupIndex}`}>
                      <DateGroup date={group.date} />
                      {group.events.map((event, eventIndex) => (
                        <TimelineEvent
                          key={`event-${groupIndex}-${eventIndex}`}
                          event={event}
                          prevEvent={eventIndex > 0 ? group.events[eventIndex - 1] : null}
                        />
                      ))}
                    </div>
                  ))}
                  <div ref={timelineEndRef} className="h-1" />
                </div>
              )}
            </div>
          ) : (
            <div className="h-full overflow-y-auto p-0 m-0">
              <DeviceInfoSection device={device} />
            </div>
          )}
        </div>

        {/* Custom footer with note input - minimized padding */}
        {showTimeline && (
          <div className={`w-full p-[2px] ${isCompactView ? 'pt-2' : 'pt-6'} mt-auto`}>
            <div className="flex items-end max-w-[700px] mx-auto">
              <textarea
                id="notation"
                ref={notationInputRef}
                value={notation}
                onChange={handleNotationChange}
                onKeyDown={handleNotationKeyDown}
                className="block border-none resize-none text-sm bg-stone-50 bg-none w-full rounded-md focus:bg-white focus:ring-2 focus:ring-blue-500"
                rows={2}
                maxLength={500}
                lang="en"
                dir="auto"
                placeholder="Add a note..."
              />
              <button
                onClick={addNote}
                className={`p-2 h-10 w-10 ml-2 text-white rounded-lg focus:outline-none focus:ring-1 focus:ring-opacity-50 flex items-center justify-center transition-colors ${
                  notation.trim()
                    ? "bg-blue-500 hover:bg-blue-700 focus:ring-blue-500 cursor-pointer"
                    : "bg-gray-300"
                }`}
                disabled={!notation.trim()}
              >
                <Send size={20} />
              </button>
            </div>
            <div className="relative max-w-[720px] mx-auto">
              {notationError && (
                <div className="absolute bottom-0 left-2 text-sm text-red-600">{notationError}</div>
              )}
              {!notationError && lineCount <= 1 && messageChars <= 60 && !isCompactView && (
                <div className="absolute bottom-1.5 left-3 text-[13px] text-gray-400">
                  Press Enter to send, Shift+Enter for a new line
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </CustomModal>
  );
}

function stepNameOf(id) {
  switch (id) {
    case "device-active":
      return "Device firmware";
    case "device-firmware-updated":
      return "Device active";
    case "ata-firmware-updated":
      return "EPI";
    case "sim1-active":
      return "SIM 1 active";
    case "sim2-active":
      return "SIM 2 active";
    case "sim-check-failover":
      return "Failover";
    case "power-cycle":
      return "Power cycle";
    default:
      return id.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
}

function getInspectionLabel(name) {
  // Define our known mappings
  const knownLabels = {
    // Lights section
    powerLight: "Power light",
    ata1Light: "ATA 1 (power, Internet, network)",
    ata2Light: "ATA 2 (power, Internet)",
    networkLight: "Eth0/1 activity lights",

    // Battery section
    batteryPower: "Battery power",
    batteryPercent: "Battery percentage",
    powerDisconnect: "Power disconnect",

    // Case and connections section
    chasis: "Case: No bends, dents, cracks, breaks",
    mountingBrackets: "Mounting brackets: No bends, dents, cracks, breaks",

    // Port test section
    phonePort: "RJ-11"
  };

  // Return known label if it exists
  if (knownLabels[name]) {
    return knownLabels[name];
  }

  return name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
}

function friendlyStageName(stage) {
  switch (stage) {
    case 'software': return 'Software stage';
    case 'hardware': return 'Hardware stage';
    case 'shipping': return 'Shipping stage';
    case 'engineering': return 'Engineering bin';
    case 'repair': return 'Repair bin';
    case 'failure': return 'Failure bin';
    default: return stage;
  }
}

export default DeviceHistoryModal;
