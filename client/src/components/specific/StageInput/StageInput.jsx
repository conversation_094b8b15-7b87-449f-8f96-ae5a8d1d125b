import React from "react";
import { InputField, Plus } from "@/components";
import { useDebounce, useMutate, useToastr } from "@/hooks";
import { fetchWrapper } from "@/api";
import { locationRegex } from "@/regex";
import { INPUT_FIELD_TYPES } from "@/constants";

function StageInput({ stage }) {
  const toastr = useToastr();

  const serialRef = React.useRef(null);

  const [inputFieldValue, setInputFieldValue] = React.useState({});
  const [isValidLocation, setIsValidLocation] = React.useState(false);

  const { mutateAsync } = useMutate();

  const debouncedLocation = useDebounce(inputFieldValue.location);
  const debouncedSerial = useDebounce(inputFieldValue.serial);

  React.useEffect(() => {
    if (locationRegex.test(inputFieldValue.location)) {
      setIsValidLocation(true);
    } else {
      setIsValidLocation(false);
    }
  }, [inputFieldValue.location]);

  React.useEffect(() => {
    if (isValidLocation) {
      serialRef.current.focus();
    }
  }, [isValidLocation]);

  React.useEffect(() => {
    if (!debouncedSerial || !debouncedLocation) {
      return;
    }

    if (debouncedSerial.length < 10) {
      return;
    }

    const callApi = async () => {
      try {
        await fetchWrapper({ endpoint: `/qc/devices/${debouncedSerial}` });
      } catch (err) {
        toastr.error(err.message);
        return;
      }

      await mutateAsync({
        endpoint: `/qc/devices/${debouncedSerial}/location/${debouncedLocation}`,
        method: "PUT",
        suppressSuccessMsg: true,
      });
      await mutateAsync({
        endpoint: `/qc/stages/${stage}/devices`,
        method: "PUT",
        body: { serial: debouncedSerial },
        suppressSuccessMsg: true,
      });

      setInputFieldValue((prevState) => ({
        ...prevState,
        serial: "",
        location: "",
      }));

      await mutateAsync({
        endpoint: `/qc/devices/${debouncedSerial}/tests`,
        method: "POST",
        successMsg: "Running tests",
      });
    };

    callApi();
  }, [debouncedLocation, debouncedSerial]);

  const handleInputFieldChange = (event) => {
    const {
      target: { name, value },
    } = event;

    setInputFieldValue((prevState) => ({ ...prevState, [name]: value }));
  };

  return (
    <div className="flex gap-2">
      <div className="flex gap-1 items-center">
        <Plus size="sm" />
        <InputField
          placeholder="Location"
          name="location"
          type={INPUT_FIELD_TYPES.TEXT}
          value={inputFieldValue.location}
          regex={locationRegex}
          onChange={handleInputFieldChange}
          className="w-24"
        />
      </div>
      <InputField
        placeholder="Device"
        name="serial"
        type={INPUT_FIELD_TYPES.TEXT}
        value={inputFieldValue.serial}
        disabled={!isValidLocation}
        ref={serialRef}
        onChange={handleInputFieldChange}
        className="w-24"
      />
    </div>
  );
}

export default StageInput;
