import React, { useEffect } from "react";
import {
  InputField,
  FilterIcon,
  UserIcon,
 } from "@/components";
import { INPUT_FIELD_TYPES } from "@/constants";
import { useLocation } from 'react-router-dom';

const makeQueryString = ({ filter1, filter2, self }) => {
  let queryString = "";

  if (["online", "offline"].includes(filter1)) {
    queryString += `&${filter1}`;
  }

  if (["status", "serial", "user"].includes(filter1) && filter2) {
    queryString += `&${filter1}=${filter2}`;
  }

  if (self) {
    queryString += '&self=1';
  }

  return queryString;
};

function StageFilter({ className, onFilterChange }) {
  const location = useLocation();
  const [inputValues, setInputValues] = React.useState({});

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const serial = params.get('serial');

    if (serial) {
      setInputValues({ filter1: 'serial', filter2: serial });
    }
  }, [location]);

  useEffect(() => {
    const queryString = makeQueryString(inputValues);
    onFilterChange && onFilterChange({ inputValues, queryString });
  }, [inputValues]);

  const handleInputChange = (event) => {
    const { name, value, type, checked } = event.target;

    setInputValues((prevState) => {
      if (type === INPUT_FIELD_TYPES.CHECKBOX) {
        return { ...prevState, [name]: checked };
      }

      if (name === "filter1") {
        return { ...prevState, [name]: value, filter2: "" };
      }

      return { ...prevState, [name]: value };
    });
  };

  return (
    <div className={`flex gap-2 items-center pl-2 ${className}`}>
      <FilterIcon size="sm" />
      <InputField
        name="filter1"
        type={INPUT_FIELD_TYPES.SELECT}
        className="w-24"
        onChange={handleInputChange}
        value={inputValues.filter1}
      >
        <option value="all">All</option>
        <option value="online">Online</option>
        <option value="offline">Offline</option>
        <option value="status">Status</option>
        <option value="serial">Serial</option>
        {/*<option value="user">User</option>*/}
      </InputField>
      {inputValues.filter1 === "status" && (
        <InputField
          name="filter2"
          type={INPUT_FIELD_TYPES.SELECT}
          className="w-24"
          onChange={handleInputChange}
          value={inputValues.filter2}
        >
          <option selected></option>
          <option value="passed">Pass</option>
          <option value="failed">Fail</option>
          <option value="running">Running</option>
          <option value="timeout">Timeout</option>
          <option value="pending">Pending</option>
        </InputField>
      )}
      {inputValues.filter1 !== "status" && (
        <InputField
          name="filter2"
          className="w-24"
          type={INPUT_FIELD_TYPES.TEXT}
          onChange={handleInputChange}
          value={inputValues.filter2}
        />
      )}
      {/*
      <UserIcon className="w-4 h-4 ml-4" />
      <InputField
        name="self"
        type={INPUT_FIELD_TYPES.CHECKBOX}
        onChange={handleInputChange}
      />
      */}
    </div>
  );
}

export default StageFilter;
