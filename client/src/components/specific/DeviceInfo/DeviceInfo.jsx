import { Info } from "lucide-react";
import { Popover1 } from "@/components";
import { RobustICCID } from "@/constants/iccids";

// Component for rendering SIM information
const renderSim = (sims, offset) => {
  if (!sims?.[offset]) {
    return <span className="text-red-400 font-semibold">Not detected</span>;
  }

  return (
    <span className="text-stone-100">
      <RobustICCID iccid={sims[offset]} />
    </span>
  );
};

// Improved ModelInfo component handling all checks
const ModelInfo = ({ model, gen, epi, ports }) => {
  if (!model || model === "false") {
    return <div className="text-red-400 font-semibold">Unknown model</div>;
  }

  return (
    <div className="text-stone-5">
      Model {model}
      {model.search('G') === -1 ? ` (G${gen})` : ''}
      {ports && epi && (
        <span className="text-stone-50 mr-1">&nbsp;{ports}-port {epi}</span>
      )}
    </div>
  );
};

const DeviceInfo = ({ device }) => {
  if (!device) return null;

  const { model, modelInfo: mi, sims, vpnAddress } = device;

  // Check for specific errors
  const simErrors = [
    !sims?.[0] ? 'SIM 1 not detected' : null,
    !sims?.[1] ? 'SIM 2 not detected' : null
  ].filter(Boolean);

  const modelError = (!model || model === "false") ? 'Unknown model' : null;

  // Combine all errors into a single array
  const errors = [...(modelError ? [modelError] : []), ...simErrors];
  const errorCount = errors.length;
  const hasError = errorCount > 0;

  const deviceInfoContent = (
    <div className="w-72">
      <div>
        <div>
          <ModelInfo model={model} gen={mi?.gen} epi={mi?.epi} ports={mi?.ports} />
        </div>
        <div className="border-t border-white/20 my-2 pl-2" />
        <div className="text-stone-200">
          VPN IP address: <span className="text-white">{vpnAddress}</span>
        </div>
        <div className="text-stone-200">
          SIM 1: {renderSim(sims, 0)}
        </div>
        <div className="text-stone-200">
          SIM 2: {renderSim(sims, 1)}
        </div>
      </div>
    </div>
  );

  // Info icon with error count badge when there are errors
  const infoIcon = (
    <div className="relative inline-block pt-1">
      <Info size={18} className="text-stone-400" />
      {hasError && (
        <div className="absolute -top-0.5 -right-1.5 bg-red-500 text-white text-xxs rounded-full w-4 h-4 flex items-center justify-center scale-75">
          {errorCount}
        </div>
      )}
    </div>
  );

  return (
    <Popover1
      text={deviceInfoContent}
      placement="top"
      trigger="hover"
      className="max-w-xs whitespace-normal"
      button={
        <div className="ml-1 hover:opacity-100 duration-300">
          {infoIcon}
        </div>
      }
    />
  );
};

export default DeviceInfo;
