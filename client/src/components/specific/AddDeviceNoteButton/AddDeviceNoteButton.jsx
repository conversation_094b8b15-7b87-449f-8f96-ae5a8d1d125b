import React, { useState, useEffect, useRef } from 'react';
import { MessageSquare } from "lucide-react";
import {
  CustomModal,
  Tooltip1,
  Text1,
  FriendlySerialNumber,
  MenuIcon,
} from '@/components';
import { useMutate } from '@/hooks';
import { useStore } from "@/store";

function AddDeviceNoteButton({
  device,
  onNotationAdded = () => {},
  btnOnly = false,
  closeDropdown,
}) {
  const { mutateAsync } = useMutate();
  const user = useStore((state) => state.user.data);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [serial, setSerial] = useState('');
  const [notation, setNotation] = useState('');
  const [serialError, setSerialError] = useState('');
  const [notationError, setNotationError] = useState('');
  const serialInputRef = useRef(null);
  const notationInputRef = useRef(null);

  const handleOpenModal = (e) => {
    e.stopPropagation(); // Prevent event bubbling
    // Delay both closing the dropdown and opening the modal
    setTimeout(() => {
      if (typeof closeDropdown === 'function') {
        closeDropdown();
      }
      setIsModalOpen(true);
      setSerialError('');
      setNotationError('');
      setNotation('');
      if (device) {
        setSerial(device);
        // Force focus on notation input after a short delay
        setTimeout(() => {
          notationInputRef.current?.focus();
        }, 100);
      } else {
        setSerial('');
      }
    }, 0);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSerial('');
    setNotation('');
    setSerialError('');
    setNotationError('');
  };

  const isValidSerial = (value) => {
    return (value.startsWith('20') && value.length === 10) || value.length === 12;
  };

  const addNotation = async () => {
    if (!isValidSerial(serial)) {
      setSerialError('Invalid serial number');
      return;
    }

    try {
      const encodedMessage = encodeURIComponent(notation.trim());
      const response = await mutateAsync({
        endpoint: '/qc/logs',
        method: 'POST',
        body: {
          device: serial,
          user: user.id,
          level: 'note',
          message: encodedMessage,
        },
      });
      if (response.error) {
        throw new Error(response.error);
      }
      handleCloseModal();
      onNotationAdded();
    } catch (err) {
      if (err.message === 'Device not found') {
        setSerialError(`Device not found`);
        setSerial('');
        serialInputRef.current?.focus();
      } else {
        setNotationError(err.message || 'An error occurred while adding the note');
      }
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      setTimeout(() => {
        if (device) {
          notationInputRef.current?.focus();
        } else {
          serialInputRef.current?.focus();
        }
      }, 100); // Small delay to ensure modal is rendered
    }
  }, [isModalOpen, device]);

  useEffect(() => {
    if (isValidSerial(serial)) {
      setSerialError('');
      notationInputRef.current?.focus();
    }
  }, [serial]);

  useEffect(() => {
    if (device) {
      setSerial(device);
    }
  }, [device]);

  const handleSerialInputChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    setSerial(value);
    setSerialError('');
    if (value.length === 12 && value.startsWith('20')) {
      setSerialError('Invalid 12-digit serial number format');
    } else if (value.length > 12) {
      setSerialError('Serial number cannot exceed 12 digits');
    }
  };

  const handleNotationInputChange = (e) => {
    setNotation(e.target.value);
    setNotationError('');
  };

  const renderButton = () => {
    return (
      <Tooltip1 content={btnOnly ? "Add device note" : ""} placement="bottom-left">
        <div className="flex items-center">
          <MenuIcon icon={<MessageSquare size={20} />} />
          {!btnOnly && (
            <Text1>Notes...</Text1>
          )}
        </div>
      </Tooltip1>
    );
  };

  return (
    <div>
      <div onClick={handleOpenModal}>{renderButton()}</div>
      {isModalOpen && (
        <CustomModal
          title="Device notes"
          size="xl"
          isOpen={true}
          onClose={handleCloseModal}
          primaryButtonText="Add note"
          onPrimaryButtonClick={addNotation}
          secondaryButtonText="Cancel"
          onSecondaryButtonClick={handleCloseModal}
        >
          <div className="space-y-3">
            <div>
              <label htmlFor="serial" className="block text-sm font-medium text-gray-700">
                Device serial number
              </label>
              {device ? (
                <div className="py-2">
                  <FriendlySerialNumber serial={device} />
                </div>
              ) : (
              <input
                type="text"
                id="serial"
                ref={serialInputRef}
                value={serial}
                onChange={handleSerialInputChange}
                className={`mt-1 block w-full rounded-md shadow-sm focus:ring focus:ring-opacity-50 ${
                  serialError ? 'border-red-300 focus:border-red-300 focus:ring-red-200' : 'border-gray-300 focus:border-blue-300 focus:ring-blue-200'
                }`}
                maxLength={12}
              />
              )}
              {serialError && <p className="mt-1 text-sm text-red-600">{serialError}</p>}
            </div>
            <div>
              <label htmlFor="notation" className="block text-sm font-medium text-gray-700">
                Note
              </label>
              <textarea
                id="notation"
                ref={notationInputRef}
                value={notation}
                onChange={handleNotationInputChange}
                className={`mt-1 block w-full rounded-md shadow-sm focus:ring focus:ring-opacity-50 ${
                  notationError ? 'border-red-300 focus:border-red-300 focus:ring-red-200' : 'border-gray-300 focus:border-blue-300 focus:ring-blue-200'
                }`}
                rows={3}
                maxLength={500}
                lang="en"
                dir="auto"
              />
              {notationError && <p className="mt-1 text-sm text-red-600">{notationError}</p>}
            </div>
          </div>
        </CustomModal>
      )}
    </div>
  );
}

export default AddDeviceNoteButton;
