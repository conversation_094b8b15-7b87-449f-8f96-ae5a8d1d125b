import { Terminal, Text1, MenuIcon } from "@/components";

function Diagnostics({ device, btnOnly = false }) {
  const text = "Launch diagnostics";

  const handleClick = () => {
    window.open(`/boxes/${device.device}?company=${device.assignedTo}`, "_blank");
  };

  const renderUI = () => {
    const stroke = "currentColor";

    if (btnOnly) {
      return (
        <MenuIcon className="mr-1.5" icon={<Terminal stroke={stroke} tooltip={text} onClick={handleClick} />} />
      );
    }

    return (
      <div className="flex items-center" onClick={handleClick}>
        <MenuIcon icon={<Terminal stroke={stroke} />} />
        <Text1>{text}</Text1>
      </div>
    );
  };

  return renderUI();
}

export default Diagnostics;
