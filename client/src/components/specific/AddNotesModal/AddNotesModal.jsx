import React from "react";
import { CustomModal, InputField, Upload } from "@/components";
import { useMutate } from "@/hooks";
import { APIS, INPUT_FIELD_TYPES } from "@/constants";

function AddNotesModal({ isOpen, deviceID, onAdd, onClose }) {
  const { mutateAsync } = useMutate();

  const fileInput = React.useRef(null);
  const [inputFields, setInputFields] = React.useState({});

  const handleInputChange = (event) => {
    const {
      target: { type, name, value, files },
    } = event;

    if (type === "file") {
      const file0 = files[0];
      setInputFields((prevState) => ({ ...prevState, [name]: file0 }));
    } else {
      setInputFields((prevState) => ({ ...prevState, [name]: value }));
    }
  };

  const handleUploadClick = () => {
    fileInput.current.click();
  };

  const uploadNote = () => {
    const { notes, file } = inputFields;

    const formData = new FormData();
    formData.append("boxId", deviceID);
    formData.append("note", notes);
    formData.append("file", file);
    formData.append("type", "userWithAttachment");

    mutateAsync({
      api: APIS.MYEPIK,
      endpoint: `/v2/boxes/${deviceID}/notes`,
      method: "POST",
      body: formData,
      successMsg: "Notes added successfully.",
      errorMsg: "Failed to add notes.",
    });

    onAdd();
  };

  return (
    <CustomModal
      title="Add Note"
      isOpen={isOpen}
      primaryButtonText="Add"
      hideSecondaryButton
      onPrimaryButtonClick={uploadNote}
      onSecondaryButtonClick={onClose}
    >
      <InputField
        type={INPUT_FIELD_TYPES.TEXTAREA}
        name="notes"
        rows={4}
        onChange={handleInputChange}
      />
      <div className="flex justify-center pt-3">
        <Upload onClick={handleUploadClick} />
        <InputField
          type={INPUT_FIELD_TYPES.FILE}
          name="file"
          ref={fileInput}
          onChange={handleInputChange}
          className="hidden"
        />
      </div>
    </CustomModal>
  );
}

export default AddNotesModal;
