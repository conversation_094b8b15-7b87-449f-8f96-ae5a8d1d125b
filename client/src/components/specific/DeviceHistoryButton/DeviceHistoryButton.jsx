import React, { useState } from 'react';
import {
  DeviceHistoryModal,
  MenuIcon,
  Text1,
  Tooltip1,
} from '@/components';
import { useMutate } from '@/hooks';
import { Info } from 'lucide-react';

function countDeviceErrors(device) {
  // Check for specific errors
  const { model, sims } = device;
  const simErrors = [
    !sims?.[0] ? 'SIM 1 not detected' : null,
    !sims?.[1] ? 'SIM 2 not detected' : null,
  ].filter(Boolean);

  const modelError = (!model || model === "false") ? 'Unknown model' : null;

  // Combine all errors into a single array
  const errors = [...(modelError ? [modelError] : []), ...simErrors];
  const errorCount = errors.length;
  return errorCount;
}

function DeviceHistoryButton({
  device,
  btnOnly = false,
  closeDropdown, // Accept the closeDropdown callback from the parent
}) {
  const deviceSerial = device.serial;
  const { mutateAsync } = useMutate(undefined);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [historyData, setHistoryData] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);

  const handleOpenModal = (e) => {
    e.stopPropagation(); // Prevent event bubbling

    // Delay opening the modal to allow the dropdown to close
    setTimeout(() => {
      if (typeof closeDropdown === 'function') {
        closeDropdown();
      }
      setIsModalOpen(true);
      setError('');
      fetchDeviceHistory();
    }, 0);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setHistoryData('');
    setError('');
    setCopied(false);
  };

  const copyToClipboard = () => {
    if (historyData) {
      navigator.clipboard.writeText(historyData)
        .then(() => {
          setCopied(true);
          // Reset copied state after 2 seconds
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
        });
    }
  };

  const fetchDeviceHistory = async () => {
    if (!deviceSerial) {
      setError('Device serial number is required');
      return;
    }

    setIsLoading(true);
    try {
      const response = await mutateAsync({
        endpoint: `/qc/devices/${deviceSerial}/history?format=text`,
        method: 'GET',
        suppressSuccessMsg: true,
        responseType: 'text', // Add responseType to get text response
      });

      // Check if response is a string (text response)
      if (typeof response === 'string') {
        setHistoryData(response);
      }
      // Check if response is an object with data property
      else if (response && response.data) {
        setHistoryData(response.data);
      }
      // If response is an object but without data property
      else if (response) {
        // Convert the entire response object to a string for display
        setHistoryData(JSON.stringify(response, null, 2));
      }
      else {
        setHistoryData('No history found for this device.');
      }

      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      setError(err.message || 'An error occurred while fetching device history');
      console.error('Device history error:', err, deviceSerial);
    }
  };

  const renderButton = () => {
    const hasNote = !!device.note;
    const numErrors = countDeviceErrors(device);
    return (
      <Tooltip1 content={btnOnly ? "Device info" : ""} placement="top">
        <div className="flex items-center cursor-pointer">
          <div className="relative inline-block">
            {numErrors !== 0 ? (
              // Info icon with red background
              <MenuIcon icon={
                <div className="bg-red-500 rounded-full">
                  <Info size={18} className="text-white" />
                </div>
              } />
            ) : (
              <MenuIcon icon={<Info size={18} />} />
            )}

            {/* Yellow dot for notes */}
            {hasNote && (
              <>
                <div className="absolute top-0 right-0">
                  <div className="w-2.5 h-2.5 bg-yellow-200 rounded-full"></div>
                </div>
                <div className="absolute top-0.5 right-0.5">
                  <div className="w-1.5 h-1.5 bg-yellow-300 rounded-full"></div>
                </div>
              </>
            )}
          </div>

          {btnOnly || <Text1>View history...</Text1>}
        </div>
      </Tooltip1>
    );
  };

  return (
    <div>
      {/* Wrap the button click handler to also stop propagation and close the dropdown */}
      <div onClick={handleOpenModal}>
        {renderButton()}
      </div>
      {isModalOpen && (
        <DeviceHistoryModal
          isOpen={true}
          onClose={handleCloseModal}
          hideSecondaryButton={true}
          device={device}
          deviceIssues={countDeviceErrors(device)}
        />
      )}
    </div>
  );
}

export default DeviceHistoryButton;
