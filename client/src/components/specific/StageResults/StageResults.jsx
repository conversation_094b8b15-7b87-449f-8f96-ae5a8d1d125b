import { Popover1, ResultBadge, Tooltip1 } from "@/components";
import { IoIosWarning } from "react-icons/io";

// Helper function to detect touch devices
function isTouchDevice() {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    navigator.msMaxTouchPoints > 0
  );
}

function StageResults({ data }) {
  // Determine the trigger type based on the device
  const triggerType = isTouchDevice() ? "click" : "hover";
  const totalResults = data?.testResults?.length ?? 0;
  const passedResults = data?.testResults?.filter(
    (result) => result.status === "passed"
  ) ?? [];

  return (
    <>
      {/* Only visible on large, or highly zoomed-out, screen. */}
      <div className="hidden lg:block">
        <div className="flex -space-x-1 xl:space-x-0 xl:gap-0.5">
          {data?.testResults?.map((result) => {
            if (!result.reason) {
              return (
                <ResultBadge
                  key={result.name}
                  text={result.label}
                  description={result.description}
                  status={result.status}
                />
              );
            }

            return (
              <Popover1
                key={result.name}
                title={result.description}
                text={
                  <span className="text-xs">
                    {result.reason}
                  </span>
                }
                trigger={triggerType}
                button={
                  <ResultBadge
                    text={result.label}
                    status={result.status}
                    description={result.description}
                    icon={<IoIosWarning color="var(--vibrant-orange)" />}
                  />
                }
              />
            );
          })}
        </div>
      </div>

      {/* Only visible on small, or highly zoomed-in, screens. */}
      <div className="lg:hidden">
        <div className="flex -space-x-1 lg:space-x-0 lg:gap-0.5">
          {data?.testResults?.map((result) => {
            // Always show running tests, regardless of their pass/fail status
            if (result.status === "running" || result.status === "passed" || result.status === "failed") {
              if (!result.reason) {
                return (
                  <Tooltip1
                    key={result.name}
                    content={
                      <span className="text-xs">
                        {result.label} check {result.status}
                      </span>
                    }
                  >
                    {result.status === "running" ? (
                      // For running tests, don't use compact mode to ensure visibility
                      <ResultBadge
                        key={result.name}
                        text={result.label}
                        description={result.description}
                        status={result.status}
                      />
                    ) : (
                      // For passed/failed tests, use compact mode as before
                      <ResultBadge
                        compact
                        key={result.name}
                        text={result.status === "passed" ? ' ' : result.label}
                        description={result.description}
                        status={result.status}
                      />
                    )}
                  </Tooltip1>
                );
              }

              return (
                <Popover1
                  key={result.name}
                  title={result.description}
                  text={
                    <span className="text-xs">
                      {result.label} check {result.status}
                      <hr className="border-white opacity-10 my-1" />
                      {result.reason}
                    </span>}
                  trigger={triggerType}
                  button={
                    <ResultBadge
                      text={result.label}
                      status={result.status}
                      description={result.description}
                      icon={<IoIosWarning color="var(--vibrant-orange)" />}
                    />
                  }
                />
              );
            }
          })}
        </div>
      </div>
    </>
  );
}

export default StageResults;
