import React from "react";
import { Edit, AddNotesModal, Text1 } from "@/components";

function AddNotes({ device }) {
  const [isOpen, setIsOpen] = React.useState(false);
  return (
    <>
      <div className="flex gap-1 items-center" onClick={() => setIsOpen(true)}>
        <Edit stroke="var(--vibrant-orange)" />
        <Text1>Add notes</Text1>
      </div>
      {isOpen && (
        <AddNotesModal
          isOpen={isOpen}
          deviceID={device.serial}
          onAdd={() => setIsOpen(false)}
          onClose={() => setIsOpen(false)}
        />
      )}
    </>
  );
}

export default AddNotes;
