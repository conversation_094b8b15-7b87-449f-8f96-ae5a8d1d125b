import React from "react";
import { Trash } from "lucide-react";
import {
  CustomModal,
  Text1,
  FriendlySerialNumber,
  MenuIcon,
} from "@/components";
import { useMutate } from "@/hooks";

function DeleteDevice({
  device,
  refetch = () => {},
  closeDropdown = () => {},
}) {
  const { mutateAsync } = useMutate();
  const [isOpenModal, setIsOpenModal] = React.useState(false);

  const handleDelete = () => {
    mutateAsync({
      endpoint: "/qc/stages/deleted/devices",
      method: "PUT",
      body: { serial: device.serial },
      successMsg: "Device moved to trash.",
    }).then(() => {
      refetch();
    }).finally(() => {
      setIsOpenModal(false);
    });
  };

  return (
    <>
      <div
        className="flex items-center"
        onClick={(e) => {
          e.stopPropagation();
          closeDropdown();
          setTimeout(() => setIsOpenModal(true), 0);
        }}
      >
        <MenuIcon icon={<Trash size={20} />} />
        <Text1>Delete...</Text1>
      </div>
      {isOpenModal && (
        <CustomModal
          title="Delete device"
          primaryButtonText="Delete"
          secondaryButtonText="Keep"
          isOpen={isOpenModal}
          onPrimaryButtonClick={handleDelete}
          onSecondaryButtonClick={() => setIsOpenModal(false)}
        >
          Delete device <FriendlySerialNumber serial={device.serial} /> from
          QC system?
        </CustomModal>
      )}
    </>
  );
}

export default DeleteDevice;
