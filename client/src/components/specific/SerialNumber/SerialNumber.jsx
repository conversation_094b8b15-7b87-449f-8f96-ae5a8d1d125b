import { CopyableText, Hyperlink } from "@/components";

function TransparentLeadingZeros({ text }) {
  if (!text) return null;

  const firstFour = text.slice(0, 4);
  const leadingZeros = firstFour.match(/^0*/)[0]; // Match leading zeros
  const rest = firstFour.slice(leadingZeros.length); // The rest of the string

  return (
    <span>
      <span className="opacity-40">{leadingZeros}</span>
      {rest}
    </span>
  );
}

export function FriendlySerialNumber({ serial }) {
  if (!serial) return null;

  const renderFormattedSerial = () => {
    if (serial.length === 10) {
      return (
        <>
          {"  "}
          <span>{serial.slice(0, 2)}</span>
          <span className="ml-[.16rem]">{serial.slice(2, 6)}</span>
          <span className="ml-[.16rem]">{serial.slice(6, 10)}</span>
        </>
      );
    }

    if (serial.length === 12) {
      return (
        <>
          <TransparentLeadingZeros text={serial.slice(0, 4)} />
          <span className="ml-[.16rem]">{serial.slice(4, 8)}</span>
          <span className="ml-[.16rem]">{serial.slice(8, 12)}</span>
        </>
      );
    }

    return <span className="text-red-500">{serial}</span>;
  };

  return (
    <span className="whitespace-nowrap font-mono">
      {renderFormattedSerial()}
    </span>
  );
}

const SerialNumber = ({ device, className = "" }) => {
  if (!device) return null;
  const { serial, assignedTo } = device;

  return (
    <div className={`flex gap-1 items-center ${className}`}>
      <Hyperlink to={`/boxes/${device.device}?company=${assignedTo}`}>
        <FriendlySerialNumber serial={serial} />
      </Hyperlink>
      <CopyableText text={serial} />
    </div>
  );
};

export default SerialNumber;
