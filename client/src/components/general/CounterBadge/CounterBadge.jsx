import { Badge } from "flowbite-react";

function formatCount(num) {
  // For numbers less than 1000, return as string
  if (num < 1000) {
    return num;
  }

  // For numbers >= 1000, format as "X.Yk"
  const thousands = Math.floor(num / 1000);
  const hundreds = Math.floor((num % 1000) / 100);

  // Only add decimal part if hundreds > 0
  if (hundreds > 0) {
    return `${thousands}.${hundreds}K`;
  } else {
    return `${thousands}K`;
  }
}

function CounterBadge({ value }) {
  if (value) {
    return (
      <Badge
        className="absolute bottom-[7px] right-[9px] min-w-4 inline-flex items-center justify-center py-0 px-.5 text-white font-500 bg-stone-500 dark:bg-gray-600 rounded-full"
        style={{ fontSize: '.625rem' }}
      >
          {formatCount(value)}
      </Badge>
    );
  }
}

export default CounterBadge;
