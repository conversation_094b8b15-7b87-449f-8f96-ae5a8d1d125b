import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  NavButton1,
  CounterBadge,
} from "@/components";
import {
  Gauge,
  Play,
  ListChecks,
  Package,
  ThumbsDown,
  Wrench,
  Rocket,
  Layers
} from "lucide-react";
import { useFetch } from "@/hooks";
import { ROUTES } from "@/constants";

const SidebarDivider = () => (
  <div className="w-full h-[1px] bg-stone-500"></div>
);

function SideBar({ children }) {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: deviceCount } = useFetch({
    endpoint: "/qc/stages",
    refetchInterval: 5000,
    suppressErrors: true,
  });

  const getIsActive = (route) => {
    const currentPathWithoutTrailingSlash = location?.pathname.endsWith('/')
      ? location.pathname.slice(0, -1) : location.pathname;
    return route === currentPathWithoutTrailingSlash;
  };

  return (
    <>
      <aside className="fixed left-0 top-0 z-40 w-[56px] h-full pt-[56px] transition-transform bg-[--brand-gray] dark:bg-gray-800" aria-label="Sidebar">
        <div className="flex flex-col items-center max-h-[90vh]">
          <NavButton1
           name={<>Dashboard{deviceCount?.dailyShippable > 1000 && <> ({deviceCount?.dailyShippable})</>}</>}
            key={ROUTES.V2.DASHBOARD}
            isActive={getIsActive(ROUTES.V2.DASHBOARD)}
            onClick={() => navigate(ROUTES.V2.DASHBOARD)}
          >
            <Gauge size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.dailyShippable} />
          </NavButton1>
          <SidebarDivider />
          <NavButton1
            name={<>Software stage{deviceCount?.software > 1000 && <> ({deviceCount?.software})</>}</>}
            key={ROUTES.V2.SOFTWARE}
            isActive={
              getIsActive(ROUTES.V2.SOFTWARE) || getIsActive(ROUTES.V2.BASE)
            }
            onClick={() => navigate(ROUTES.V2.SOFTWARE)}
          >
            <Play size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.software} />
          </NavButton1>
          <NavButton1
            name={<>Hardware stage{deviceCount?.hardware > 1000 && <> ({deviceCount?.hardware})</>}</>}
            key={ROUTES.V2.HARDWARE}
            isActive={getIsActive(ROUTES.V2.HARDWARE)}
            onClick={() => navigate(ROUTES.V2.HARDWARE)}
          >
            <ListChecks size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.hardware} />
          </NavButton1>
          <NavButton1
            name={<>Shipping stage{deviceCount?.shipping > 1000 && <> ({deviceCount?.shipping})</>}</>}
            key={ROUTES.V2.SHIPPING}
            isActive={getIsActive(ROUTES.V2.SHIPPING)}
            onClick={() => navigate(ROUTES.V2.SHIPPING)}
          >
            <Package size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.shipping} />
          </NavButton1>
          <SidebarDivider />
          <NavButton1
            name={<>Failed bin{deviceCount?.failure > 1000 && <> ({deviceCount?.failure})</>}</>}
            key={ROUTES.V2.FAILED_BIN}
            isActive={getIsActive(ROUTES.V2.FAILED_BIN)}
            onClick={() => navigate(ROUTES.V2.FAILED_BIN)}
          >
            <ThumbsDown size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.failure} />
          </NavButton1>
          <NavButton1
           name={<>Repair bin{deviceCount?.repair > 1000 && <> ({deviceCount?.repair})</>}</>}
            key={ROUTES.V2.REPAIR_BIN}
            isActive={getIsActive(ROUTES.V2.REPAIR_BIN)}
            onClick={() => navigate(ROUTES.V2.REPAIR_BIN)}
          >
            <Wrench size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.repair} />
          </NavButton1>
          <NavButton1
            name={<>Engineering bin{deviceCount?.engineering > 1000 && <> ({deviceCount?.engineering})</>}</>}
            key={ROUTES.V2.ENGINEERING_BIN}
            isActive={getIsActive(ROUTES.V2.ENGINEERING_BIN)}
            onClick={() => navigate(ROUTES.V2.ENGINEERING_BIN)}
          >
            <Rocket size={25} className="stroke-[1.4px]" />
            <CounterBadge value={deviceCount?.engineering} />
          </NavButton1>
          <SidebarDivider />
          <NavButton1
            name="Reports"
            key={ROUTES.V2.REPORTS}
            isActive={getIsActive(ROUTES.V2.REPORTS)}
            onClick={() => navigate(ROUTES.V2.REPORTS)}
          >
            <Layers size={25} className="stroke-[1.4px]" />
          </NavButton1>
        </div>
      </aside>
      <div className="flex flex-col pl-[56px]">
        {children}
      </div>
    </>
  );
}

export default SideBar;
