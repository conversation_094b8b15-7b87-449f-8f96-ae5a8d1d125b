import React from "react";
import { Dropdown1 } from "@/components";

function ResponsiveRender({ children }) {
  const [winWidth, setWinWidth] = React.useState();

  React.useEffect(() => {
    const handleResize = () => {
      setWinWidth(window.innerWidth);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const showDropdown = winWidth <= 1024;

  if (showDropdown) {
    return <Dropdown1>{children}</Dropdown1>;
  }

  return children;
}

export default ResponsiveRender;
