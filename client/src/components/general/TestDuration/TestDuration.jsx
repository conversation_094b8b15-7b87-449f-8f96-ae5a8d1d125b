import { useState, useEffect } from "react";

export default function TestDuration({ startTime }) {
  const [counter, setCounter] = useState(0);

  // Start the timer when the component mounts
  useEffect(() => {
    let timer = setInterval(() => {
      let now = new Date();
      let durationInSeconds = Math.floor((now - new Date(startTime)) / 1000);
      setCounter(durationInSeconds);
    }, 1000);

    // Clean up the timer when the component unmounts
    return () => {
      clearInterval(timer);
    };
  }, [startTime]);

  // Format the counter as a duration string
  let minutes = Math.floor(counter / 60);
  let seconds = counter % 60;
  let duration = `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;

  // Render the duration
  return (
    <span className="text-bold">
      {duration}
    </span>
  );
}
