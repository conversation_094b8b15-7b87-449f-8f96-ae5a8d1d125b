import React from "react";
import {
  TextInput,
  Radio,
  Checkbox,
  Textarea,
  FileInput,
  Select,
  Label,
} from "flowbite-react";
import { INPUT_FIELD_TYPES } from "@/constants";

const commonStyles = {
  height: "28px",
  borderRadius: "0.375rem",
  fontSize: 13.5,
  border: 'none',
  backgroundColor: 'rgb(245 245 244)',
};

const baseStyles = {
  textInput: {
    ...commonStyles,
  },
  selectInput: {
    ...commonStyles,
    padding: "0 5px",
  },
};

function InputField(
  {
    type,
    name,
    value,
    label,
    regex,
    showHelperText = false,
    children,
    ...rest
  },
  ref
) {
  switch (type) {
    case INPUT_FIELD_TYPES.TEXT:
    case INPUT_FIELD_TYPES.NUMBER:
      const validValue = regex && value ? regex.test(value) : true;

      return (
        <div className="flex flex-row gap-1 items-center">
          {label && (
            <Label htmlFor={name} className="font-bold">
              {label}
            </Label>
          )}
          <TextInput
            id={name}
            type={type}
            name={name}
            value={value}
            className="w-24"
            style={baseStyles.textInput}
            sizing="sm"
            color={!validValue ? "failure" : ""}
            helperText={showHelperText && !validValue ? "Invalid value" : ""}
            ref={ref}
            {...rest}
          />
        </div>
      );
    case INPUT_FIELD_TYPES.RADIO:
      const bgColor = value === false ? "bg-[var(--red)]" : "";

      return (
        <div className="flex items-center gap-2">
          <Radio
            id={name}
            name={name}
            checked={Boolean(value)}
            className={`w-3 h-3 checked:bg-[var(--vivid-green)] checked:bg-none ${bgColor}`}
            ref={ref}
            {...rest}
          />
          {label && <Label htmlFor={name}>{label}</Label>}
        </div>
      );
    case INPUT_FIELD_TYPES.CHECKBOX:
      return (
        <div className="flex items-center gap-2">
          <Checkbox id={name} name={name} checked={value} ref={ref} {...rest} />
          <Label htmlFor={name}>{label}</Label>
        </div>
      );
    case INPUT_FIELD_TYPES.TEXTAREA:
      return (
        <div className="max-w-md">
          {label && (
            <div className="mb-2 block">
              <Label htmlFor={name}>{label}</Label>
            </div>
          )}
          <Textarea id={name} name={name} ref={ref} {...rest} />
        </div>
      );
    case INPUT_FIELD_TYPES.FILE:
      return (
        <div className="max-w-md">
          <div className="mb-2 block">
            <Label htmlFor={name}>{label}</Label>
          </div>
          <FileInput id={name} name={name} ref={ref} {...rest} />
        </div>
      );
    case INPUT_FIELD_TYPES.SELECT:
      return (
        <div className="flex flex-row gap-1 items-center">
          {label && <Label htmlFor={name}>{label}</Label>}
          <Select
            id={name}
            name={name}
            style={baseStyles.selectInput}
            ref={ref}
            className="w-24"
            {...rest}
          >
            {children}
          </Select>
        </div>
      );
    default:
      return "";
  }
}

export default React.forwardRef(InputField);
