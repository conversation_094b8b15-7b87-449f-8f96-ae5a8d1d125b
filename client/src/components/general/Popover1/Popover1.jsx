import React from "react";
import { Popover } from "flowbite-react";
import { IoIosClose } from "react-icons/io";
import './Popover1.css';

function Popover1({
  title,
  text,
  button,
  trigger = "hover",
  placement = "top",
}) {
  const [open, setOpen] = React.useState(false);

  const toggle = () => {
    setOpen((o) => !o);
  };

  const TouchscreenCloseButton = () => {
    return trigger !== "hover" && (
      <div className="ml-2 w-6 h-6">
        <IoIosClose onClick={toggle} size="25" className="cursor-pointer" />
      </div>
    );
  };

  return (
    <Popover
      open={open}
      trigger={trigger}
      placement={placement}
      onOpenChange={(o) => setOpen(o)}
      className="Popover1 z-[100]"
      arrow={false}
      content={
        <div className="flex flex-col w-64 bg-stone-500 text-sm text-gray-800 dark:text-gray-400 rounded-sm">
          {title && (
            <div className="flex justify-between items-center border-b border-gray-200 bg-gray-100 px-3 py-2 dark:border-gray-600 dark:bg-gray-700">
              <h3 className="font-semibold text-gray-900 max-w-xs overflow-ellipsis overflow-hidden">{title}</h3>
              <TouchscreenCloseButton />
            </div>
          )}
          {!title && (
            <div className="flex justify-between items-start px-3 py-2 bg-stone-500 text-stone-100">
              <p className="whitespace-normal break-words">{text}</p>
              <TouchscreenCloseButton />
            </div>
          )}
          {title && (
            <div className="px-3 py-2">
              <p className="whitespace-normal break-words">{text}</p>
            </div>
          )}
        </div>
      }
    >
      <button type="button" className="cursor-default outline-none">{button}</button>
    </Popover>
  );
}

export default Popover1;
