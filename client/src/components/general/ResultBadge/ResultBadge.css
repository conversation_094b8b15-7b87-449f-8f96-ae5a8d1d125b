@keyframes shimmer {
  0% {
    background-position: 800% 0;
  }
  100% {
    background-position: -800% 0;
  }
}

.shimmer-badge {
  position: relative;
  overflow: hidden;
}

.shimmer-badge::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg,
   rgba(255,255,255,0.0) 0%,
    rgba(255,255,255,0.5) 5%,
    rgba(255,255,255,0.2) 66%,
    rgba(255,255,255,0.0) 100%
    );
  background-size: 600% 100%;
  animation: shimmer 18s infinite linear;
  pointer-events: none; /* Ensures the shimmer effect doesn't interfere with clicks */
}
