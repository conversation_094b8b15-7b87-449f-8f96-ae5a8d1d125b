// ResultBadge.js

import { useState, useEffect } from 'react';
import './ResultBadge.css';
import { Check, X } from 'lucide-react';

// Create a single shared timestamp for all animations
// Using a module-level constant so it's shared across all components
const SHARED_ANIMATION_START = Date.now();

const attributes = {
  pending: {
    name: "Pending",
    shortName: "P",
    style: "bg-white text-gray-500 opacity-60",
    zIndex: 1,
    icon: null
  },
  running: {
    name: "Running",
    shortName: "R",
    style: "text-white bg-blue-500", // No shimmer-badge class here
    zIndex: 8,
    icon: null,
  },
  passed: {
    name: "Pass",
    shortName: "P",
    style: "bg-green-500 text-white border-white border xl:border-none",
    zIndex: 4,
    icon: <Check size={12} />
  },
  failed: {
    name: "Fail",
    shortName: "F",
    style: "text-white border-white border xl:border-none bg-red-500",
    zIndex: 8,
    icon: <X size={12} />
  },
};

export default function ResultBadge({ text, status, icon, compact = false }) {
  const [position, setPosition] = useState(-800);

  // Set up the shimmer animation using requestAnimationFrame
  useEffect(() => {
    if (status !== 'running') return;

    let animationId;
    const duration = 18000; // 18 seconds in ms

    const animate = (time) => {
      // Calculate time elapsed since the shared timestamp
      const elapsed = (time - SHARED_ANIMATION_START) % duration;
      const progress = elapsed / duration;

      // Calculate position (from -800 to 800)
      const newPosition = -800 + progress * 1600;
      setPosition(newPosition);

      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) cancelAnimationFrame(animationId);
    };
  }, [status]);

  const {
    style = "text-gray-300 bg-transparent",
    shortName,
    zIndex = 1,
    icon: statusIcon
  } = attributes?.[status] ?? {};

  const displayText = compact ? '' : text;
  const shapeClass = compact ? 'rounded-full' : 'rounded-full';
  const dimensionClass = compact ? 'h-[1rem] w-[1rem]' : 'h-[1rem]';
  const paddingClass = compact ? 'p-0' : 'px-[.4rem]';

  // Use status icon (Check/X) in compact mode or the passed icon for non-compact
  const displayIcon = compact ? statusIcon : icon;

  return (
    <div className="outline:none">
      <div
        className={`${style} relative ${shapeClass} ${dimensionClass} flex items-center justify-center overflow-hidden`}
        style={{ zIndex }}
      >
        {status === 'running' && (
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              background: 'linear-gradient(90deg, rgba(255,255,255,0.0) 0%, rgba(255,255,255,0.5) 5%, rgba(255,255,255,0.2) 66%, rgba(255,255,255,0.0) 100%)',
              backgroundSize: '600% 100%',
              backgroundPosition: `${position}% 0`,
            }}
          />
        )}
        <div className={`flex items-center justify-center ${paddingClass} w-full h-full cursor-default text-xxs`}>
          {compact ? (
            // In compact mode, center the icon properly
            <div className="flex items-center justify-center w-full h-full">
              {statusIcon}
            </div>
          ) : (
            <>
              <span className="font-condensed xl:font-sans">{displayText}</span>
              {icon && <span className="lg:ml-[1px]">{icon}</span>}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
