import Tooltip1 from "../Tooltip1";

const defaultColors = {
  light: '#e5e7eb',
  dark: '#334155',
};

function Avatar1({ text, colors = defaultColors, tooltipPlacement = "right" }) {
  if (!text) {
    return;
  }

  const initials = text
    .split(/\s+/)
    .map((w) => w.charAt(0))
    .join("");

  return (
    <Tooltip1 content={text} placement={tooltipPlacement}>
      <div className="flex items-center justify-center space-x-4 rounded cursor-default">
        <div
          className="relative inline-flex text-xm items-center justify-center overflow-hidden uppercase h-8 w-8 rounded-full"
          style={{
            backgroundColor: colors.light,
            color: colors.dark,
          }}
        >
          <span className="pl-[0.0333rem] pt-[0.0333rem]">{initials}</span>
        </div>
      </div>
    </Tooltip1>
  );
}

export default Avatar1;
