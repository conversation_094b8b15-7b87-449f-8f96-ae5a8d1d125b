import React, { useEffect, useMemo, useState } from "react";
import { Pa<PERSON><PERSON>, Spinner, Alert } from "flowbite-react";
import "./DataTable.css";

const UpArrowIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" style={{ transform: "translateY(-2px)" }}>
    <path d="M7 14.5L12 9.5L17 14.5" stroke="currentColor" fill="currentColor" />
  </svg>
);

const DownArrowIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" style={{ transform: "translateY(2px)" }}>
    <path d="M17 9.5L12 14.5L7 9.5" stroke="currentColor" fill="currentColor" />
  </svg>
);

const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-[66vh]">
    <div className="text-center">
      <Spinner className="w-16 h-16 fill-[--brand-blue] text-stone-200 animate-spin dark:text-gray-600" />
    </div>
  </div>
);

export default function DataTable({
  columns = [],
  rows = [],
  isLoading = false,
  showPagination = false,
  pageSize,
  totalRows,
  onPageChange,
  onSortOrderChange = () => {},
}) {
  const totalPages = useMemo(
    () => (totalRows < pageSize ? 1 : Math.ceil(totalRows / pageSize)),
    [totalRows, pageSize]
  );

  const [currentPage, setCurrentPage] = useState(1);

  const renderCellContent = (column, row) => {
    const { field, cellRenderer } = column;
    const value = row[field];

    if (cellRenderer) {
      return cellRenderer({ column, row, value });
    }

    return value;
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);

    const isFirstPage = page === 1;
    const isLastPage = page === totalPages;
    onPageChange({ page, totalPages, isFirstPage, isLastPage });
  };

  // Array of `{ field: string, direction: "asc" | "desc" }`.
  const [sortingStack, setSortingStack] = useState([]);

  useEffect(() => {
    // Trigger server-side fetch with updated sorting stack
    onSortOrderChange({ sortingStack });
  }, [sortingStack]); // dependency array

  const handleSortClick = (column, event) => {
    // Copy the current sorting stack
    let updatedStack = [...sortingStack];

    // Find the existing sort object for this column
    let sortObject = updatedStack.find((sort) => sort.field === column.field);

    if (sortObject) {
      // If the sort object exists, update its direction
      sortObject.direction = sortObject.direction === "asc" ? "desc" : "asc";
      // Remove the sort object from its current position
      updatedStack = updatedStack.filter((sort) => sort.field !== column.field);
    } else {
      // If the sort object doesn't exist, create a new one
      sortObject = { field: column.field, direction: "asc" };
    }

    // Add the sort object to the end of the stack
    updatedStack.push(sortObject);

    // Update the sorting stack
    setSortingStack(updatedStack);
  };

  const pagination = showPagination && totalPages > 1;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (rows.length === 0) {
    return (
      <Alert color="info" className="rounded-lg text-sm bg-stone-100 text-gray-600 py-6 px-6 shadow-none">
        No matching devices found
      </Alert>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="border-2 border-stone-100 rounded-sm dataTableScrollBar max-w-full">
        <table className="w-full min-w-full">
          <thead className={`sticky top-0 z-6 ${isLoading ? "animate-pulse" : ""}`}>
            <tr>
              {columns.map((c, rowIndex) => {
                if (!c.title) {
                  return null;
                }

                // Find the sort object for this column
                const sortObject = sortingStack.find((sort) => sort.field === c.field);

                return (
                  <th
                    key={rowIndex}
                    className={`whitespace-nowrap bg-stone-100 text-gray-500 uppercase text-left px-1  py-1 font-condensed font-normal text-[14px] dark:text-gray-100 ${c.headerClasses} ${c.field ? "hover:text-gray-700 cursor-pointer" : ""}`}
                    onClick={(event) => handleSortClick(c, event)}
                  >
                    <div className="flex items-center justify-start select-none">
                      {c.title}
                      {c.field && (
                        sortObject ? (
                          <span className="flex-shrink-0 text-gray-400">
                            {sortObject.direction === "asc" ? <UpArrowIcon /> : <DownArrowIcon />}
                          </span>
                        ) : <div className="w-4" />
                      )}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody>
            {rows.map((r) => {
              const isSpanRowFieldExists = columns.some(
                (c) => c.spanRow && r[c.field]
              );
              const lightUserColor = r?.userColors?.light;
              const backgroundColor = lightUserColor ? `${lightUserColor}3f` : '#fff';
              return (
                <tr
                  key={r.device}
                  className="border-gray-300/30 dark:border-gray-900 dark:bg-black border-b"
                >
                  {columns.map((c, index) => {
                    if (c.spanRow) {
                      return null;
                    }

                    const cellClasses = isSpanRowFieldExists ? "pb-0" : "";
                    const contentClasses =
                      c.contentClasses || "flex justify-left items-center";
                    return (
                      <td
                        key={index}
                        className={`whitespace-nowrap text-left m-0 px-1 h-12 py-2 text-sm ${cellClasses}`}
                        style={{ backgroundColor }}
                      >
                        <div className={contentClasses}>
                          {renderCellContent(c, r)}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      {pagination && (
        <div className="flex justify-end pb-4 pr-2">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            showIcons
          />
        </div>
      )}
    </div>
  );
}
