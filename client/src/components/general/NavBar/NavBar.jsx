import React from "react";
import {
  AppsMenu,
  AddDeviceNoteButton,
  LocationChangeButton,
  SearchBar,
} from '@/components';

function NavBar() {
  return (
    <nav className="fixed top-0 left-0 right-0 z-50 h-[56px] bg-[--paper-white]">
      <div className="flex justify-between items-center w-full z-50 dark:bg-gray-800 border border-b h-[56px] border-gray-200 dark:border-none pr-4">
        <div className="flex items-center flex-shrink-0">
          <div className="w-[56px] flex justify-center">
            <AppsMenu currentApp="qc" className="align-middle" />
          </div>
          <img
            className="bg-center bg-no-repeat w-32 mt-.5 ml-3 dark:hidden"
            src="epik-logo.png"
          />
          <img
            className="bg-center bg-no-repeat w-32 mt-.5 ml-3 opacity-90 hidden dark:block"
            src="epik-logo-the-white.png"
          />
        </div>
        <div className="flex-grow"></div>
        <div className="flex items-center space-x-2">
          <div className="mr-1.5">
            <SearchBar />
          </div>
          <AddDeviceNoteButton btnOnly />
          <LocationChangeButton btnOnly />
        </div>
      </div>
    </nav>
  );
}

export default NavBar;
