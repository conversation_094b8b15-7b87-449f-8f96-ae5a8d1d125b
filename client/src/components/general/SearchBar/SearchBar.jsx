import React, { useState, useEffect } from 'react';
import { useFetch, useDebounce } from '@/hooks';
import { useNavigate } from 'react-router-dom';
import { SearchIcon } from '@/components';
import { FriendlySerialNumber } from '@/components';
import './SearchBar.css';

function CloseIcon() {
  return (
    <svg
      className="h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  );
}

function SearchBar() {
  const [inputValue, setInputValue] = useState("");
  const [inputVisible, setInputVisible] = useState(false);
  const debouncedValue = useDebounce(inputValue, 500);
  const {
    data: results,
    isLoading,
    error,
  } = useFetch({
    endpoint: `/qc/devices?q=${debouncedValue}`,
    enabled: !!debouncedValue,
  });
  const [isFocused, setIsFocused] = useState(false);

  const navigate = useNavigate();

  const pathMap = {
    software: "software",
    hardware: "hardware",
    shipping: "shipping",
    failure: "failedbin",
    repair: "repairbin",
    engineering: "engineeringbin",
  };

  const handleResultClick = (result) => {
    navigate(`/apps/qc/${pathMap[result.stage]}?serial=${result.serial}`);
  };

  const stageMap = {
    software: "Stage 1",
    hardware: "Stage 2",
    shipping: "Stage 3",
    failure: "Failed bin",
    repair: "Repair bin",
    engineering: "Engineering bin",
  };

  const stageOrder = [
    "Stage 1",
    "Stage 2",
    "Stage 3",
    "Shipping",
    "Failed bin",
    "Failed bin",
    "Engineering bin",
  ];

  const [transformedResults, setTransformedResults] = useState({});

  useEffect(() => {
    if (results) {
      const transformedData = results.reduce((acc, item) => {
        let stage = stageMap[item.stage] || item.stage;
        if (!acc[stage]) {
          acc[stage] = [];
        }
        acc[stage].push(item);
        return acc;
      }, {});

      let orderedData = {};
      stageOrder.forEach((stage) => {
        if (transformedData[stage]) {
          orderedData[stage] = transformedData[stage];
        }
      });

      setTransformedResults(orderedData);
    } else {
      setTransformedResults({});
    }
  }, [results]);

  const hasResults =
    transformedResults && Object.keys(transformedResults).length > 0;

  return (
    <div className="flex">
      <div
        className="px-0 py-2.5 cursor-pointer"
        onClick={() => setInputVisible(!inputVisible)}
      >
        {" "}
        <SearchIcon />
      </div>
      {inputVisible && (
        <div className="relative w-36 h-7">
          {hasResults && (
            <div
              className="absolute right-0 top-1 z-10 w-6 h-[28px] text-center text-stone-500 text-xl cursor-pointer flex items-center justify-center"
              onClick={() => {
                setInputValue("");
                setTransformedResults({});
              }}
            >
              <CloseIcon />
            </div>
          )}
          <div className={`absolute top-[4px] ml-2 ${hasResults && 'shadow-lg'}`}>
            <div
              className={`bg-white w-34 rounded-md overflow-hidden${
                hasResults && isFocused ? "shadow-xl" : ""
              }`}
            >
              <input
                maxLength="12"
                placeHolder="Search all stages"
                autoFocus
                value={inputValue}
                onChange={(e) => {
                  setInputValue(e.target.value);
                  if (e.target.value === "") {
                    setTransformedResults({});
                  }
                }}
                onFocus={() => setIsFocused(true)}
                onBlur={() => {
                  // Delay the onBlur event to allow for clicks on the dropdown
                  setTimeout(() => setIsFocused(false), 200);
                }}
                className={`search-input bg-stone-100 !border-none w-full pl-2 mr-4 h-6 focus:outline-none rounded-md ${
                  hasResults && isFocused ? "rounded-b-none" : ""
                }`}
                style={{
                  border: '!none',
                  height: "28px",
                  fontSize: 13.5,
                }}
              />
              {hasResults && isFocused && (
                <div
                  className="bg-white results-preview w-full overflow-hidden overflow-y-auto custom-scrollbar"
                  style={{ maxHeight: "calc(100vh - 75px)" }}
                >
                  {Object.keys(transformedResults).map((stage, index) => (
                    <div key={stage}>
                      <div className="p-2 py-0.5 text-stone-500 text-xxs uppercase font-condensed font-bold">
                        {stage}
                      </div>
                      {transformedResults[stage].map((result) => (
                        <div
                          key={result.id}
                          onClick={() => handleResultClick(result)}
                          className="p-4 py-2 text-sm cursor-pointer hover:bg-stone-100 font-regular"
                        >
                          <FriendlySerialNumber className="mb-0" serial={result.serial} />
                          <div className="mt-0 text-stone-400 text-sm">
                            {result.location}
                          </div>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchBar;
