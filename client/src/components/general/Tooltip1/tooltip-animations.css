/* Base styles for all tooltips */
[role="tooltip"] {
  opacity: 0;
  transform-origin: center;
  transition: opacity 0.3s, transform 0.4s;
}

/* For tooltips that are currently showing */
[role="tooltip"][data-show] {
  opacity: 1;
}

/* Elastic animation - uses cubic-bezier for elastic effect */
.tooltip-elastic[role="tooltip"][data-show] {
  transform: scale(1);
  transition: opacity 0.2s, transform 0.5s cubic-bezier(0.18, 1.55, 0.38, 1.12);
}

/* Placement variations for elastic */
.tooltip-elastic-top[role="tooltip"] {
  transform: translateY(10px) scale(0.9);
}

.tooltip-elastic-bottom[role="tooltip"] {
  transform: translateY(-10px) scale(0.9);
}

.tooltip-elastic-left[role="tooltip"] {
  transform: translateX(10px) scale(0.9);
}

.tooltip-elastic-right[role="tooltip"] {
  transform: translateX(-10px) scale(0.9);
}

/* <PERSON>un<PERSON> animation */
.tooltip-bounce[role="tooltip"][data-show] {
  animation: tooltip-bounce-in 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes tooltip-bounce-in {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Smooth animation - simple slide & fade */
.tooltip-smooth[role="tooltip"][data-show] {
  transform: scale(1) translateY(0) translateX(0);
  transition: opacity 0.25s ease-out, transform 0.3s ease-out;
}

/* Placement variations for smooth */
.tooltip-smooth-top[role="tooltip"] {
  transform: translateY(8px) scale(0.95);
}

.tooltip-smooth-bottom[role="tooltip"] {
  transform: translateY(-8px) scale(0.95);
}

.tooltip-smooth-left[role="tooltip"] {
  transform: translateX(8px) scale(0.95);
}

.tooltip-smooth-right[role="tooltip"] {
  transform: translateX(-8px) scale(0.95);
}
