import { Tooltip } from "flowbite-react";

export default function Tooltip1({ content = "", placement = "top", children }) {
  if (!content) {
    return children;
  }

  return (
    <Tooltip
      content={content}
      placement={placement}
      className="whitespace-nowrap bg-stone-500 text-xs shadow-md pointer-events-none"
      animation="duration-300"
      style="dark"
      arrow={false}
    >
      {children}
    </Tooltip>
  );
}
