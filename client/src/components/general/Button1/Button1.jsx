import { Button } from "flowbite-react";

function Button1({ isActive = false, size, classes = "", onClick, children }) {
  const activeButtonClasses =
    "text-white bg-[var(--brand-blue)] justify-center items-center";
  const inActiveButtonClasses =
    "text-[var(--brand-blue)] bg-gray-50 hover:bg-gray-100 border-[var(--light-gray)]";

  const baseClasses = isActive ? activeButtonClasses : inActiveButtonClasses;

  return (
    <Button
      className={`${baseClasses} ${classes}`}
      size={size}
      onClick={onClick}
    >
      {children}
    </Button>
  );
}

export default Button1;
