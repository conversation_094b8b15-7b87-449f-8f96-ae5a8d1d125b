import { Badge } from "flowbite-react";

const attributes = {
  pending: {
    name: "Pending",
    style: "bg-[--chrome] text-gray-500",
  },
  running: {
    name: "Running",
    style: "text-[--brand-blue] bg-blue-100",
  },
  passed: {
    name: "Pass",
    style: "bg-green-100 text-[var(--vivid-green)]",
  },
  failed: {
    name: "Fail",
    style: "text-[var(--red)] bg-red-100",
  },
  timeout: {
    name: "Timeout",
    style: "bg-orange-100 text-orange-500",
  },
};

export default function StatusBadge({ text, status, icon }) {
  const getStatusText = () => {
    if (text) {
      return text;
    }

    return attributes[status]?.name ?? status;
  };

  const statusText = getStatusText();
  const style = attributes[status]?.style ?? "bg-gray-100 text-gray-600";

  return (
    <div className="scale-90">
      <Badge className={style}>
        <div className="flex gap-1 items-center">
          <span className="font-semibold text-xs">
            {statusText}
          </span>
          {icon}
        </div>
      </Badge>
    </div>
  );
}
