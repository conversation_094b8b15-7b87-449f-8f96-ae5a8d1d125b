import React from "react";
import { MoreHorizontal } from "lucide-react";
import { MenuIcon } from "@/components";

function Dropdown1({ label = "", children }) {
  const [isOpen, setIsOpen] = React.useState(false);
  const ref = React.useRef(null);

  const toggleDropdown = () => {
    setIsOpen((i) => !i);
  };

  const handleClickOutside = (event) => {
    const modal = event.target.closest(".modal");

    if (ref.current && !ref.current.contains(event.target) && !modal) {
      setIsOpen(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      setIsOpen(false);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative focus:outline-none focus:bg-gray-700" ref={ref}>
      <div className="flex">
        {label}
        <MenuIcon
          icon={
            <MoreHorizontal size={20} onClick={toggleDropdown} />
          }
        />
      </div>
      {/* Always render the dropdown; toggle its visibility with a CSS class */}
      <div
        className={`bg-white absolute mt-2 right-0 py-2 rounded-md z-10 shadow-md ${isOpen ? "" : "hidden"}`}
      >
        <ul>
          {React.Children.map(children, (child) => {
            if (!child) return null;

            if (child.props.hasOwnProperty("separator")) {
              return (
                <li className="px-0 py-2 hover:bg-white">
                  <hr />
                </li>
              );
            }

            return (
              <li className="pl-4 hover:bg-stone-200">
                {React.cloneElement(child, {
                  closeDropdown: () => setIsOpen(false),
                })}
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
}

export default Dropdown1;
