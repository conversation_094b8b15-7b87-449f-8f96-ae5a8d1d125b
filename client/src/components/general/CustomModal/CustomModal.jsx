import React from "react";
import ReactDOM from "react-dom";
import { Modal, But<PERSON> } from "flowbite-react";

const largeTheme = {
  header: {
    title: "text-xl font-bold text-[var(--sky-blue)]",
  },
  content: {
    base: "p-6",
  },
};

const smallTheme = {
  header: {
    title: "text-md font-bold text-[var(--sky-blue)] p-0 m-0",
  },
  content: {
    base: "p-0",
  },
};

function CustomModal({
  isOpen,
  title,
  children,
  primaryButtonText = "Submit",
  secondaryButtonText = "Cancel",
  size = "lg",
  dismissible = true,
  hideSecondaryButton = false,
  onPrimaryButtonClick,
  onSecondaryButtonClick,
  onClose=() => {},
  className={}
}) {
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <Modal
      show={isOpen}
      size={size}
      onClose={onClose || onSecondaryButtonClick}
      dismissible={dismissible}
      theme={size === "sm" ? smallTheme : largeTheme}
      className={`modal ${className}`}
    >
      <div className="w-full">
        <Modal.Header>{title}</Modal.Header>
        <Modal.Body>{children}</Modal.Body>
        {onPrimaryButtonClick && (
          <Modal.Footer className="justify-end">
            {onSecondaryButtonClick && !hideSecondaryButton && (
              <Button size="sm" color="gray" onClick={onSecondaryButtonClick}>
                {secondaryButtonText}
              </Button>
            )}
            {onPrimaryButtonClick && (
              <Button
                size="sm"
                className="bg-[var(--sky-blue)]"
                onClick={onPrimaryButtonClick}
              >
                {primaryButtonText}
              </Button>
            )}
          </Modal.Footer>
        )}
      </div>
    </Modal>,
    document.body
  );
}

export default CustomModal;
