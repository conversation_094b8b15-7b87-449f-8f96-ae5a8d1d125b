import React from "react";
import { useStore } from "@/store";

const myEpik = 'https://my.epik.io/';

function Auth({ children }) {
  const user = useStore((state) => state.user);

  React.useEffect(() => {
    let mounted = true;
    let retryCount = 0;
    const maxRetries = 3;
  
    const tryFetch = async () => {
      if (!mounted) return;
      
      const token = localStorage.getItem("jwtToken");
      if (!token) {
        window.location.href = myEpik;
        return;
      }
      
      try {
        await user.fetch();
      } catch (error) {
        console.error('Failed to fetch user');
        
        if (!mounted) return;
        
        if (retryCount < maxRetries) {
          retryCount++;
          const delay = Math.pow(2, retryCount - 1) * 1000;
          setTimeout(tryFetch, delay);
        } else {
            window.location.href = myEpik;
        }
      }
    };
  
    tryFetch();
  
    return () => {
      mounted = false;
    };
  }, []);

  if (user.status === "idle" || user.status === "fetching") {
    return (
      <div style={{ margin: '1rem' }}>
        <h2>Authenticating</h2>
        <p>Please wait while we verify your credentials.</p>
      </div>
    );
  }

  if (user.status === "failure") {
    window.location.href = myEpik;
    return;
  }

  if (user.status === "success" && !user.data.warehouseQcAccess) {
    window.location.href = myEpik;
    return;
  }

  return children;
}

export default Auth;
