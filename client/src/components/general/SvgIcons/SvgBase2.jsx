export default function SvgBase2({
  disabled = false,
  size = "md",
  className,
  onClick,
  children,
  ...rest
}) {
  const svgStyle = {
    pointerEvents: disabled ? "none" : "auto",
  };

  const getHeightWidthClasses = () => {
    switch (size) {
      case "xs":
        return "w-2 h-2";
      case "sm":
        return "w-4 h-4";
      case "md":
        return "w-6 h-6";
      case "lg":
        return "w-8 h-8";
      case "xl":
        return "w-10 h-10";
      case "xxl":
        return "w-16 h-16";
      default:
        return "w-6 h-6";
    }
  };

  const heightWidthClasses = getHeightWidthClasses();

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      className={`${heightWidthClasses} cursor-pointer ${className}`}
      onClick={onClick}
      {...svgStyle}
      {...rest}
    >
      {children}
    </svg>
  );
}
