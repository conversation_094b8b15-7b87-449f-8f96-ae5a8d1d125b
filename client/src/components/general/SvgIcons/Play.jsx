import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function Play({
  tooltip,
  size,
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        size={size}
        disabled={disabled}
        onClick={onClick}
        viewBox="20 20 250 250"
      >
        <path
          d="M228.1,121.2,84.2,33.2A8,8,0,0,0,72,40V216a8,8,0,0,0,12.2,6.8l143.9-88A7.9,7.9,0,0,0,228.1,121.2Z"
          fill="none"
          stroke="currentColor"
          strokeWidth="12"
          style={{ transform: "scale(1.2) translateX(-25px) translateY(-4px)" }}
        />
      </SvgBase2>
    </Tooltip1>
  );
}
