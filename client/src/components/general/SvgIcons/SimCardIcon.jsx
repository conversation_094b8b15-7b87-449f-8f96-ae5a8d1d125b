const SimCardIcon = ({
  size = 24,
  color = "currentColor",
  strokeWidth = 2,
  className = "",
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth * 0.8}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`lucide lucide-sim-card ${className}`}
      {...props}
    >
      {/* Main SIM card outline - rotated 90° with 45° notched corner */}
      <path d="M6 8v8l3 3h10a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2z" />

      {/* Larger "SIM" text */}
      <text
        x="13.5"
        y="15"
        fontSize="7"
        textAnchor="middle"
        fill="none"
        stroke={color}
        strokeWidth={strokeWidth * 0.2}
      >
        SIM
      </text>
    </svg>
  );
};

export default SimCardIcon;
