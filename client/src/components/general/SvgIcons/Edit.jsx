import { Tooltip1 } from "@/components";
import SvgBase1 from "./SvgBase1";

export default function Edit({ stroke, tooltip, size, disabled, onClick }) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase1
        d="m14.3 4.8 2.9 2.9M7 7H4a1 1 0 0 0-1 1v10c0 .6.4 1 1 1h11c.6 0 1-.4 1-1v-4.5m2.4-10a2 2 0 0 1 0 3l-6.8 6.8L8 14l.7-3.6 6.9-6.8a2 2 0 0 1 2.8 0Z"
        stroke={stroke}
        size={size}
        disabled={disabled}
        onClick={onClick}
      />
    </Tooltip1>
  );
}
