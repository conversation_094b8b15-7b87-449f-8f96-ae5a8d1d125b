import React from "react";

function Brand({ ...props }) {
  return (
    <svg
      viewBox="0 0 602.52 296.01"
      stroke="currentColor"
      fill="currentColor"
      {...props}
    >
      <polygon
        points="594.53 269.42 568.81 232.33 568.81 232.33 568.81 232.33 544.65 232.33 570.38 269.42 594.53 269.42"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <polygon
        points="544.65 232.33 562.59 206.35 586.85 206.35 568.81 232.33 568.81 232.33 568.81 232.33 544.65 232.33"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m461.97,206.4c-5.51,0-10.66,1.42-15.21,3.89v-3.99h-20.16v89.7h10.06c5.56,0,10.06-4.5,10.06-10.06v-20.47c4.5,2.48,9.7,3.89,15.21,3.89,17.38,0,31.49-14.1,31.49-31.48.05-17.39-14.05-31.49-31.43-31.49Zm0,44.47c-7.13,0-12.94-5.81-12.94-12.94s5.81-12.94,12.94-12.94,12.94,5.81,12.94,12.94-5.81,12.94-12.94,12.94Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m497.91,236.12v22.89c0,5.56,4.5,10.41,10.06,10.41h10.06v-63.02h-20.16v29.72h.05Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m507.96,201.6c6.52,0,11.83-5.31,11.83-11.83s-5.31-11.83-11.83-11.83h-11.83v11.83c0,6.52,5.31,11.83,11.83,11.83Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m534.6,177.95h-10.06v91.47h10.06c5.56,0,10.06-4.5,10.06-10.06v-71.36c0-5.56-4.5-10.06-10.06-10.06Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m421.49,242.94c.25-1.67.4-3.34.4-5.05,0-17.44-13.8-31.54-30.88-31.54s-30.88,14.1-30.88,31.54,13.8,31.54,30.88,31.54c10.51,0,19.81-5.41,25.37-13.65,1.26-1.87-.66-4.19-2.68-3.28-6.87,3.08-16.63,5.51-24.06,3.03-5.61-1.87-11.07-6.17-11.83-12.58h43.66Zm-30.47-21.58h0c6.82,0,11.02,3.94,12.53,9.85h-25.62c1.21-5.26,6.37-9.85,13.09-9.85Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
      <path
        d="m123.02,19.51c-26.73-1.97-50.29,3.18-64.23,15.62-11.47-5.76-22.74-11.78-34.16-17.59C37.46,7.33,58.68,1.42,81.78.21c4.4-.25,13.54-.3,19.31,0,2.22.1,4.55-.2,5.86.25,1.36.45,3.13,3.13,4.4,4.65,4.04,4.8,8.44,9.96,11.67,14.4Z"
        style={{
          fill: "#4c9cd3",
          fillRule: " evenodd",
          strokeWidth: 0,
        }}
      />
      <path
        d="m115.49,1.67c-.05-.2.1-.25.25-.25,36.89,4.5,67.32,15.57,95.21,29.06-1.36,4.04-2.38,8.39-3.64,12.43-20.37-9.35-43.46-17.94-70.1-21.98-1.82-.3-3.99-.2-5.36-.71-1.42-.56-2.68-2.63-4.14-4.4-1.42-1.67-2.88-3.23-3.89-4.4-3.28-3.74-5.46-7.02-8.34-9.75Z"
        style={{ fill: "#4c9cd3", fillRule: "evenodd", strokeWidth: 0 }}
      />
      <path
        d="m19.77,21.23c11.88,5.71,23.15,12.03,34.92,17.84-5.81,7.18-10.21,16.27-10.26,28.1-.05,18.7,9.25,34.62,17.08,46.14.45.71,1.21,1.52,1.21,2.43-16.22-.4-31.99-1.21-48.11-1.72-1.26-1.16-1.92-2.83-2.68-4.4C5.21,95.98-1.26,80.61.21,58.58c.56-8.19,3.39-15.26,6.57-21.23,3.59-6.62,7.63-11.72,12.99-16.12Z"
        style={{
          fill: "#4c9cd3",
          fillRule: " evenodd",
          strokeWidth: 0,
        }}
      />
      <path
        d="m259.52,75.56c-14.1-11.22-29.67-21.02-46.34-29.67,1.01-4.35,2.32-8.44,3.44-12.68.2-.05.25.1.25.25,14.2,7.23,27.95,15.21,40.78,24.16,8.04,5.61,15.77,11.62,22.99,17.94h-21.12Z"
        style={{ fill: "#4c9cd3", fillRule: "evenodd", strokeWidth: 0 }}
      />
      <path
        d="m19.26,122.06c13.24.51,29.21.96,43.21,1.47,2.17.1,4.65-.4,6.12,0,1.26.35,3.59,3.64,5.1,5.36,24.41,27.54,55.29,47.71,91.32,63.48-3.34,11.37-6.57,22.89-9.75,34.42-3.59-.96-7.08-2.63-10.51-4.14-24.16-10.82-45.99-23.85-66.15-38.81-23.1-17.13-43.97-37.4-59.33-61.76Z"
        style={{
          fill: "#4c9cd3",
          fillRule: " evenodd",
          strokeWidth: 0,
        }}
      />
      <path
        d="m344.12,174.61c.61,3.94.61,8.49.4,12.63-1.31,23.55-15.62,38.66-31.74,47.86-4.7-4.85-9-10.31-13.44-15.62-3.79-4.55-7.93-8.89-11.72-13.7-.4-.51-1.16-1.21-.96-1.97,13.24-5.66,23.5-14.3,27.44-29.21h30.02Z"
        style={{ fill: "#4c9cd3", fillRule: "evenodd", strokeWidth: 0 }}
      />
      <path
        d="m306.42,238.29c-19.36,9-47.96,11.47-75.2,8.54-25.57-2.73-48.57-9.55-68.83-16.83,3.44-11.17,5.91-23.55,9.5-34.16.15-.4,0-.96.25-.51,15.77,5.66,31.23,10.82,50.03,13.7,16.73,2.58,35.68,2.83,51.3-.71,1.87-.4,5.05-1.87,6.12-1.72.96.15,2.38,2.12,3.64,3.64,7.99,9.45,15.46,18.35,23.2,28.05Z"
        style={{
          fill: "#4c9cd3",
          fillRule: " evenodd",
          strokeWidth: 0,
        }}
      />
      <path
        d="m177.55,71.11v-7.33h15.31v31.89h-15.31c-1.87-5.31-5.05-9.5-9.6-12.68-4.5-3.13-9.55-4.7-15.06-4.7-5.15,0-9.7,1.16-13.65,3.54s-7.02,5.71-9.15,10.06c-2.17,4.3-3.23,9.35-3.23,15.06,0,9.75,2.38,17.44,7.08,23.1,4.7,5.61,11.22,8.44,19.46,8.44,5.46,0,9.96-1.26,13.44-3.84,3.49-2.58,6.01-6.27,7.48-11.07h-15.87v-15.31h42.35c0,9.8-1.92,18.19-5.81,25.12-3.89,6.97-9.45,12.28-16.73,15.97-7.28,3.64-15.97,5.51-26.08,5.51s-19.1-1.87-26.73-5.56c-7.63-3.69-13.49-9.05-17.59-16.02-4.09-6.97-6.17-15.21-6.17-24.76,0-8.95,2.07-16.93,6.22-23.95s9.85-12.48,17.13-16.37c7.23-3.94,15.41-5.86,24.56-5.86,11.98-.05,21.28,2.88,27.95,8.79Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m264.32,90.27v18.8c-5.51.35-9.85,1.21-13.09,2.58s-5.76,3.74-7.53,7.08-2.68,8.14-2.68,14.35v6.22h12.48v14.45h-44.02v-14.45h11.52v-33.61h-10.92v-14.71h29.26v15.97c1.47-4.8,4.19-8.79,8.19-12.03,3.99-3.18,8.89-4.8,14.81-4.8.86.05,1.52.05,1.97.15Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m328.86,116.04v23.25h10.16v14.45h-30.12v-4.9c-5.71,3.89-11.37,5.86-17.08,5.86-6.87,0-12.38-1.77-16.53-5.31s-6.22-8.34-6.22-14.35c0-6.37,2.32-11.47,6.97-15.31,4.65-3.84,10.82-5.76,18.5-5.76,2.68,0,5.05.3,7.13.86s4.5,1.57,7.28,2.98v-5c0-3.18-1.01-5.71-2.98-7.63-1.97-1.92-4.95-2.88-8.95-2.88-4.35,0-8.19,1.67-11.52,5l-16.12-2.93c4.9-9.85,15.01-14.81,30.32-14.81,10.51,0,17.99,2.17,22.49,6.47,4.45,4.3,6.67,11.02,6.67,20.01Zm-32.55,26.18c3.59,0,7.78-2.17,12.58-6.47v-5.61c-4.3-2.78-8.24-4.19-11.83-4.19-2.88,0-5.05.66-6.57,2.02s-2.27,3.28-2.27,5.81c.05,5.61,2.73,8.44,8.09,8.44Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m413,113.72v25.57h9.45v14.45h-29.51v-38.41c0-2.93-.45-5.1-1.36-6.42-.91-1.31-2.38-2.02-4.5-2.02-3.84,0-7.68,1.36-11.52,4.14v28.25h9.8v14.45h-39.22v-14.45h9.55v-33.61h-9.55v-14.71h29.46v5.91c5.96-4.5,12.08-6.77,18.4-6.77s10.97,1.97,14.2,5.96c3.18,3.99,4.8,9.85,4.8,17.64Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m468.54,139.29v14.45h-38.76v-14.45h9.55v-33.61h-9.55v-14.71h29.46v48.31h9.3Zm-8.04-66.15c0,3.18-1.06,5.81-3.23,7.93-2.17,2.07-4.75,3.13-7.73,3.13s-5.61-1.06-7.78-3.23c-2.17-2.17-3.23-4.75-3.23-7.78s1.06-5.56,3.23-7.73,4.75-3.23,7.78-3.23,5.56,1.06,7.73,3.23c2.12,2.12,3.23,4.7,3.23,7.68Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m508.92,90.97v14.71h-11.57v27.39c0,1.97.35,3.44,1.11,4.4.76.96,2.17,1.47,4.25,1.47,1.72,0,3.79-.45,6.22-1.31v13.9c-5.36,2.12-10.41,3.18-15.16,3.18-5.46,0-9.55-1.36-12.28-4.14s-4.09-6.67-4.09-11.72v-33.15h-7.93v-14.71h7.93v-9.75l19.96-14.35v24.06h11.57v.05Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m582.66,126.55h-46.8c.45,4.8,2.12,8.64,5,11.42,2.83,2.83,6.52,4.19,11.02,4.19,4.95,0,9.85-2.48,14.55-7.43l15.52,5c-3.44,5.15-7.68,9-12.74,11.57s-11.07,3.84-17.99,3.84-13.04-1.31-18.35-3.89c-5.31-2.63-9.45-6.32-12.38-11.07-2.93-4.8-4.4-10.41-4.4-16.83s1.52-12.13,4.55-17.33c3.03-5.15,7.18-9.25,12.38-12.13,5.21-2.93,10.82-4.4,16.93-4.4,6.62,0,12.38,1.52,17.28,4.55,4.9,3.03,8.69,7.38,11.32,12.94,2.68,5.66,4.04,12.18,4.09,19.56Zm-33.61-23.7c-3.59,0-6.47,1.06-8.64,3.08-2.17,2.07-3.54,5.26-4.09,9.5h26.18c-.56-3.74-2.07-6.77-4.55-9.1-2.53-2.32-5.46-3.49-8.89-3.49Z"
        style={{ fill: "#4c9cd3", strokeWidth: 0 }}
      />
      <path
        d="m594.53,183.86c1.06,0,2.07.2,3.08.61.96.4,1.82.96,2.53,1.67s1.31,1.57,1.72,2.53.66,2.02.66,3.13-.2,2.12-.66,3.08c-.4.96-1.01,1.82-1.72,2.53s-1.57,1.26-2.53,1.72c-.96.4-2.02.66-3.08.66s-2.12-.2-3.08-.61c-.96-.4-1.82-.96-2.53-1.72-.71-.71-1.31-1.57-1.72-2.53s-.66-2.02-.66-3.13.2-2.17.66-3.13c.4-.96,1.01-1.82,1.72-2.53s1.57-1.26,2.53-1.67c1.01-.4,2.02-.61,3.08-.61Zm0,1.31c-.91,0-1.77.15-2.58.51s-1.52.81-2.12,1.42c-.61.61-1.11,1.31-1.47,2.12-.35.81-.51,1.67-.51,2.63,0,.91.15,1.82.51,2.63s.81,1.52,1.42,2.12c.61.61,1.31,1.06,2.12,1.42.81.35,1.67.51,2.58.51s1.82-.2,2.63-.56,1.52-.86,2.12-1.47c.61-.61,1.06-1.31,1.42-2.12.35-.81.51-1.67.51-2.58s-.15-1.82-.51-2.63-.81-1.52-1.47-2.12c-.61-.61-1.31-1.06-2.12-1.42-.76-.3-1.62-.45-2.53-.45Zm-2.98,2.22h3.08c.25,0,.56,0,.96.05.35.05.71.1,1.01.25s.61.4.81.71.35.76.35,1.36c0,.4-.05.76-.2,1.01-.1.25-.25.45-.45.66-.15.15-.35.3-.56.35-.2.1-.35.15-.51.2.3.05.56.1.71.2.15.1.3.25.4.4.1.15.15.4.2.61.05.25.1.51.15.76,0,.2.05.4.05.61.05.2.05.45.1.66s.1.45.15.66c.05.2.1.35.15.45h-1.92c0-.1-.05-.3-.05-.61-.05-.3-.05-.61-.1-.91-.05-.3-.1-.61-.1-.86-.05-.25-.05-.4-.05-.45-.05-.25-.15-.45-.25-.56-.1-.15-.35-.2-.66-.2h-1.57v3.59h-1.72v-8.95h0Zm1.67,3.89h1.42c.3,0,.51-.05.71-.15.15-.1.3-.2.4-.3.1-.15.15-.25.2-.4s.05-.25.05-.4c0-.3-.05-.56-.15-.71-.1-.15-.2-.3-.35-.35-.15-.1-.3-.1-.45-.15h-1.82v2.48Z"
        style={{ fill: "#595555", strokeWidth: 0 }}
      />
    </svg>
  );
}

export default Brand;
