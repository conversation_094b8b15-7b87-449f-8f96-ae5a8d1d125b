import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function Box({
  tooltip,
  size,
  fill = "none",
  stroke = "currentColor",
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        fill={fill}
        stroke={stroke}
        size={size}
        disabled={disabled}
        onClick={onClick}
        viewBox="0 0 256 256"
      >
        <path
          d="M224,177.3V78.7a8.1,8.1,0,0,0-4.1-7l-88-49.5a7.8,7.8,0,0,0-7.8,0l-88,49.5a8.1,8.1,0,0,0-4.1,7v98.6a8.1,8.1,0,0,0,4.1,7l88,49.5a7.8,7.8,0,0,0,7.8,0l88-49.5A8.1,8.1,0,0,0,224,177.3Z"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="12"
        />
        <polyline
          points="177 152.5 177 100.5 80 47"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="12"
        />
        <polyline
          points="222.9 74.6 128.9 128 33.1 74.6"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="12"
        />
        <line
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="12"
          x1="128.9"
          x2="128"
          y1="128"
          y2="234.8"
        />
      </SvgBase2>
    </Tooltip1>
  );
}
