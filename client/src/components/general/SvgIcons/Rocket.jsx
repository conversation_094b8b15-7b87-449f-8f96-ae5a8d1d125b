const getHeightWidthClasses = (size) => {
  switch (size) {
    case "xs":
      return "w-2 h-2";
    case "sm":
      return "w-5 h-5";
    case "md":
      return "w-6 h-6";
    case "lg":
      return "w-10 h-10";
    case "xl":
      return "w-12 h-12";
    case "xxl":
      return "w-16 h-16";
    default:
      return "w-6 h-6";
  }
};

export default function Rocket({ size = "md" }) {
  return (
    <svg
      className={getHeightWidthClasses(size)}
      fill="none"
      stroke="currentColor"
      strokeWidth="32"
      viewBox="0 0 512 512"
    >
      <path d="M461.81,53.81a4.4,4.4,0,0,0-3.3-3.39c-54.38-13.3-180,34.09-248.13,102.17a294.9,294.9,0,0,0-33.09,39.08c-21-1.9-42-.3-59.88,7.5-50.49,22.2-65.18,80.18-69.28,105.07a9,9,0,0,0,9.8,10.4l81.07-8.9a180.29,180.29,0,0,0,1.1,18.3,18.15,18.15,0,0,0,5.3,11.09l31.39,31.39a18.15,18.15,0,0,0,11.1,5.3,179.91,179.91,0,0,0,18.19,1.1l-8.89,81a9,9,0,0,0,10.39,9.79c24.9-4,83-18.69,105.07-69.17,7.8-17.9,9.4-38.79,7.6-59.69a293.91,293.91,0,0,0,39.19-33.09C427.82,233.76,474.91,110.9,461.81,53.81Z" />
      <path d="M109.64,352a45.06,45.06,0,0,0-26.35,12.84C65.67,382.52,64,448,64,448s65.52-1.67,83.15-19.31A44.73,44.73,0,0,0,160,402.32" />
    </svg>
  );
}
