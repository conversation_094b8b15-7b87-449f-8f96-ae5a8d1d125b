import { Tooltip1 } from "@/components";
import { RefreshCw as RefreshIcon } from "lucide-react";

export default function Refresh({ stroke, tooltip, disabled, onClick }) {
  return (
    <Tooltip1 content={tooltip}>
      <button
        onClick={disabled ? undefined : onClick}
        disabled={disabled}
        className="inline-block w-6 h-6"
        style={{ pointerEvents: disabled ? "none" : "auto" }}
      >
        <RefreshIcon className="w-5 h-5 mt-1" stroke={stroke} />
      </button>
    </Tooltip1>
  );
}
