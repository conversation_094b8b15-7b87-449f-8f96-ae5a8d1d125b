import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function Checklist({
  tooltip,
  size,
  fill = "none",
  stroke = "currentColor",
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        size={size}
        disabled={disabled}
        onClick={onClick}
        viewBox="0 0 256 256"
      >
        <line
          fill={fill}
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
          x1="128"
          x2="216"
          y1="128"
          y2="128"
        />
        <line
          fill={fill}
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
          x1="128"
          x2="216"
          y1="64"
          y2="64"
        />
        <line
          fill={fill}
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
          x1="128"
          x2="216"
          y1="192"
          y2="192"
        />
        <polyline
          fill={fill}
          points="92 48 57.3 80 40 64"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
        />
        <polyline
          fill={fill}
          points="92 112 57.3 144 40 128"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
        />
        <polyline
          fill={fill}
          points="92 176 57.3 208 40 192"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="16"
        />
      </SvgBase2>
    </Tooltip1>
  );
}
