import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function PieChart({
  tooltip,
  size,
  fill = "none",
  stroke = "currentColor",
  strokeWidth = "1.5",
  strokeLinecap = "round",
  viewBox = "-3 0 28 24",
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        size={size}
        disabled={disabled}
        fill={fill}
        stroke={stroke}
        strokeLinecap={strokeLinecap}
        strokeWidth={strokeWidth}
        viewBox={viewBox}
        onClick={onClick}
      >
        <path d="M21.21 15.89A10 10 0 1 1 8 2.83" />
        <path d="M22 12A10 10 0 0 0 12 2v10z" />
      </SvgBase2>
    </Tooltip1>
  );
}
