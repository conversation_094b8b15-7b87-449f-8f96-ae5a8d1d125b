export default function SvgBase1({
  d,
  stroke = "currentColor",
  disabled = false,
  size = "md",
  onClick,
  children,
  ...rest
}) {
  const svgStyle = {
    pointerEvents: disabled ? "none" : "auto",
  };

  const getHeightWidthClasses = () => {
    switch (size) {
      case "xs":
        return "w-2 h-2";
      case "sm":
        return "w-4 h-4";
      case "md":
        return "w-5 h-5";
      case "lg":
        return "w-10 h-10";
      case "xl":
        return "w-12 h-12";
      case "xxl":
        return "w-16 h-16";
      default:
        return "w-6 h-6";
    }
  };

  const heightWidthClasses = getHeightWidthClasses();

  return (
    <svg
      className={`${heightWidthClasses} text-gray-800 dark:text-white cursor-pointer`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      onClick={onClick}
      {...svgStyle}
      {...rest}
    >
      {children ? (
        children
      ) : (
        <path
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d={d}
        />
      )}
    </svg>
  );
}
