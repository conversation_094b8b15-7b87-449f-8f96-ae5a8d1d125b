import { Tooltip1 } from "@/components";
import SvgBase2 from "./SvgBase2";

export default function Home({
  tooltip,
  size,
  fill = "currentColor",
  disabled,
  onClick,
}) {
  return (
    <Tooltip1 content={tooltip}>
      <SvgBase2
        fill={fill}
        viewBox="0 0 640 640"
        size={size}
        disabled={disabled}
        onClick={onClick}
        style={{ transform: 'translateX(3px) translateY(3px) scale(1.15)' }}
      >
        <path
          fill={fill}
          d="M469.666 216.45L271.078 33.749a34 34 0 0 0-47.062.98L41.373 217.373L32 226.745V496h176V328h96v168h176V225.958ZM248.038 56.771c.282 0 .108.061-.013.18c-.125-.119-.269-.18.013-.18ZM448 464H336V328a32 32 0 0 0-32-32h-96a32 32 0 0 0-32 32v136H64V240L248.038 57.356c.013-.012.014-.023.024-.035L448 240Z"
        />
      </SvgBase2>
    </Tooltip1>
  );
}
