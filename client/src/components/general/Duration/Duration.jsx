import React from 'react';
import {
  formatDecimalDuration,
  formatNaturalDurationShort,
  formatDuration
} from './durations';

/**
 * Component that displays a duration in various formats
 *
 * @param {Object} props
 * @param {number} props.seconds - Duration in seconds
 * @param {string} props.format - Format type: 'natural' or 'decimal'
 * @param {string} props.length - Length type: 'short' or 'long'
 * @param {Date} props.now - Optional current date reference (defaults to new Date())
 * @returns {React.Component}
 */
export default function DurationDisplay({
  seconds,
  format = 'natural',
  length = 'long',
  now = new Date()
}) {
  let formattedDuration;

  if (format === 'decimal') {
    formattedDuration = formatDecimalDuration(seconds, length === 'short', now);
  } else {
    if (length === 'short') {
      formattedDuration = formatNaturalDurationShort(seconds, now);
    } else {
      formattedDuration = formatDuration(seconds, now);
    }
  }

  return (
    <span className="whitespace-nowrap">
      {formattedDuration}
    </span>
  );
}
