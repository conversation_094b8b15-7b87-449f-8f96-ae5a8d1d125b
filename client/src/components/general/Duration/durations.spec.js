import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  getDateDifference,
  getDateDifferenceFromDates,
  formatDecimalDuration,
  formatNaturalDurationShort,
  formatDuration
} from './durations';

describe('durations', () => {
  describe('getDateDifferenceFromDates', () => {
    it('should calculate correct difference between two dates', () => {
      const start = new Date(2025, 1, 28); // Feb 28, 2025
      const end = new Date(2025, 2, 4);    // Mar 4, 2025

      const result = getDateDifferenceFromDates(start, end);

      expect(result.years).toBe(0);
      expect(result.months).toBe(0);
      expect(result.days).toBe(4); // Should be 4 days, not 1 month 1 day
    });

    it('should handle leap years correctly', () => {
      const start = new Date(2024, 1, 28); // Feb 28, 2024 (leap year)
      const end = new Date(2024, 2, 4);    // Mar 4, 2024

      const result = getDateDifferenceFromDates(start, end);

      expect(result.days).toBe(5); // In leap year, Feb 28 to Mar 4 is 5 days
    });

    it('should calculate months correctly', () => {
      const start = new Date(2025, 0, 15); // Jan 15, 2025
      const end = new Date(2025, 2, 15);   // Mar 15, 2025

      const result = getDateDifferenceFromDates(start, end);

      expect(result.years).toBe(0);
      expect(result.months).toBe(2);
      expect(result.days).toBe(0);
    });

    it('should handle year boundaries correctly', () => {
      const start = new Date(2024, 11, 30); // Dec 30, 2024
      const end = new Date(2025, 0, 2);     // Jan 2, 2025

      const result = getDateDifferenceFromDates(start, end);

      expect(result.years).toBe(0);
      expect(result.months).toBe(0);
      expect(result.days).toBe(3);
    });

    it('should swap dates if end date is before start date', () => {
      const start = new Date(2025, 2, 15); // Mar 15, 2025
      const end = new Date(2025, 0, 15);   // Jan 15, 2025 (earlier)

      const result = getDateDifferenceFromDates(start, end);

      expect(result.years).toBe(0);
      expect(result.months).toBe(2);
      expect(result.days).toBe(0);
    });

    it('should throw error if non-Date objects are provided', () => {
      expect(() => getDateDifferenceFromDates('not a date', new Date())).toThrow();
      expect(() => getDateDifferenceFromDates(new Date(), 'not a date')).toThrow();
      expect(() => getDateDifferenceFromDates(null, new Date())).toThrow();
    });
  });

  describe('getDateDifference', () => {
    it('should use the provided now parameter', () => {
      // Set "now" to March 15, 2025
      const now = new Date(2025, 2, 15, 12, 0, 0);

      // 4 days in seconds = 4 * 24 * 60 * 60 = 345600
      const result = getDateDifference(345600, now);

      expect(result.years).toBe(0);
      expect(result.months).toBe(0);
      expect(result.days).toBe(4);
      expect(result.hours).toBe(0);
    });

    it('should handle the Feb-Mar edge case correctly', () => {
      // Set "now" to March 4, 2025
      const now = new Date(2025, 2, 4, 12, 0, 0);

      // Calculate difference from Feb 28, 2025 (4 days ago)
      const seconds = 4 * 24 * 60 * 60;
      const result = getDateDifference(seconds, now);

      expect(result.months).toBe(0);
      expect(result.days).toBe(4);
    });

    it('should handle leap year Feb-Mar edge case correctly', () => {
      // Set "now" to March 4, 2024 (leap year)
      const now = new Date(2024, 2, 4, 12, 0, 0);

      // Calculate difference from Feb 28, 2024 (5 days ago in leap year)
      const seconds = 5 * 24 * 60 * 60;
      const result = getDateDifference(seconds, now);

      expect(result.months).toBe(0);
      expect(result.days).toBe(5);
    });
  });

  describe('formatDecimalDuration', () => {
    let consoleWarnSpy;

    beforeEach(() => {
      // Mock console.warn
      consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    it('should format seconds correctly', () => {
      expect(formatDecimalDuration(30)).toBe('30 secs');
      expect(formatDecimalDuration(1)).toBe('1 sec');
    });

    it('should format minutes correctly', () => {
      const now = new Date(2025, 2, 15, 12, 0, 0);
      expect(formatDecimalDuration(90, false, now)).toBe('1.5 mins');
      expect(formatDecimalDuration(60, false, now)).toBe('1 min');
    });

    it('should handle short format correctly', () => {
      const now = new Date(2025, 2, 15, 12, 0, 0);
      expect(formatDecimalDuration(90, true, now)).toBe('1.5m');
      expect(formatDecimalDuration(3600, true, now)).toBe('1h');
      expect(formatDecimalDuration(86400, true, now)).toBe('1d');
    });

    it('should handle invalid input', () => {
      expect(formatDecimalDuration(NaN)).toBe('-');
      expect(formatDecimalDuration('not a number')).toBe('-');
      expect(consoleWarnSpy).toHaveBeenCalled();
    });
  });

  describe('formatNaturalDurationShort', () => {
    let consoleWarnSpy;

    beforeEach(() => {
      consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    it('should format years and months correctly', () => {
      // Create a date and a date 1 year and 2 months in the past
      const now = new Date(2025, 2, 15);
      const then = new Date(2024, 0, 15); // Jan 15, 2024
      const seconds = (now - then) / 1000;

      expect(formatNaturalDurationShort(seconds, now)).toBe('1y 2mo');
    });

    it('should format small durations correctly', () => {
      expect(formatNaturalDurationShort(30)).toBe('a few s');
    });

    it('should handle invalid input', () => {
      expect(formatNaturalDurationShort(NaN)).toBe('--');
      expect(consoleWarnSpy).toHaveBeenCalled();
    });
  });

  describe('formatDuration', () => {
    let consoleWarnSpy;

    beforeEach(() => {
      consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    it('should format single year correctly', () => {
      // Create a date and a date 1 year in the past
      const now = new Date(2025, 2, 15);
      const then = new Date(2024, 2, 15);
      const seconds = (now - then) / 1000;

      expect(formatDuration(seconds, now)).toBe('1 year');
    });

    it('should format single month correctly', () => {
      // Create a date and a date 1 month in the past
      const now = new Date(2025, 2, 15);
      const then = new Date(2025, 1, 15);
      const seconds = (now - then) / 1000;

      expect(formatDuration(seconds, now)).toBe('1 month');
    });

    it('should format small durations correctly', () => {
      expect(formatDuration(30)).toBe('a few seconds');
    });

    it('should handle invalid input', () => {
      expect(formatDuration(NaN)).toBe('--');
      expect(consoleWarnSpy).toHaveBeenCalled();
    });
  });
});
