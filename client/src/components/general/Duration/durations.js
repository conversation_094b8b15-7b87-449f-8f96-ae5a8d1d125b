/**
 * Utility functions for duration calculations and formatting
 */

/**
 * Calculate real date differences between now and a point in time seconds ago
 * @param {number} seconds - Number of seconds in the past to calculate difference from
 * @param {Date} now - Current date (defaults to new Date())
 * @returns {Object} Object containing years, months, days, hours, minutes, seconds
 */
export const getDateDifference = (seconds, now = new Date()) => {
  // Create "then" date based on seconds difference from now
  const then = new Date(now.getTime() - seconds * 1000);

  return getDateDifferenceFromDates(then, now);
};

/**
 * Get date difference between two specific dates
 * @param {Date} startDate - The earlier date
 * @param {Date} endDate - The later date
 * @returns {Object} Object containing years, months, days, hours, minutes, seconds
 */
export const getDateDifferenceFromDates = (startDate, endDate) => {
  if (!(startDate instanceof Date) || !(endDate instanceof Date)) {
    throw new Error('Both arguments must be Date objects');
  }

  // Ensure startDate is earlier than endDate
  if (startDate > endDate) {
    [startDate, endDate] = [endDate, startDate];
  }

  // Initialize values
  let years = 0;
  let months = 0;
  let days = 0;
  let hours = 0;
  let minutes = 0;
  let seconds = 0;

  // Calculate years difference
  years = endDate.getFullYear() - startDate.getFullYear();

  // Adjust months
  months = endDate.getMonth() - startDate.getMonth();
  if (months < 0) {
    years--;
    months += 12;
  }

  // Adjust days
  days = endDate.getDate() - startDate.getDate();
  if (days < 0) {
    const lastDayOfLastMonth = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      0
    ).getDate();
    months--;
    if (months < 0) {
      years--;
      months += 12;
    }
    days += lastDayOfLastMonth;
  }

  // Calculate hours, minutes, seconds
  hours = endDate.getHours() - startDate.getHours();
  if (hours < 0) {
    days--;
    hours += 24;
  }

  minutes = endDate.getMinutes() - startDate.getMinutes();
  if (minutes < 0) {
    hours--;
    minutes += 60;
  }

  seconds = endDate.getSeconds() - startDate.getSeconds();
  if (seconds < 0) {
    minutes--;
    seconds += 60;
  }

  // Handle potential negative values after all calculations
  if (minutes < 0) {
    hours--;
    minutes += 60;
  }

  if (hours < 0) {
    days--;
    hours += 24;
  }

  if (days < 0) {
    months--;
    const lastDayOfMonth = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      0
    ).getDate();
    days += lastDayOfMonth;
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  return { years, months, days, hours, minutes, seconds };
};

/**
 * Format a duration in decimal format (e.g., "1.5 hours")
 * @param {number} seconds - Duration in seconds
 * @param {boolean} isShort - Whether to use short format (e.g., "1.5h" vs "1.5 hours")
 * @param {Date} now - Current date (defaults to new Date())
 * @returns {string} Formatted duration string
 */
export const formatDecimalDuration = (seconds, isShort = false, now = new Date()) => {
  // Handle invalid input
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    console.warn('Invalid duration provided to formatDecimalDuration:', seconds);
    return '-';
  }

  const withUnits = (n, abbreviation, word) => {
    return `${n}${isShort ? abbreviation : (n === 1) ? ` ${word}` : ` ${word}s`}`;
  };

  // Constants for time units in seconds
  const MINUTE = 60;
  const HOUR = MINUTE * 60;
  const DAY = HOUR * 24;
  const YEAR = DAY * 365;

  // Handle very small durations
  if (seconds < 60) {
    return withUnits(Math.round(seconds), 's', 'sec');
  }

  // Use Date calculations for months and years
  const diff = getDateDifference(seconds, now);

  // Determine the appropriate unit based on duration
  if (diff.years > 0) {
    // For decimal years, include partial months
    const decimalYears = diff.years + (diff.months / 12);
    const roundedYears = Math.round(decimalYears * 10) / 10;
    return withUnits(roundedYears, 'y', 'year');
  }

  if (diff.months > 0) {
    // For decimal months, include partial days
    const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
    const decimalMonths = diff.months + (diff.days / daysInMonth);
    const roundedMonths = Math.round(decimalMonths * 10) / 10;
    return withUnits(roundedMonths, 'mo', 'month');
  }

  if (diff.days > 0) {
    // Convert to decimal days, including hours
    const decimalDays = diff.days + (diff.hours / 24);
    const roundedDays = Math.round(decimalDays * 10) / 10;
    return withUnits(roundedDays, 'd', 'day');
  }

  if (diff.hours > 0) {
    // Convert to decimal hours, including minutes
    const decimalHours = diff.hours + (diff.minutes / 60);
    const roundedHours = Math.round(decimalHours * 10) / 10;
    return withUnits(roundedHours, 'h', 'hour');
  }

  // Convert to decimal minutes, including seconds
  const decimalMinutes = diff.minutes + (diff.seconds / 60);
  const roundedMinutes = Math.round(decimalMinutes * 10) / 10;
  return withUnits(roundedMinutes, 'm', 'min');
};

/**
 * Format a duration in natural short format (e.g., "1y 2mo" or "3h 45m")
 * @param {number} seconds - Duration in seconds
 * @param {Date} now - Current date (defaults to new Date())
 * @returns {string} Formatted duration string
 */
export const formatNaturalDurationShort = (seconds, now = new Date()) => {
  // Handle invalid input
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    console.warn('Invalid duration provided to formatNaturalDurationShort:', seconds);
    return '--';
  }

  // Handle very small durations
  if (seconds < 60) {
    return 'a few s';
  }

  // Use Date calculations
  const diff = getDateDifference(seconds, now);

  // Format the duration based on the largest unit
  if (diff.years > 0) {
    if (diff.months > 0) {
      return `${diff.years}y ${diff.months}mo`;
    }
    return `${diff.years}y`;
  }

  if (diff.months > 0) {
    if (diff.days > 0) {
      return `${diff.months}mo ${diff.days}d`;
    }
    return `${diff.months}mo`;
  }

  if (diff.days > 0) {
    if (diff.hours > 0) {
      return `${diff.days}d ${diff.hours}h`;
    }
    return `${diff.days}d`;
  }

  if (diff.hours > 0) {
    if (diff.minutes > 0) {
      return `${diff.hours}h ${diff.minutes}m`;
    }
    return `${diff.hours}h`;
  }

  return `${diff.minutes}m`;
};

/**
 * Format a duration in natural long format (e.g., "1 year 2 months" or "3 hours 45 minutes")
 * @param {number} seconds - Duration in seconds
 * @param {Date} now - Current date (defaults to new Date())
 * @returns {string} Formatted duration string
 */
export const formatDuration = (seconds, now = new Date()) => {
  // Handle invalid input
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    console.warn('Invalid duration provided to formatDuration:', seconds);
    return '--';
  }

  // Handle very small durations
  if (seconds < 60) {
    return 'a few seconds';
  }

  // Use Date calculations
  const diff = getDateDifference(seconds, now);

  // Format the duration based on the largest unit
  if (diff.years > 0) {
    if (diff.months > 0) {
      return `${diff.years} ${diff.years === 1 ? 'year' : 'years'} ${diff.months} ${diff.months === 1 ? 'month' : 'months'}`;
    }
    return `${diff.years} ${diff.years === 1 ? 'year' : 'years'}`;
  }

  if (diff.months > 0) {
    if (diff.days > 0) {
      return `${diff.months} ${diff.months === 1 ? 'month' : 'months'} ${diff.days} ${diff.days === 1 ? 'day' : 'days'}`;
    }
    return `${diff.months} ${diff.months === 1 ? 'month' : 'months'}`;
  }

  if (diff.days > 0) {
    if (diff.hours > 0) {
      return `${diff.days} ${diff.days === 1 ? 'day' : 'days'} ${diff.hours} ${diff.hours === 1 ? 'hour' : 'hours'}`;
    }
    return `${diff.days} ${diff.days === 1 ? 'day' : 'days'}`;
  }

  if (diff.hours > 0) {
    if (diff.minutes > 0) {
      return `${diff.hours} ${diff.hours === 1 ? 'hour' : 'hours'} ${diff.minutes} ${diff.minutes === 1 ? 'minute' : 'minutes'}`;
    }
    return `${diff.hours} ${diff.hours === 1 ? 'hour' : 'hours'}`;
  }

  return `${diff.minutes} ${diff.minutes === 1 ? 'minute' : 'minutes'}`;
};
