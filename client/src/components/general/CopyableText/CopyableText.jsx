import React from "react";
import { Copy as CopyIcon } from "lucide-react";
import { Tooltip1 } from "@/components";

function CopyableText({ text }) {
  const [copyText, setCopyText] = React.useState("Copy");

  const handleCopyClick = async () => {
    await navigator.clipboard.writeText(text);
    setCopyText("Copied");
    setTimeout(() => setCopyText("Copy"), 2000);
  };

  return (
    <Tooltip1 content={copyText}>
      <CopyIcon
        size={13}
        className="ml-1 cursor-pointer text-sm opacity-30 hover:opacity-100 duration-300"
        onClick={handleCopyClick}
      />
    </Tooltip1>
  );
}

export default CopyableText;
