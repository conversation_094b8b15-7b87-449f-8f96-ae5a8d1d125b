import { Tooltip } from 'flowbite-react';
import './navButton1.css';

function NavButton1({
  name,
  isActive = false,
  href,
  onClick,
  children,
}) {
  return (
    <Tooltip
      content={name}
      className={`text-sm whitespace-nowrap bg-stone-500 ${isActive ? 'hidden' : ''}`}
      animation="duration-300"
      arrow={false}
      style="dark"
      placement="right"
      trigger="hover"
    >
      <div className="navButtonContainer relative">
        <a
          href={href}
          className="navButton relative flex items-center justify-center cursor-pointer hover:text-white transition duration-300 ease-in-out"
          style={{
            height: 56,
            width: 56,
            color: isActive ? 'white' : '#b0b0b0',
            backgroundColor: isActive ? 'var(--brand-blue)' : '',
          }}
          onClick={onClick}
        >
          {children}
        </a>
      </div>
    </Tooltip>
  );
}

export default NavButton1;
