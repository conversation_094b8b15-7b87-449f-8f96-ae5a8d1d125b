import {
  Signal,
  SignalZero,
  SignalLow,
  SignalMedium,
  SignalHigh,
} from 'lucide-react';
import { Tooltip1 } from '@/components';

const SignalStrength = ({
  rssi,
  quality = 'No Signal',
  isOnline,
  size = 20,
  lastChecked,
  className = ""
}) => {
  if (!rssi) {
    return <></>;
  }

  const qualityMap = {
    'No Signal': 0,
    'Poor': 1,
    'OK': 2,
    'Good': 3,
    'Excellent': 4,
  };

  const strength = qualityMap[quality] || 0;

  return (
    <Tooltip1
      content={
        <>
          {rssi} dBm | {quality}
        </>
      }
    >
      <div
        className={`${className} ${isOnline ? 'opacity-100' : 'opacity-30'}`}
        style={{ width: size, height: size }}
      >
        <div className="relative pointer-events-auto">
          {strength === 0 && <SignalZero size={size} className="text-black" />}
          {strength === 1 && <SignalLow size={size} className="text-red-500" />}
          {strength === 2 && <SignalMedium size={size} className="text-yellow-300" />}
          {strength === 3 && <SignalHigh size={size} className="text-lime-400" />}
          {strength === 4 && <Signal size={size} className="text-green-500" />}
        </div>
      </div>
    </Tooltip1>
  );
};

export default SignalStrength;
