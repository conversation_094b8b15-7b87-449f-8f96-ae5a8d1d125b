import React, { useEffect, useState } from "react";
import { Tooltip1 } from "@/components";
import { X } from 'lucide-react';

function parseUptime(uptime) {
  // Check if uptime string is empty or doesn't match the expected format
  if (uptime === "" || !/(\d+(h|m|s|d))+/.test(uptime)) {
    throw new Error(`Invalid uptime format: ${uptime}`);
  }

  // Parse uptime string to get total seconds
  const uptimeParts = uptime.match(/(\d+)(h|m|s|d)/g);
  let totalSeconds = 0;
  for (let part of uptimeParts) {
    const value = parseInt(part.slice(0, -1));
    const unit = part.slice(-1);
    if (unit === 'h') {
      totalSeconds += value * 60 * 60;
    } else if (unit === 'm') {
      totalSeconds += value * 60;
    } else if (unit === 's') {
      totalSeconds += value;
    } else if (unit === 'd') {
      totalSeconds += value * 24 * 60 * 60;
    }
  }
  return totalSeconds;
}

function formatDuration(totalSeconds) {
  if (totalSeconds < 60) {
    return "0m";
  } else if (totalSeconds < 180 * 60) { // Up to 180 minutes
    const minutes = Math.floor(totalSeconds / 60);
    return `${minutes}m`;
  } else if (totalSeconds < 96 * 60 * 60) { // Up to 96 hours
    const hours = Math.floor(totalSeconds / (60 * 60));
    return `${hours}h`;
  } else {
    const days = Math.floor(totalSeconds / (24 * 60 * 60));
    return `${days}d`;
  }
}

const OnlineIcon = ({ uptime }) => {
  const formattedUptime = uptime ? formatDuration(parseUptime(uptime)) : '';

  return (
    <div className="relative inline-block">
      {/* Phantom container with fixed width to maintain consistent sizing */}
      <div className="invisible inline-flex items-center px-2 h-5"></div>
      {/* Actual content container */}
      <div className="absolute top-1 left-1/2 transform -translate-x-1/2 inline-flex items-center justify-center px-1.5 rounded-full bg-green-500 text-white text-xs font-medium">
        {formattedUptime}
      </div>
    </div>
  );
};


const OfflineIcon = () => (
  <div className="relative inline-block mt-1.5 w-4 h-4 scale-90">
    <div className="absolute inset-0 rounded-full bg-red-500" />
    <div className="absolute inset-0 flex items-center justify-center">
      <X
        className="w-3 h-3"
        color="white"
        strokeWidth={2}
      />
    </div>
  </div>
);

const BusyIcon = ({ startTime, duration }) => {
  const [offset, setOffset] = useState(0);
  const [remainingTime, setRemainingTime] = useState("");
  const radius = 50;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    if (startTime && duration) {
      const start = Date.parse(startTime);
      const intervalId = setInterval(() => {
        const elapsedSeconds = (Date.now() - start) / 1000;
        if (elapsedSeconds <= duration) {
          setOffset((elapsedSeconds / duration) * circumference);
          const remainingSeconds = duration - elapsedSeconds;
          const minutes = Math.floor(remainingSeconds / 60);
          const seconds = Math.floor(remainingSeconds % 60);
          setRemainingTime(`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`);
        } else {
          clearInterval(intervalId);
        }
      }, 100);

      return () => clearInterval(intervalId);
    }
  }, [duration, circumference, startTime]);

  return (
    <Tooltip1 content={`Busy ${remainingTime}...`}>
      <svg width="16" height="16" viewBox="0 0 120 120">
        <circle
          cx="60"
          cy="60"
          r="50"
          stroke="var(--vivid-green)"
          strokeWidth="10"
          fill="white"
         />
        {startTime && duration && (
          <circle
            cx="60"
            cy="60"
            r="25"
            stroke="var(--vivid-green)"
            strokeWidth="50"
            fill="white"
            strokeDasharray={circumference}
            strokeDashoffset={circumference - offset}
            transform="rotate(-90 60 60)"
          />
        )}
      </svg>
    </Tooltip1>
  );
};

const UnknownIcon = () => (
  <div className="relative">
    <svg className="relative" width="16" height="16" viewBox="0 0 120 120">
      <circle cx="60" cy="60" r="50" stroke="var(--red) " strokeWidth="10" fill="#e7e5e4" />
    </svg>
  </div>
);

function OnlineStatus({ status, uptime, bootTime, startTime, duration }) {
  switch (status) {
    case true:
    case "online":
      return <OnlineIcon uptime={uptime} />;
    case false:
    case null:
    case "offline":
      return <OfflineIcon />;
    case "busy":
      return <BusyIcon startTime={startTime} duration={duration} />;
    case undefined:
    case "unknown":
      return <UnknownIcon />;
    default:
      return null;
  }
}

export default OnlineStatus;
