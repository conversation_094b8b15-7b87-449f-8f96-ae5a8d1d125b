import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToastr } from "@/hooks";
import { fetchWrapper } from "@/api";

/**
 * Hook to create/update/delete data or perform server side-effects
 */

export function useMutate() {
  const toastr = useToastr();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ api, endpoint, method, body }) => {
      return fetchWrapper({ api, endpoint, method, body });
    },
    onError: (error, { endpoint, method, errorMsg }) => {
      if (errorMsg) {
        toastr.error(errorMsg);
        return;
      }

      toastr.error(error.message);

      console.log(
        `Failed to perform ${method} on ${endpoint} - ${error.message}`
      );
    },
    onSuccess: (_, { successMsg, suppressSuccessMsg }) => {
      if (!suppressSuccessMsg && successMsg) {
        toastr.success(successMsg);
      }
    },
    onSettled: (_, __, { invalidateQueries, endpoint }) => {
      queryClient.invalidateQueries(invalidateQueries || endpoint);
    },
  });
}
