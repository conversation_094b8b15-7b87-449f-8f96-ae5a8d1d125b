import { useEffect } from 'react';
import { useFetch } from '@/hooks';

export default function useVersionCheck() {
  const { data: version } = useFetch({
    endpoint: '/qc/version',
    refetchInterval: 1 * 60 * 1000,
    suppressErrors: true,
  });

  useEffect(() => {
    if (version) {
      const storedVersion = localStorage.getItem('version');
      const currentVersion = `${version.major}.${version.minor}.${version.patch}`;

      if (storedVersion && storedVersion !== currentVersion) {
        localStorage.setItem('version', currentVersion);
        location.reload();
      } else if (!storedVersion) {
        localStorage.setItem('version', currentVersion);
      }
    }
  }, [version]);
}
