import { useQuery } from "@tanstack/react-query";
import { useToastr } from "@/hooks";
import { fetchWrapper } from "@/api";

/**
 * Hook to fetch data from server
 */

export function useFetch({
  endpoint,
  enabled = true,
  suppressErrors = false,
  ...rest
}) {
  const toastr = useToastr();

  return useQuery({
    queryKey: [endpoint],
    enabled: <PERSON><PERSON><PERSON>(endpoint) && enabled,
    queryFn: () => fetchWrapper({ endpoint }),
    throwOnError: (error) => {
      if (suppressErrors) {
        return;
      }

      toastr.error(`Failed to fetch from ${endpoint} - ${error.message}`);
    },
    ...rest,
  });
}
