import { useStore } from "@/store";
import { APIS, API_BASE_URLS } from "@/constants";

const getToken = (api) => {
  const userData = useStore.getState().user.data;

  const myEpikToken = localStorage.getItem("jwtToken");
  const qcToken = userData?.token;

  const { VITE_TEST_TOKEN } = import.meta.env;

  const tokens = {
    [APIS.QC]: myEpikToken,
    [APIS.MYEPIK]: myEpikToken,
  };

  return tokens[api] || VITE_TEST_TOKEN;
};

// URL for redirects.
const myEpik = 'https://my.epik.io/';

// fetchWrapper will call qc-api by default
export async function fetchWrapper({
  api = APIS.QC,
  endpoint,
  method = "GET",
  body,
  responseType = "json", // New parameter to specify expected response type
}) {
  const url = `${API_BASE_URLS[api]}${endpoint}`;
  const token = getToken(api);

  const options = {
    method,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  const isFormData = body instanceof FormData;

  if (isFormData) {
    options.body = body;
  } else if (body) {
    options.headers["Content-Type"] = "application/json";
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, options);
    const { ok, status, statusText } = response;

    // Handle authentication errors specifically
    if (status === 401) {
      // Clear the invalid token
      localStorage.removeItem("jwtToken");

      // Redirect to my.epik.io
      window.location.href = myEpik;
      throw new Error('Authentication failed - redirecting to login');
    }

    if (!ok) {
      // There's something wrong.

      // Clone the response before first attempt to avoid reading twice.
      const responseClone = response.clone();

      try {
        const errorData = await response.json();
        throw new Error(errorData.error || statusText);
      } catch (jsonError) {
        // If JSON parsing fails, use the cloned response for text
        const errorText = await responseClone.json();
        throw new Error(errorText.error || statusText);
      }
    }

    // Process the response based on the expected type
    if (responseType === "text") {
      return await response.text();
    } else if (responseType === "blob") {
      return await response.blob();
    } else if (responseType === "arrayBuffer") {
      return await response.arrayBuffer();
    } else {
      // Default to JSON, but check Content-Type to be sure
      const contentType = response.headers.get("Content-Type");
      if (contentType && contentType.includes("application/json")) {
        return await response.json();
      } else {
        // If server didn't return JSON despite expectation, return as text
        return await response.text();
      }
    }
  } catch (error) {
    throw error;
  }
}
