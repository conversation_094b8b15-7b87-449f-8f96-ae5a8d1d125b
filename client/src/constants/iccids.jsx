import React, { useState } from 'react';

/**
 * Compact ICCID display
 */
export const RobustICCID = ({ iccid }) => {
  // Parse ICCID
  const parsedData = parseICCID(iccid);

  if (!parsedData.valid) {
    return (
      <div className="inline-flex items-center">
        <div className="font-mono text-sm bg-red-50">
          <span className="text-red-700">{iccid || "Invalid ICCID"}</span>
        </div>
        <span className="ml-2 text-xs text-red-600">{parsedData.error}</span>
      </div>
    );
  }

  // ICCID is valid
  return (
    <div className="inline-flex items-center">
      {/* ICCID with special spacing */}
      <div
        className="font-mono text-sm bg-gray-50"
        style={{ borderLeftColor: parsedData.carrierColor }}
      >
        {parsedData.formattedParts.map((part, index) => (
          <span key={index} className={index > 0 ? "ml-1" : ""}>
            {part}
          </span>
        ))}
      </div>

      {/* Separator */}
      <span className="mx-1 text-gray-400">|</span>

      {/* Carrier name with dot indicator */}
      <div className="flex items-center">
        <div
          className="w-2 h-2 rounded-full mr-1"
          style={{ backgroundColor: parsedData.carrierColor }}
        ></div>
        <span className="text-xs font-medium">{parsedData.carrierName}</span>
      </div>

      {/* Country */}
      <span className="mx-1 text-gray-400">|</span>
      <span className="text-xs text-gray-600">{parsedData.country || "Unknown country"}</span>
    </div>
  );
};

/**
 * Detailed SIM card information display
 */
const SIMDetails = ({ iccid }) => {
  const parsedData = parseICCID(iccid);

  if (!parsedData.valid) {
    return (
      <div className="bg-red-50 p-3 rounded border border-red-200">
        <p className="text-red-700">Invalid ICCID: {parsedData.error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4">
        <CompactICCID iccid={iccid} />
      </div>

      <div className="grid grid-cols-2 gap-3 text-sm">
        <div>
          <span className="text-gray-500">Industry ID:</span>
          <span className="ml-1 font-mono">{parsedData.industryID}</span>
        </div>

        <div>
          <span className="text-gray-500">Country Code:</span>
          <span className="ml-1 font-mono">{parsedData.countryCode}</span>
        </div>

        <div>
          <span className="text-gray-500">Provider Code:</span>
          <span className="ml-1 font-mono">{parsedData.providerCode}</span>
        </div>

        <div>
          <span className="text-gray-500">Format:</span>
          <span className="ml-1">{parsedData.format}</span>
        </div>

        <div>
          <span className="text-gray-500">Network Type:</span>
          <span className="ml-1">{
            parsedData.carrierName.includes('IoT') ? 'IoT/M2M' : 'Standard'
          }</span>
        </div>

        <div>
          <span className="text-gray-500">POTS Compatible:</span>
          <span className={`ml-1 ${
            parsedData.carrierName.includes('IoT')
              ? 'text-green-600 font-medium'
              : 'text-blue-600'
          }`}>
            {parsedData.carrierName.includes('IoT') ? 'Ideal' : 'Compatible'}
          </span>
        </div>
      </div>

      <div className="mt-4 pt-3 border-t border-gray-100">
        <h4 className="text-sm font-medium mb-2">Status Information</h4>
        <div className="flex space-x-4">
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
            <span className="text-xs">Active</span>
          </div>
          <div className="flex items-center">
            <div className="flex space-x-0.5">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="w-1 h-2 bg-green-500 rounded-sm"></div>
              ))}
            </div>
            <span className="text-xs ml-1">Signal</span>
          </div>
          <div className="flex items-center">
            <span className="text-xs font-mono">123.45.67.89</span>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Parse ICCID and extract all needed information
 */
function parseICCID(iccid) {
  // Handle null/undefined/empty
  if (!iccid) {
    return {
      valid: false,
      error: "ICCID is missing"
    };
  }

  // Clean any existing spaces
  const cleanICCID = iccid.replace(/\s+/g, '');

  // Basic validation
  if (/[^0-9]/.test(cleanICCID)) {
    return {
      valid: false,
      error: "ICCID contains non-numeric characters"
    };
  }

  if (cleanICCID.length < 19 || cleanICCID.length > 20) {
    return {
      valid: false,
      error: `Invalid length: ${cleanICCID.length} digits (should be 19-20)`
    };
  }

  // Extract standard segments
  const industryID = cleanICCID.substring(0, 2);
  const countryCode = cleanICCID.substring(2, 4);
  const providerCode = cleanICCID.substring(4, 7);

  // Brand color constants
  const BRAND_GREEN = "#E20074";   // T-Mobile
  const BRAND_BLUE = "#00A8E0";    // AT&T
  const BRAND_PURPLE = "#333333";  // Verizon
  const GRAY = "#6B7280";          // Unknown

  // Carrier information
  let carrierName = "Unknown carrier";
  let carrierColor = GRAY;
  let country = getCountryByCode(countryCode);
  let format = "standard";
  let formattedParts;

  // Identify carrier

  // Special case for Verizon alternate format
  if (countryCode === "14" && providerCode.startsWith("8")) {
    carrierName = "Verizon";
    carrierColor = BRAND_PURPLE;
    format = "verizon_alt";
    formattedParts = formatWithAlternateSpacing(cleanICCID);
  }
  // Standard formats
  else {
    // AT&T family
    if ((countryCode === "01" && providerCode === "310") ||
        (countryCode === "01" && providerCode === "410")) {
      carrierName = "AT&T";
      carrierColor = BRAND_BLUE;
    }
    else if (countryCode === "01" && providerCode === "150") {
      carrierName = "AT&T IoT";
      carrierColor = BRAND_BLUE;
    }
    // Verizon family
    else if (countryCode === "01" && providerCode === "004") {
      carrierName = "Verizon";
      carrierColor = BRAND_PURPLE;
    }
    else if (countryCode === "01" && providerCode === "012") {
      carrierName = "Verizon IoT";
      carrierColor = BRAND_PURPLE;
    }
    // T-Mobile family
    else if (countryCode === "01" && providerCode === "260") {
      carrierName = "T-Mobile";
      carrierColor = BRAND_GREEN;
    }
    else if (countryCode === "01" && providerCode === "240") {
      carrierName = "T-Mobile IoT";
      carrierColor = BRAND_GREEN;
    }
    // Sprint (now T-Mobile) special format
    else if (countryCode === "31") {
      carrierName = "T-Mobile (Sprint)";
      carrierColor = BRAND_GREEN;
      format = "sprint_alt";
      formattedParts = formatWithAlternateSpacing(cleanICCID);
    }

    // Use standard formatting if not special case
    if (!formattedParts) {
      formattedParts = formatWithStandardSpacing(cleanICCID);
    }
  }

  // Return complete parsed information
  return {
    valid: true,
    industryID,
    countryCode,
    providerCode,
    carrierName,
    carrierColor,
    country,
    format,
    formattedParts
  };
}

/**
 * Format an ICCID with standard spacing (2-2-3-3-4-4-1)
 */
function formatWithStandardSpacing(iccid) {
  const clean = iccid.replace(/\s+/g, '');

  return [
    clean.substring(0, 2),         // Industry (89)
    clean.substring(2, 4),         // Country (01)
    clean.substring(4, 7),         // Provider (310)
    clean.substring(7, 10),        // First 3 of unique ID
    clean.substring(10, 14),       // Next 4 of unique ID
    clean.substring(14, 18),       // Next 4 of unique ID
    clean.substring(18)            // Check digit(s)
  ];
}

/**
 * Format an ICCID with alternate spacing for special formats
 */
function formatWithAlternateSpacing(iccid) {
  const clean = iccid.replace(/\s+/g, '');

  // Adjust this based on which alternate format we're using
  return [
    clean.substring(0, 2),         // Industry (89)
    clean.substring(2, 4),         // Country (14/31)
    clean.substring(4, 7),         // Provider
    clean.substring(7, 11),        // First 4 of unique ID
    clean.substring(11, 15),       // Next 4 of unique ID
    clean.substring(15, 19),       // Next 4 of unique ID
    clean.substring(19)            // Check digit
  ];
}

/**
 * Get country name from country code
 */
function getCountryByCode(code) {
  const countryCodes = {
    "01": "US",
    "14": "US", // Special US code used by some carriers
    "31": "US", // Another special US code
    "44": "UK",
    "49": "DE",
    "33": "FR",
    "61": "AU",
    "86": "CN",
    "81": "JP"
  };

  return countryCodes[code];
}
