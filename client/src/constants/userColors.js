// Color palette for user avatars
export const colors = [
  { light: '#e5e7eb', dark: '#334155' }, // Slate
  { light: '#e7e5e4', dark: '#44403c' }, // Stone
  { light: '#ffedd5', dark: '#c2410c' }, // Orange
  { light: '#fef3c7', dark: '#b45309' }, // Amber
  { light: '#fef08a', dark: '#a16207' }, // Yellow
  { light: '#d9f99d', dark: '#4d7c0f' }, // Lime
  { light: '#bbf7d0', dark: '#15803d' }, // Green
  { light: '#d1fae5', dark: '#047857' }, // Emerald
  { light: '#ccfbf1', dark: '#0f766e' }, // Teal
  { light: '#cffafe', dark: '#0e7490' }, // Cyan
  { light: '#e0f2fe', dark: '#0369a1' }, // Sky
  { light: '#dbeafe', dark: '#1d4ed8' }, // Blue
  { light: '#e0e7ff', dark: '#4338ca' }, // Indigo
  { light: '#ede9fe', dark: '#6d28d9' }, // Violet
  { light: '#f3e8ff', dark: '#7e22ce' }, // Purple
  { light: '#fae8ff', dark: '#a21caf' }, // Fuchsia
  { light: '#fce7f3', dark: '#be185d' }, // Pink
  { light: '#ffe4e6', dark: '#be123c' }, // Rose
];

// Color cache to maintain consistent colors per user
const colorCache = new Map();

/**
 * Get color for a user based on their name
 * @param {string} name - Username or identifier
 * @returns {Object} - Object with light and dark color values
 */
export const toColors = (name) => {
  if (!name) {
    return colors[0];
  }

  // Check if the color is already cached
  if (colorCache.has(name)) {
    return colorCache.get(name);
  }

  // Generate a hash from the name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Use the hash to pick a color from the palette
  const result = colors[Math.abs(hash % colors.length)];

  // Cache the color for future use
  colorCache.set(name, result);
  return result;
};

/**
 * Get user initials from name
 * @param {string} name - Username, full name, or email
 * @returns {string} - One or two letter initials
 */
export const getUserInitials = (name) => {
  if (!name) return 'S'; // Default for System

  // Handle email addresses
  if (name.includes('@')) {
    return name.charAt(0).toUpperCase();
  }

  // Split by spaces
  const nameParts = name.trim().split(' ');

  if (nameParts.length === 1) {
    // Single name, return first initial
    return nameParts[0].charAt(0).toUpperCase();
  } else if (nameParts.length === 2) {
    // First and last name, return both initials
    return (nameParts[0].charAt(0) + nameParts[1].charAt(0)).toUpperCase();
  } else {
    // Multiple names, return first and last initials
    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  }
};
