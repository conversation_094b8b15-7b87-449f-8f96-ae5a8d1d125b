import React, { createContext, useState, useContext, useEffect } from 'react';

const FilterContext = createContext();

export const FilterProvider = ({ children }) => {
  const [selfFilter, setSelfFilter] = useState(() => {
    const stored = localStorage.getItem('selfFilter');
    return stored !== null ? JSON.parse(stored) : true;
  });

  useEffect(() => {
    localStorage.setItem('selfFilter', JSON.stringify(selfFilter));
  }, [selfFilter]);

  return (
    <FilterContext.Provider value={{ selfFilter, setSelfFilter }}>
      {children}
    </FilterContext.Provider>
  );
};

export const useFilter = () => useContext(FilterContext);
