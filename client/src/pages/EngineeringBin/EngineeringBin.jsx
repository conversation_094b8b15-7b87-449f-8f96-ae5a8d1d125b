import React from "react";
import {
  MainWrapper,
  DataTable,
  OnlineStatus,
  StatusBadge,
  StageInput,
  StageFilter,
  StageResults,
  Avatar1,
  Diagnostics,
  RedoTests,
  StageMove,
  Dropdown1,
  SerialNumber,
  PageTitle,
  DateTime,
  TestDuration,
  AddDeviceNoteButton,
  LocationChangeButton,
  DeleteDevice,
  SignalStrength,
  DeviceHistoryButton,
} from "@/components";
import { useStore } from "@/store";
import { useDebounce, useFetch } from "@/hooks";

export default function EngineeringBin() {
  const user = useStore((state) => state.user.data);

  const [page, setPage] = React.useState(1);
  const [queryString, setQueryString] = React.useState("");
  const [sortOrder, setSortOrder] = React.useState("");

  const debouncedQueryString = useDebounce(queryString);

  const { data: engineeringBinData, isLoading, refetch } = useFetch({
    endpoint: `/qc/stages/engineering/devices?page=${page}&pageSize=10&sortOrder=${sortOrder}${debouncedQueryString}`,
    refetchInterval: 5000,
    suppressErrors: true,
  });

  const { devices = [], total: totalDevices } = engineeringBinData || {};

  const handlePageChange = ({ page }) => {
    setPage(page);
  };

  const handleFilterChange = (filterData) => {
    setPage(1);
    setQueryString(filterData.queryString);
  };

  const handleSortOrderChange = ({ sortingStack }) => {
    // Convert the sorting stack to a string format that the backend can understand
    const newSortOrder = sortingStack.map(sort => `${sort.field}:${sort.direction}`).join(",");
    setSortOrder(newSortOrder);
  };

  const tableColumns = [
    {
      field: "username",
      title: "User",
      headerClasses: "pl-3",
      contentClasses: "pl-1",
      cellRenderer: ({ row }) => (
        <Avatar1 text={row.username} colors={row.userColors} />
      ),
    },
    {
      field: "location",
      title: "Location",
    },
    {
      field: "serial",
      title: "Device",
      cellRenderer: ({ row }) => <SerialNumber device={row} />,
    },
    {
      title: "Info",
      cellRenderer: ({ row }) => <DeviceHistoryButton device={row} btnOnly={true} onNotationUpdated={refetch} />,
    },
    {
      title: "4G",
      headerClasses: "pl-6 pr-5",
      contentClasses: "pl-4 pr-2",
      cellRenderer: ({ row }) => (
        <SignalStrength
          rssi={row?.modemStatus?.rssi}
          quality={row?.modemStatus?.quality}
          isOnline={row?.online === 'online'}
          lastChecked={row?.modemStatus?.lastChecked}
        />
      )
    },
    {
      field: "online",
      title: (
        <>
          <span className="xl:hidden">On</span>
          <span className="hidden xl:inline">Online</span>
        </>
      ),
      contentClasses: "min-w-2 text-middle xl:pl-2",
      headerClasses: "min-w-2 text-middle",
      cellRenderer: ({ row }) => (
        <OnlineStatus status={row.online} uptime={row.uptime} />
      ),
    },
    {
      title: "Automated checks",
      cellRenderer: ({ row }) => {
        return <StageResults data={row} />;
      },
    },
    {
      field: "status",
      title: "Status",
      cellRenderer: ({ value }) => {
        return <StatusBadge status={value} />;
      },
    },
    {
      field: "enrolledAt",
      title: "QC",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} format="short" />;
      },
      contentClasses: "text-xs",
    },
    {
      field: "createdAt",
      title: "Tested",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} format="short" />;
      },
      contentClasses: "text-xs",
    },
    {
      field: "duration",
      title: (
        <>
          <span className="xl:hidden">Dur</span>
          <span className="hidden xl:inline">Duration</span>
        </>
      ),
      contentClasses: "text-xs",
      cellRenderer: ({ row }) => {
        return row.status === 'pending' || row.status === 'running'
          ? <TestDuration startTime={row.createdAt} />
          : <>{row.duration}</>;
      },
    },
    {
      field: "attempt",
      title: "Run",
      contentClasses: "text-xs",
    },
    {
      title: " ",
      contentClasses: "flex justify-end w-full",
      cellRenderer: ({ row }) => {
        return (
          <div className="flex flex-row gap-2">
            {user?.warehouseQcRecAccess && <Diagnostics device={row} btnOnly />}
            <RedoTests device={row} btnOnly />
            <Dropdown1>
              <AddDeviceNoteButton device={row.serial} onNotationAdded={refetch} />
              <LocationChangeButton device={row.serial} onLocationUpdated={refetch} />
              <div separator />
              <StageMove target="software" device={row} />
              <StageMove target="hardware" device={row} />
              <StageMove target="failure" device={row} />
              <StageMove target="repair" device={row} />
              <StageMove target="engineering" device={row} />
              <div separator />
              <StageMove target="archive" device={row} />
              <DeleteDevice device={row} />
            </Dropdown1>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <MainWrapper>
        <div className="flex justify-between">
          <div className="flex justify-start">
            <PageTitle>Engineering bin</PageTitle>
          </div>
          <div className="flex justify-end gap-2">
          <StageInput stage="engineering" />
            <StageFilter onFilterChange={handleFilterChange} />
          </div>
        </div>
        <DataTable
          columns={tableColumns}
          rows={devices}
          isLoading={isLoading}
          showPagination
          pageSize={10}
          totalRows={totalDevices}
          onPageChange={handlePageChange}
          onSortOrderChange={handleSortOrderChange}
        ></DataTable>
      </MainWrapper>
    </>
  );
}
