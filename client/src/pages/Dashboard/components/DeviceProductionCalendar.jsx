import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useDeviceProductionData, formatDisplayDate } from './DeviceProductionDataProvider';

export const DeviceProductionCalendar = () => {
  const [viewMode, setViewMode] = useState('stacked'); // 'v3', 'v4', 'stacked'
  const [hoveredDay, setHoveredDay] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const tooltipRef = useRef(null);
  const [tooltipActive, setTooltipActive] = useState(false);

  // Get data from context instead of fetching
  const { calendarData, isLoading, startDate, endDate } = useDeviceProductionData();

  // Define our 32-step color gradients
  const greenColors = [
    '#acfa70', '#a1f36d', '#96ec69', '#8be566', '#81dd63', '#77d65f', '#6ecf5c', '#65c859',
    '#5cc155', '#54ba52', '#4eb251', '#4bab52', '#48a453', '#449d53', '#419653', '#3e8f53',
    '#3a8752', '#378051', '#34794f', '#30724d', '#2d6b4b', '#2a6449', '#265c46', '#235543',
    '#1f4e3f', '#1c473b', '#194037', '#153932', '#12312d', '#0f2a28', '#0b2322', '#081c1c'
  ];

  const blueColors = [
    '#7ac5fa', '#76bef3', '#73b8ec', '#6fb1e5', '#6babdd', '#68a4d6', '#649ecf', '#6097c8',
    '#5d91c1', '#598bba', '#5585b2', '#527fab', '#4e79a4', '#4a729d', '#476c96', '#43678f',
    '#3f6187', '#3b5b80', '#385579', '#344f72', '#304a6b', '#2d4464', '#293f5c', '#253955',
    '#22344e', '#1e2e47', '#1a2940', '#172439', '#131e31', '#0f192a', '#0c1423', '#080f1c'
  ];

  const purpleColors = [
    '#ba7afa', '#b076f3', '#a573ec', '#9c6fe5', '#926bdd', '#8968d6', '#8064cf', '#7760c8',
    '#6e5dc1', '#6659ba', '#5e55b2', '#5752ab', '#4f4ea4', '#4a4c9d', '#474c96', '#434b8f',
    '#3f4a87', '#3b4880', '#384779', '#344572', '#30426b', '#2d4064', '#293d5c', '#253a55',
    '#22374e', '#1e3347', '#1a2f40', '#172b39', '#132631', '#0f212a', '#0c1c23', '#08171c'
  ];

  // For legend - 5 steps (gray + 4 evenly spaced colors)
  const getLegendColors = (type) => {
    if (type === 'v3') {
      return ['#ebedf0', blueColors[0], blueColors[5], blueColors[10], blueColors[15]];
    } else if (type === 'v4') {
      return ['#ebedf0', purpleColors[0], purpleColors[5], purpleColors[10], purpleColors[15]];
    } else { // stacked
      return ['#ebedf0', greenColors[0], greenColors[5], greenColors[10], greenColors[15]];
    }
  };

  // Color scales for different device types with 32 steps
  const getColor = (value, type) => {
    // For undefined values, return a light gray that's darker than zero
    if (value === undefined) return '#d1d5db'; // gray-300
    if (value === 0) return '#ebedf0';

    const colors =
      type === 'v3.5' ? blueColors :
      type === 'v4' ? purpleColors :
      greenColors;

    // Map value to a color index (0-31) with a max of 150 devices
    const maxValue = 150;
    const index = Math.min(Math.floor((value / maxValue) * 31), 31);
    return colors[index];
  };

  // Get the color for a specific day based on viewMode
  const getDayColor = (day) => {
    if (!day) return '#d1d5db'; // Use gray-300 for undefined days instead of white

    if (viewMode === 'v3') {
      return getColor(day.v3, 'v3.5');
    } else if (viewMode === 'v4') {
      return getColor(day.v4, 'v4');
    } else {
      return getColor(day.v3 + day.v4, 'stacked');
    }
  };

  // Create tooltip content for a day
  const createTooltipContent = (day) => {
    if (!day) return null;

    // Format the date for display in a more readable format
    const displayDate = formatDisplayDate(day.date);

    return (
      <>
        <div className="pb-1 mb-1 border-b border-stone-400 whitespace-nowrap">
          {displayDate}
        </div>
        {day.v3 > 0 && (
          <p className="flex justify-between items-center p-0.5">
            <span className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full mr-2" style={{ backgroundColor: blueColors[10] }}></span>
              G3.5
            </span>
            <span className="ml-2">{day.v3}</span>
          </p>
        )}
        {day.v4 > 0 && (
          <p className="flex justify-between items-center p-0.5">
            <span className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full mr-2" style={{ backgroundColor: purpleColors[10] }}></span>
              G4
            </span>
            <span className="ml-2">{day.v4}</span>
          </p>
        )}
        {(day.v3 + day.v4) > 0 ? (
          <p className="flex justify-between items-center p-0.5">
            <span className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full mr-2" style={{ backgroundColor: greenColors[10] }}></span>
              Total
            </span>
            <span className="ml-2">{day.v3 + day.v4}</span>
          </p>
        ) : (
          <p className="whitespace-nowrap">No devices</p>
        )}
      </>
    );
  };

  // Calendar dimensions
  const weekCount = 53;
  const dayCount = 7;
  const weekLabelWidth = 30;
  const monthLabelHeight = 20;
  const cellSize = 10; // Base cell size
  const cellMargin = 2;
  const cellTotal = cellSize + cellMargin;

  // Prepare weeks and months data for rendering
  const { weeks, monthLabels } = useMemo(() => {
    // Create week grid with correct date assignments
    const weeks = [];
    const now = new Date();
    const yearAgo = new Date(now);
    yearAgo.setFullYear(yearAgo.getFullYear() - 1);
    yearAgo.setHours(0, 0, 0, 0);

    // Find the start of the week containing the date from a year ago
    const dayOfWeek = yearAgo.getDay();
    yearAgo.setDate(yearAgo.getDate() - dayOfWeek);

    // Prepare month labels
    const monthLabels = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    let lastMonth = -1;
    let weekIndex = 0;

    // Generate 53 weeks of data
    while (weekIndex < weekCount) {
      const week = [];

      // For each day of the week (0-6)
      for (let day = 0; day < dayCount; day++) {
        const date = new Date(yearAgo);
        date.setDate(yearAgo.getDate() + (weekIndex * 7) + day);
        const dateStr = date.toISOString().split('T')[0];

        // Track month changes for labels
        const month = date.getMonth();
        if (month !== lastMonth) {
          lastMonth = month;
          monthLabels.push({
            month: months[month],
            x: weekIndex * cellTotal + weekLabelWidth,
          });
        }

        // Add day data to the week
        // Use data from context or create empty day
        week.push({
          date: dateStr,
          dayData: calendarData[dateStr] || { date: dateStr, v3: 0, v4: 0 }
        });
      }

      weeks.push(week);
      weekIndex++;
    }

    return { weeks, monthLabels };
  }, [calendarData]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="w-full bg-white rounded shadow p-4">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  const daysOfWeek = ['', 'Mon', '', 'Wed', '', 'Fri', ''];

  // Handle cell mouse events - track for tooltip display
  const handleCellMouseEnter = (e, day) => {
    const rect = e.target.getBoundingClientRect();
    const newPosition = {
      x: rect.left + rect.width / 2,
      y: rect.top
    };

    // If the tooltip is not currently active, set its position immediately
    // without animation by temporarily removing the transition
    if (!tooltipActive && tooltipRef.current) {
      tooltipRef.current.style.transition = 'none';
      setTooltipPosition(newPosition);

      // Force a reflow to ensure the transition is disabled before we make it visible
      tooltipRef.current.offsetHeight;

      setHoveredDay(day);
      setTooltipActive(true);

      // Re-enable transitions on the next tick
      setTimeout(() => {
        if (tooltipRef.current) {
          tooltipRef.current.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), left 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), top 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), opacity 0.2s';
        }
      }, 0);
    } else {
      // If already active, just update position with transition
      setTooltipPosition(newPosition);
      setHoveredDay(day);
      setTooltipActive(true);
    }
  };

  const handleCellMouseLeave = () => {
    // We don't hide the tooltip here - just mark it as inactive
    setTooltipActive(false);
  };

  // Handle mouse leaving the entire chart
  const handleChartMouseLeave = () => {
    setTooltipActive(false);
    setHoveredDay(null);
  };

  // Get UI theme colors (around 33% from light side of each gradient)
  const getThemeColor = (type) => {
    if (type === 'v3') return blueColors[10]; // #5585b2 - from blue gradient
    if (type === 'v4') return purpleColors[10]; // #5e55b2 - from purple gradient
    return greenColors[10]; // #54ba52 - from green gradient
  };

  const greenTheme = getThemeColor('stacked');
  const blueTheme = getThemeColor('v3');
  const purpleTheme = getThemeColor('v4');

  return (
    <div className="w-full rounded p-4 relative overflow-hidden bg-stone-100">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-md font-normal text-stone-600">Device production calendar</h2>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-1 text-sm relative rounded-t ${
              viewMode === 'stacked'
                ? `bg-lime-500 text-white`
                : 'text-gray-800 hover:text-lime-500 bg-stone-200'
            }`}
            onClick={() => setViewMode('stacked')}
            style={{
              ...(viewMode !== 'stacked' && {
                paddingBottom: '4px',
                borderBottom: `3px solid ${greenTheme}`
              })
            }}
          >
            All devices
          </button>
          <button
            className={`px-4 py-1 text-sm relative rounded-t ${
              viewMode === 'v3'
                ? `bg-blue-500 text-white`
                : 'text-gray-800 hover:text-blue-500 bg-stone-200'
            }`}
            onClick={() => setViewMode('v3')}
            style={{
              ...(viewMode !== 'v3' && {
                paddingBottom: '4px',
                borderBottom: `3px solid ${blueTheme}`
              })
            }}
          >
            Gen 3.5
          </button>
          <button
            className={`px-4 py-1 text-sm relative rounded-t ${
              viewMode === 'v4'
                ? `bg-purple-500 text-white`
                : 'text-gray-800 hover:text-purple-500 bg-stone-200'
            }`}
            onClick={() => setViewMode('v4')}
            style={{
              ...(viewMode !== 'v4' && {
                paddingBottom: '4px',
                borderBottom: `3px solid ${purpleTheme}`
              })
            }}
          >
            Gen 4
          </button>
        </div>
      </div>

      <div className="relative" onMouseLeave={handleChartMouseLeave}>
        {/* Container with responsive behavior */}
        <div className="overflow-x-auto lg:overflow-x-hidden w-full">
          <div className="min-w-[480px] lg:min-w-0 lg:max-w-4xl lg:mx-auto mt-2">
            {/* Use the viewBox attribute to make SVG responsive */}
            <svg
              className="w-full h-auto"
              viewBox={`0 0 ${weekCount * cellTotal + weekLabelWidth} ${dayCount * cellTotal + monthLabelHeight}`}
              preserveAspectRatio="xMidYMid meet"
            >
              {/* Month labels */}
              {monthLabels.map((label, index) => (
                <text
                  key={`month-${index}`}
                  x={label.x}
                  y={monthLabelHeight - 5}
                  fontSize="10"
                  fill="#666"
                >
                  {label.month}
                </text>
              ))}

              {/* Day of week labels */}
              {daysOfWeek.map((day, index) => (
                <text
                  key={`day-${index}`}
                  x={weekLabelWidth - 5}
                  y={(index * cellTotal) + (cellSize * 0.75) + monthLabelHeight}
                  fontSize="10"
                  fill="#666"
                  textAnchor="end"
                  alignmentBaseline="middle"
                >
                  {day}
                </text>
              ))}

              {/* Calendar cells with expanded hit areas */}
              {weeks.map((week, weekIndex) => (
                <React.Fragment key={`week-${weekIndex}`}>
                  {week.map((day, dayIndex) => {
                    const cellX = (weekIndex * cellTotal) + weekLabelWidth;
                    const cellY = (dayIndex * cellTotal) + monthLabelHeight;

                    return (
                      <g key={`day-${weekIndex}-${dayIndex}`}>
                        {/* Visible colored square */}
                        <rect
                          x={cellX}
                          y={cellY}
                          width={cellSize}
                          height={cellSize}
                          rx={2}
                          fill={getDayColor(day.dayData)}
                          className="transition-colors duration-150"
                        />
                        {/* Invisible hit area that covers the gap */}
                        <rect
                          x={cellX - cellMargin/2}
                          y={cellY - cellMargin/2}
                          width={cellSize + cellMargin}
                          height={cellSize + cellMargin}
                          fill="transparent"
                          className="cursor-default calendar-cell"
                          onMouseEnter={(e) => handleCellMouseEnter(e, day.dayData)}
                          onMouseLeave={handleCellMouseLeave}
                        />
                      </g>
                    );
                  })}
                </React.Fragment>
              ))}
            </svg>
          </div>
        </div>

        {/* Persistent tooltip with smarter positioning */}
        <div
          ref={tooltipRef}
          className="fixed bg-stone-500 text-white rounded-md shadow-md px-3 py-2 text-xs z-50 transform -translate-y-full -translate-x-1/2 pointer-events-none"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y - 10}px`,
            minWidth: '108px',
            // Transition is now handled in the event handlers for more control
            opacity: tooltipActive ? 1 : 0,
            visibility: hoveredDay ? 'visible' : 'hidden'
          }}
        >
          {hoveredDay && createTooltipContent(hoveredDay)}
        </div>
      </div>

      {/* Legend - still using just 5 steps for simplicity */}
      <div className="flex justify-center items-center mt-4 mb-2">
        <span className="text-xs mr-2">Less</span>
        {getLegendColors(viewMode).map((color, i) => (
          <div
            key={i}
            className="w-3 h-3 mx-1 rounded-sm"
            style={{ backgroundColor: color }}
          ></div>
        ))}
        <span className="text-xs ml-2">More</span>
      </div>
    </div>
  );
};
