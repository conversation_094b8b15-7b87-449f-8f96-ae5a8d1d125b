import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>Axis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { useDeviceProductionData } from './DeviceProductionDataProvider';

// Monthly chart processing logic
const useMonthlyChartData = (calendarData) => {
  return useMemo(() => {
    if (!calendarData || Object.keys(calendarData).length === 0) {
      return [];
    }

    // Get current date for filtering
    const currentDate = new Date();
    const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // Filter out current month to ensure we only show complete months
    const sortedDates = Object.keys(calendarData)
      .filter(dateStr => {
        const date = new Date(dateStr);
        const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        return month !== currentMonth;
      })
      .sort();

    // Group by month for readability
    const monthlyData = [];
    let currentMonthGroup = '';
    let monthSum = { v3: 0, v4: 0, total: 0, count: 0 };

    sortedDates.forEach(dateStr => {
      const date = new Date(dateStr);
      const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (currentMonthGroup === '') {
        currentMonthGroup = month;
      }

      if (month !== currentMonthGroup) {
        // Push the aggregated data
        const monthDate = new Date(currentMonthGroup + '-01T12:00:00');
        monthlyData.push({
          month: currentMonthGroup,
          // Include year in display to avoid duplicate month names
          displayMonth: `${monthDate.toLocaleDateString('en-US', { month: 'short' })} '${monthDate.getFullYear().toString().substr(2)}`,
          v3: Math.round(monthSum.v3),
          v4: Math.round(monthSum.v4),
          total: Math.round(monthSum.total)
        });

        // Reset for new month
        currentMonthGroup = month;
        monthSum = { v3: 0, v4: 0, total: 0, count: 0 };
      }

      // Accumulate values
      const dayData = calendarData[dateStr];
      monthSum.v3 += dayData.v3;
      monthSum.v4 += dayData.v4;
      monthSum.total += dayData.v3 + dayData.v4;
      monthSum.count++;
    });

    // Add the last complete month
    if (currentMonthGroup !== '' && sortedDates.length > 0) {
      const monthDate = new Date(currentMonthGroup + '-01T12:00:00');
      monthlyData.push({
        month: currentMonthGroup,
        // Include year in display to avoid duplicate month names
        displayMonth: `${monthDate.toLocaleDateString('en-US', { month: 'short' })} '${monthDate.getFullYear().toString().substr(2)}`,
        v3: Math.round(monthSum.v3),
        v4: Math.round(monthSum.v4),
        total: Math.round(monthSum.total)
      });
    }

    return monthlyData;
  }, [calendarData]);
};

// Weekly chart processing logic
const useWeeklyChartData = (calendarData) => {
  return useMemo(() => {
    if (!calendarData || Object.keys(calendarData).length === 0) {
      return [];
    }

    // Get current date for filtering
    const currentDate = new Date();
    const currentDateStr = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD

    // Get dates for the last 4 weeks
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(currentDate.getDate() - 28);

    // Filter dates: last 4 weeks only, exclude current day
    // CHANGE: Removed the weekday filtering to show all days
    const filteredDates = Object.keys(calendarData)
      .filter(dateStr => {
        const date = new Date(dateStr + "T12:00:00Z"); // Add noon UTC time to stabilize timezone
        return date >= fourWeeksAgo && dateStr !== currentDateStr;
      })
      .sort();

    // Map all dates to data points
    return filteredDates.map(dateStr => {
      // Create a date object with explicit UTC time for consistency
      const date = new Date(dateStr + "T12:00:00Z");
      const dayData = calendarData[dateStr];

      // Get the day of week using UTC methods to avoid timezone issues
      const weekday = new Intl.DateTimeFormat('en-US', {
        weekday: 'short',
        timeZone: 'UTC' // Force UTC timezone for consistent weekday names
      }).format(date);

      // Add a debugging value to help understand what's happening
      const utcDay = date.getUTCDay(); // 0=Sun, 1=Mon, ..., 6=Sat
      const localDay = date.getDay();

      return {
        date: dateStr,
        // Use weekday name formatted in UTC
        displayDate: weekday,
        // Add the numeric day value for debugging
        displayDayNum: `${weekday} (${utcDay})`,
        // Keep the full date info for tooltip
        fullDisplayDate: new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: 'numeric',
          timeZone: 'UTC' // Force UTC timezone for consistency
        }).format(date),
        v3: dayData?.v3 || 0,
        v4: dayData?.v4 || 0,
        total: (dayData?.v3 || 0) + (dayData?.v4 || 0)
      };
    });
  }, [calendarData]);
};

// Weekly Chart Component with displayDayNum showing for debugging
const WeeklyProductionChart = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="displayDate" // Changed from displayDayNum to displayDate for normal use
          tick={{ fontSize: 11 }}
        />
        <YAxis tick={{ fontSize: 11 }} />
        <Tooltip content={<WeeklyTooltip />} />
        <Legend />
        <Line
          type="monotone"
          dataKey="v3"
          name="Gen 3.5"
          stroke="#6ecf5c"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
        <Line
          type="monotone"
          dataKey="v4"
          name="Gen 4"
          stroke="#6659ba"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
        <Line
          type="monotone"
          dataKey="total"
          name="Total"
          stroke="#77d65f"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Monthly tooltip component
const MonthlyTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    // Get the full date with year for the tooltip
    let displayDate = label;

    if (payload[0] && payload[0].payload && payload[0].payload.month) {
      // Format as "Month YYYY" for monthly view (no comma)
      const [year, month] = payload[0].payload.month.split('-');
      const monthDate = new Date(parseInt(year), parseInt(month) - 1, 1);
      displayDate = monthDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    }

    return (
      <div className="bg-stone-500 text-white text-xs p-3 rounded-md shadow-md">
        <p className="border-b border-stone-400 pb-2 mb-2">{displayDate}</p>
        {payload.map((entry, index) => {
          // Map dataKey to proper display name
          let displayName;
          if (entry.dataKey === 'v3') {
            displayName = 'G3.5';
          } else if (entry.dataKey === 'v4') {
            displayName = 'G4';
          } else {
            displayName = 'Total';
          }

          return (
            <p key={`tooltip-${index}`} className="flex justify-between items-center mb-1">
              <span className="flex items-center">
                <span
                  className="inline-block w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: entry.color }}
                ></span>
                {displayName}
              </span>
              <span className="ml-3">{entry.value}</span>
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

// Weekly tooltip component
const WeeklyTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    // Get the full date with year for the tooltip
    let displayDate = label;

    if (payload[0] && payload[0].payload && payload[0].payload.date) {
      // Format as "Month Day, YYYY" for weekly view
      const date = new Date(payload[0].payload.date + 'T12:00:00');
      displayDate = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }

    return (
      <div className="bg-stone-500 text-white text-xs p-3 rounded-md shadow-md">
        <p className="border-b border-stone-400 pb-2 mb-2">{displayDate}</p>
        {payload.map((entry, index) => {
          // Map dataKey to proper display name
          let displayName;
          if (entry.dataKey === 'v3') {
            displayName = 'G3.5';
          } else if (entry.dataKey === 'v4') {
            displayName = 'G4';
          } else {
            displayName = 'Total';
          }

          return (
            <p key={`tooltip-${index}`} className="flex justify-between items-center mb-1">
              <span className="flex items-center">
                <span
                  className="inline-block w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: entry.color }}
                ></span>
                {displayName}
              </span>
              <span className="ml-3">{entry.value}</span>
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

// Monthly Chart Component
const MonthlyProductionChart = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="displayMonth"
          tick={{ fontSize: 14 }}
        />
        <YAxis tick={{ fontSize: 14 }} />
        <Tooltip content={<MonthlyTooltip />} />
        <Legend />
        <Line
          type="monotone"
          dataKey="v3"
          name="Gen 3.5"
          stroke="#4d9cd3"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
        <Line
          type="monotone"
          dataKey="v4"
          name="Gen 4"
          stroke="#6659ba"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
        <Line
          type="monotone"
          dataKey="total"
          name="Total"
          stroke="#77d65f"
          activeDot={{ r: 8 }}
          strokeWidth={2}
          animationDuration={250}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};


// Main Component
export const DeviceProductionLineChart = () => {
  const { calendarData, isLoading } = useDeviceProductionData();
  const [viewMode, setViewMode] = useState('monthly'); // 'monthly' or 'weekly'

  // Get the data based on the view mode
  const monthlyData = useMonthlyChartData(calendarData);
  const weeklyData = useWeeklyChartData(calendarData);

  if (isLoading) {
    return (
      <div className="w-full bg-white rounded shadow p-4">
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full rounded p-4 relative overflow-hidden bg-stone-100">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-md font-normal text-stone-600">
          {viewMode === 'monthly' ? 'Monthly device production' : 'Device production (last 4 weeks, weekdays)'}
        </h2>

        {/* View selection toggle */}
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('monthly')}
            className={`px-3 py-1 text-sm rounded ${viewMode === 'monthly'
              ? 'bg-stone-600 text-white'
              : 'bg-stone-200 text-stone-600'}`}
          >
            Monthly
          </button>
          <button
            onClick={() => setViewMode('weekly')}
            className={`px-3 py-1 text-sm rounded ${viewMode === 'weekly'
              ? 'bg-stone-600 text-white'
              : 'bg-stone-200 text-stone-600'}`}
          >
            Weekly
          </button>
        </div>
      </div>

      <div className="h-72">
        {viewMode === 'monthly' ? (
          <MonthlyProductionChart data={monthlyData} />
        ) : (
          <WeeklyProductionChart data={weeklyData} />
        )}
      </div>
    </div>
  );
};
