import React, { createContext, useContext, useState, useEffect } from 'react';
import { useFetch } from '@/hooks';

// Create context for device production data
const DeviceProductionContext = createContext();

// Calculate date range (1 year back from today)
const getDateRange = () => {
  const endDate = new Date();
  endDate.setHours(23, 59, 59, 999);
  const endDateStr = formatISODate(endDate);

  const startDate = new Date(endDate);
  startDate.setFullYear(startDate.getFullYear() - 1);
  startDate.setHours(0, 0, 0, 0);
  const startDateStr = formatISODate(startDate);

  return { startDate, startDateStr, endDate, endDateStr };
};

// Helper to create combined data structure
const createCombinedData = (gen3Data, gen4Data, startDate, endDate) => {
  const combinedData = {};

  // Process all dates in the range
  const currentDate = new Date(startDate);
  const endDateObj = new Date(endDate);

  while (currentDate <= endDateObj) {
    const dateStr = formatISODate(currentDate);

    // Get counts from API data or default to 0
    const gen3Count = gen3Data?.dailyCounts?.[dateStr]?.count || 0;
    const gen4Count = gen4Data?.dailyCounts?.[dateStr]?.count || 0;

    combinedData[dateStr] = {
      date: new Date(currentDate),
      v3: gen3Count,
      v4: gen4Count
    };

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return combinedData;
};

// Provider component that will fetch and provide data to all visualizations
export const DeviceProductionDataProvider = ({ children }) => {
  const [calendarData, setCalendarData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const { startDate, startDateStr, endDate, endDateStr } = getDateRange();

  // Fetch data for all devices
  const { data: allDevicesData, isLoading: isLoadingAll } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}`,
  });

  // Fetch data for Gen 3.5 devices
  const { data: gen3Data, isLoading: isLoadingGen3 } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}&gen=3`,
  });

  // Fetch data for Gen 4 devices
  const { data: gen4Data, isLoading: isLoadingGen4 } = useFetch({
    endpoint: `/qc/devices/shipped?startDate=${startDateStr}&endDate=${endDateStr}&gen=4`,
  });

  // Process data when all API calls complete
  useEffect(() => {
    if (isLoadingAll || isLoadingGen3 || isLoadingGen4) {
      setIsLoading(true);
      return;
    }

    if (allDevicesData && gen3Data && gen4Data) {
      // Combine data from different API responses
      const combinedData = createCombinedData(gen3Data, gen4Data, startDate, endDate);
      setCalendarData(combinedData);
      setIsLoading(false);
    }
  }, [allDevicesData, gen3Data, gen4Data, isLoadingAll, isLoadingGen3, isLoadingGen4]);

  // Value object to provide through the context
  const value = {
    calendarData,
    isLoading,
    startDate,
    endDate,
    rawData: {
      allDevicesData,
      gen3Data,
      gen4Data
    }
  };

  return (
    <DeviceProductionContext.Provider value={value}>
      {children}
    </DeviceProductionContext.Provider>
  );
};

// Custom hook to use the device production data
export const useDeviceProductionData = () => {
  const context = useContext(DeviceProductionContext);

  if (context === undefined) {
    throw new Error('useDeviceProductionData must be used within a DeviceProductionDataProvider');
  }

  return context;
};

// Helper function to format date as YYYY-MM-DD for API lookups
export function formatISODate(date) {
  return date.toISOString().split('T')[0];
}

// Helper function to format date as "Month Day, Year" for display
export function formatDisplayDate(date) {
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(date).toLocaleDateString('en-US', options);
}
