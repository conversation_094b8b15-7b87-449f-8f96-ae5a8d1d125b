import React from "react";
import {
  CustomModal,
  InputField,
  FriendlySerialNumber,
} from "@/components";
import { useMutate } from "@/hooks";
import { INPUT_FIELD_TYPES } from "@/constants";

const INITIAL_INPUT_FIELD_VALUES = {
  powerLight: false,
  ata2Light: false,
  ata1Light: false,
  networkLight: false,
  batteryPower: false,
  batteryPercent: false,
  powerDisconnect: false,
  chasis: false,
  mountingBrackets: false,
  phonePort: false,
};

function PhysicalHardwareChecklist({
  isOpen,
  serial,
  existingChecklist,
  handleSubmit,
  handleClose,
}) {
  const [inputFieldValues, setInputFieldValues] = React.useState(
    existingChecklist || INITIAL_INPUT_FIELD_VALUES
  );

  const { mutateAsync } = useMutate();

  const handleInputChange = (event) => {
    const {
      target: { name, checked },
    } = event;

    setInputFieldValues((prevState) => ({ ...prevState, [name]: checked }));
  };

  const submitHardwareChecklist = async () => {
    const apiResponse = await mutateAsync({
      endpoint: `/qc/devices/${serial}/inspections`,
      method: "POST",
      body: inputFieldValues,
      successMsg: "Pysical hardware checklist submitted.",
    });

    if (apiResponse?.passed) {
      await mutateAsync({
        endpoint: `/qc/stages/shipping/devices`,
        method: "PUT",
        body: { serial },
        successMsg: `Device moved to shipping stage.`,
      });
    }

    handleSubmit();
  };

  return (
    <CustomModal
      isOpen={isOpen}
      title={
        <div className="relative mt-[-10px]">
          <div className="text-[20px]">Physical hardware checklist</div>
          <div className="absolute text-sm font-normal text-black">
            <FriendlySerialNumber serial={serial} />
          </div>
        </div>
      }
      onPrimaryButtonClick={submitHardwareChecklist}
      onSecondaryButtonClick={handleClose}
    >
      <h5 className="text-sm font-bold mt-[-10px] ml-[-3px] mb-2 text-gray-600">Lights</h5>
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Power light"
        name="powerLight"
        value={inputFieldValues.powerLight}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="ATA 1 (power, Internet, network)"
        name="ata1Light"
        value={inputFieldValues.ata1Light}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="ATA 2 (power, Internet)"
        name="ata2Light"
        value={inputFieldValues.ata2Light}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Eth0/1 activity lights"
        name="networkLight"
        value={inputFieldValues.networkLight}
        onChange={handleInputChange}
      />
      <h5 className="text-sm font-bold mt-5 ml-[-3px] mb-2 text-gray-600">Battery</h5>
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Battery power"
        name="batteryPower"
        value={inputFieldValues.batteryPower}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Battery percentage"
        name="batteryPercent"
        value={inputFieldValues.batteryPercent}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Power disconnect"
        name="powerDisconnect"
        value={inputFieldValues.powerDisconnect}
        onChange={handleInputChange}
      />
      <h5 className="text-sm font-bold mt-5 ml-[-3px] mb-2 text-gray-600">Case and connections</h5>
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Case: No bends, dents, cracks, breaks"
        name="chasis"
        value={inputFieldValues.chasis}
        onChange={handleInputChange}
      />
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="Mounting brackets: No bends, dents, cracks, breaks"
        name="mountingBrackets"
        value={inputFieldValues.mountingBrackets}
        onChange={handleInputChange}
      />
      <h5 className="text-sm font-bold mt-5 ml-[-3px] mb-2 text-gray-600">Port test</h5>
      <InputField
        type={INPUT_FIELD_TYPES.CHECKBOX}
        label="RJ-11"
        name="phonePort"
        value={inputFieldValues.phonePort}
        onChange={handleInputChange}
      />
    </CustomModal>
  );
}

export default PhysicalHardwareChecklist;
