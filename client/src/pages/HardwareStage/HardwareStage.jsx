import React from "react";
import {
  MainWrapper,
  DataTable,
  OnlineStatus,
  StatusBadge,
  Button1,
  StageFilter,
  Avatar1,
  StageMove,
  DeleteDevice,
  Dropdown1,
  SerialNumber,
  PageTitle,
  DateTime,
  AddDeviceNoteButton,
  LocationChangeButton,
  SignalStrength,
  DeviceHistoryButton,
} from "@/components";
import PhysicalHardwareChecklist from "./PhysicalHardwareChecklist";
import { useDebounce, useFetch } from "@/hooks";

export default function HardwareStage() {
  const [page, setPage] = React.useState(1);
  const [serial, setSerial] = React.useState();
  const [hardwareChecklist, setHardwareChecklist] = React.useState();
  const [isPhysicalChecksModalOpen, setIsPhysicalChecksModalOpen] =
    React.useState(false);
  const [queryString, setQueryString] = React.useState("");
  const [sortOrder, setSortOrder] = React.useState("");

  const debouncedQueryString = useDebounce(queryString);

  const { data: hardwareStageData, isLoading, refetch } = useFetch({
    endpoint: `/qc/stages/hardware/devices?page=${page}&pageSize=10&sortOrder=${sortOrder}${debouncedQueryString}`,
    refetchInterval: 5000,
    suppressErrors: true,
  });

  const { devices = [], total: totalDevices } = hardwareStageData || {};

  const handleLaunch = (serial) => {
    setSerial(serial);

    const device = devices.find((d) => d.serial === serial);
    const { inspection } = device;

    setHardwareChecklist(inspection?.checks);
    setIsPhysicalChecksModalOpen(true);
  };

  const columns = [
    {
      field: "username",
      title: "User",
      headerClasses: "pl-3",
      contentClasses: "pl-1",
      cellRenderer: ({ row }) => (
        <Avatar1 text={row.username} colors={row.userColors} />
      ),
    },
    {
      title: "Location",
      field: "location",
    },
    {
      field: "serial",
      title: "Device",
      cellRenderer: ({ row }) => <SerialNumber device={row} />,
    },
    {
      title: "Info",
      cellRenderer: ({ row }) => <DeviceHistoryButton device={row} btnOnly={true} onNotationUpdated={refetch} />,
    },
    {
      title: "4G",
      headerClasses: "pl-6 pr-5",
      contentClasses: "pl-4 pr-2",
      cellRenderer: ({ row }) => (
        <SignalStrength
          rssi={row?.modemStatus?.rssi}
          quality={row?.modemStatus?.quality}
          isOnline={row?.online === 'online'}
          lastChecked={row?.modemStatus?.lastChecked}
        />
      )
    },
    {
      field: "online",
      title: (
        <>
          <span className="xl:hidden">On</span>
          <span className="hidden xl:inline">Online</span>
        </>
      ),
      contentClasses: "min-w-2 text-middle xl:pl-2",
      headerClasses: "min-w-2 text-middle",
      cellRenderer: ({ row }) => (
        <OnlineStatus status={row.online} uptime={row.uptime} />
      ),
    },
    {
      title: "Physical checks",
      field: "physicalChecks",
      cellRenderer: ({ row }) => (
        <Button1
          isActive
          size="xs"
          classes="h-5 w-17 rounded-full text-xs font-semibold"
          onClick={() => handleLaunch(row.serial)}
        >
          Launch&hellip;
        </Button1>
      ),
    },
    {
      title: "Status",
      //field: "status",
      cellRenderer: ({ row }) => {
        const { passed } = row.inspection || {};

        if (passed === undefined) {
          return <StatusBadge status="pending" />;
        }

        if (passed === true) {
          return <StatusBadge status="passed" />;
        }

        return <StatusBadge status="failed" />;
      },
    },
    {
      field: "enrolledAt",
      title: "QC",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} />;
      },
      contentClasses: "text-xs",
    },
    {
      title: "HW",
      field: "createdAt",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} />;
      },
      contentClasses: "text-xs",
    },
    /*
    {
      title: (
        <>
          <span className="xl:hidden">Dur</span>
          <span className="hidden xl:inline">Duration</span>
        </>
      ),
      field: "duration",
      contentClasses: "text-xs",
    },
    {
      title: "Run",
      field: "attempt",
      contentClasses: "text-xs",
    },
    */
    {
      title: " ",
      contentClasses: "flex justify-end w-full",
      cellRenderer: ({ row }) => {
        return (
          <div className="flex">
            <StageMove target="repair" device={row} btnOnly />
            <Dropdown1>
              <AddDeviceNoteButton device={row.serial} onNotationAdded={refetch} />
              <LocationChangeButton device={row.serial} onLocationUpdated={refetch} />
              <div separator />
              <StageMove target="software" device={row} refetch={refetch} />
              <StageMove target="failure" device={row} refetch={refetch} />
              <StageMove target="repair" device={row} refetch={refetch} />
              <StageMove target="engineering" device={row} refetch={refetch} />
              <div separator />
              <DeleteDevice device={row} />
            </Dropdown1>
          </div>
        );
      },
    },
    {
      field: "error",
      contentClasses: "text-red-500]",
      spanRow: true,
    },
  ];

  const handlePageChange = ({ page }) => {
    setPage(page);
  };

  const closePhysicalHardwareChecklist = () => {
    setSerial();
    setHardwareChecklist();
    setIsPhysicalChecksModalOpen(false);
  };

  const handleFilterChange = (filterData) => {
    setPage(1);
    setQueryString(filterData.queryString);
  };

  const handleSortOrderChange = ({ sortingStack }) => {
    // Convert the sorting stack to a string format that the backend can understand
    const newSortOrder = sortingStack.map(sort => `${sort.field}:${sort.direction}`).join(",");
    setSortOrder(newSortOrder);
  };

  return (
    <>
      <MainWrapper>
        <div className="flex justify-between">
          <div className="flex justify-start">
            <PageTitle>
              Stage 2
              <b className="h-10 pl-3 mr-3 border-r border-stone-300" />
              <span className="font-thin">Hardware</span>
            </PageTitle>
          </div>
          <div className="flex justify-end gap-2">
            <StageFilter onFilterChange={handleFilterChange} />
          </div>
        </div>
        <DataTable
          columns={columns}
          rows={devices}
          isLoading={isLoading}
          showPagination
          pageSize={10}
          totalRows={totalDevices}
          onPageChange={handlePageChange}
          onSortOrderChange={handleSortOrderChange}
        ></DataTable>
      </MainWrapper>
      {isPhysicalChecksModalOpen && (
        <PhysicalHardwareChecklist
          isOpen={isPhysicalChecksModalOpen}
          serial={serial}
          existingChecklist={hardwareChecklist}
          handleSubmit={closePhysicalHardwareChecklist}
          handleClose={closePhysicalHardwareChecklist}
        />
      )}
    </>
  );
}
