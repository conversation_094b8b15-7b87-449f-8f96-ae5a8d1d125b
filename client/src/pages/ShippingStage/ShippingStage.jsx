import React from "react";
import {
  <PERSON>Wrap<PERSON>,
  DataTable,
  StageFilter,
  Avatar1,
  StageMove,
  Dropdown1,
  SerialNumber,
  PageTitle,
  DateTime,
  AddDeviceNoteButton,
  LocationChangeButton,
  DeleteDevice,
  Duration,
  DeviceHistoryButton,
} from "@/components";
import { formatTableDate } from '@/components/dates';
import { useDebounce, useFetch } from "@/hooks";

export default function ShippingStage() {
  const [page, setPage] = React.useState(1);
  const [queryString, setQueryString] = React.useState("");
  const [sortOrder, setSortOrder] = React.useState("");

  const debouncedQueryString = useDebounce(queryString);

  const { data: shippingStageData, isLoading, refetch } = useFetch({
    endpoint: `/qc/stages/shipping/devices?page=${page}&pageSize=10&sortOrder=${sortOrder}${debouncedQueryString}`,
    refetchInterval: 5000,
    suppressErrors: true,
  });

  const { devices = [], total: totalDevices } = shippingStageData || {};

  const handlePageChange = ({ page }) => {
    setPage(page);
  };

  const handleFilterChange = (filterData) => {
    setPage(1);
    setQueryString(filterData.queryString);
  };

  const handleSortOrderChange = ({ sortingStack }) => {
    // Convert the sorting stack to a string format that the backend can understand
    const newSortOrder = sortingStack.map(sort => `${sort.field}:${sort.direction}`).join(",");
    setSortOrder(newSortOrder);
  };

  const columns = [
    {
      field: "username",
      title: "User",
      headerClasses: "pl-3",
      contentClasses: "pl-1",
      cellRenderer: ({ row }) => (
        <Avatar1 text={row.username} colors={row.userColors} />
      ),
    },
    {
      field: "location",
      title: "Location",
    },
    {
      field: "serial",
      title: "Device",
      cellRenderer: ({ row }) => <SerialNumber device={row} />,
    },
    {
      title: "Info",
      cellRenderer: ({ row }) => <DeviceHistoryButton device={row} btnOnly={true} onNotationUpdated={refetch} />,
    },
    {
      field: "enrolledAt",
      title: "Started",
      cellRenderer: ({ value }) => {
        return <span className="whitespace-nowrap">{formatTableDate(value)}</span>;
      },
      contentClasses: "text-xs",
    },
    {
      field: "stage1Pass",
      title: "Stage 1 Pass",
      contentClasses: "text-xs",
      cellRenderer: ({ value }) => {
        return <span className="whitespace-nowrap">{formatTableDate(value)}</span>;
      },
    },
    {
      field: "stage2Pass",
      title: "Stage 2 Pass",
      contentClasses: "text-xs",
      cellRenderer: ({ value }) => {
        return <span className="whitespace-nowrap">{formatTableDate(value)}</span>;
      },
    },
    {
      field: "enrolledSeconds",
      title: "Duration",
      cellRenderer: ({ row }) => {
        return (
          <Duration
            seconds={row.enrolledSeconds}
            format="natural"
            length="long"
          />
        );
      },
      contentClasses: "text-xs",
    },
    {
      title: " ",
      contentClasses: "flex justify-end w-full",
      cellRenderer: ({ row }) => {
        return (
          <Dropdown1>
            <AddDeviceNoteButton device={row.serial} onNotationAdded={refetch} />
            <LocationChangeButton device={row.serial} onLocationUpdated={refetch} />
            <div separator />
            <StageMove target="software" device={row} refetch={refetch} />
            <StageMove from="shipping" target="hardware" device={row} refetch={refetch} />
            <StageMove target="failure" device={row} refetch={refetch} />
            <StageMove target="repair" device={row} refetch={refetch} />
            <StageMove target="engineering" device={row} refetch={refetch} />
            <div separator />
            <StageMove target="archive" device={row} />
            <DeleteDevice device={row} refetch={refetch} />
          </Dropdown1>
        );
      },
    },
  ];

  return (
    <>
      <MainWrapper>
        <div className="flex justify-between">
          <div className="flex justify-start">
            <PageTitle>
              Stage 3
              <b className="h-10 pl-3 mr-3 border-r border-stone-300" />
              <span className="font-thin">Shipping</span>
            </PageTitle>
          </div>
          <div className="flex justify-end gap-2">
            <StageFilter onFilterChange={handleFilterChange} />
          </div>
        </div>
        <DataTable
          columns={columns}
          rows={devices}
          isLoading={isLoading}
          showPagination
          pageSize={10}
          totalRows={totalDevices}
          onPageChange={handlePageChange}
          onSortOrderChange={handleSortOrderChange}
        ></DataTable>
      </MainWrapper>
    </>
  );
}
