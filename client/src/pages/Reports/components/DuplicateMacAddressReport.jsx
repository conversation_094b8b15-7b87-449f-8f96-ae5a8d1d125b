// Import useState at the top if not already imported
import React, { useState } from 'react';
import { Alert, Table, Button, Dropdown } from 'flowbite-react';
import { useFetch } from '@/hooks';
import { FriendlySerialNumber, Tooltip1 } from '@/components';

const formatMacAddress = (mac) => {
  // Remove any existing colons, spaces, or other separators
  const cleanMac = mac.replace(/[^a-fA-F0-9]/g, '');

  // Check if it's a valid MAC address (12 hex digits)
  if (cleanMac.length !== 12) {
    return mac;
  }

  // Format with colons
  const formattedAddress = cleanMac.match(/.{1,2}/g).join(':').toLowerCase();
  return formattedAddress;
};

// Add to the existing function component
export function DuplicateMacAddressReport() {
  const [viewMode, setViewMode] = useState('byEpi'); // 'byEpi' or 'byDevice'
  const [selectedCompany, setSelectedCompany] = useState('all'); // 'all' or company ID

  // Fetch duplicate MAC data using the cached endpoint
  const {
    data: macData,
    isLoading: isLoadingMacData,
    isError: isMacError,
    refetch: refetchMacData
  } = useFetch({
    endpoint: '/qc/reports/duplicate-macs/epis',
  });

  // Fetch device-oriented data using the cached endpoint
  const {
    data: deviceData,
    isLoading: isLoadingDeviceData,
    isError: isDeviceError,
    refetch: refetchDeviceData
  } = useFetch({
    endpoint: '/qc/reports/duplicate-macs/devices',
  });

  // Handle data refresh
  const handleRefresh = () => {
    refetchMacData();
    refetchDeviceData();
  };

  // Loading state
  if (isLoadingMacData || isLoadingDeviceData) {
    return <div>Loading duplicate MAC address data...</div>;
  }

  // Error state
  if (isMacError || isDeviceError) {
    return <div>Error loading duplicate MAC address data. Please try again.</div>;
  }

  // Render MAC-oriented view
  const renderEpiView = () => {
    if (!macData?.data?.length) {
      return <div>No duplicate MAC addresses found.</div>;
    }

    return (
      <Table className="m-1">
        <Table.Head>
          <Table.HeadCell className="bg-stone-100">MAC address</Table.HeadCell>
          <Table.HeadCell className="bg-stone-100">Duplicate count</Table.HeadCell>
          <Table.HeadCell className="bg-stone-100">Device serial numbers</Table.HeadCell>
        </Table.Head>
        <Table.Body className="divide-y">
          {macData.data.map((item) => (
            <Table.Row key={item.macAddress} className="bg-white">
              <Table.Cell className="whitespace-nowrap font-medium text-gray-900 align-top py-2">
                {formatMacAddress(item.macAddress)}
              </Table.Cell>
              <Table.Cell className="align-top py-2">{item.count}</Table.Cell>
              <Table.Cell className="align-top py-2">
                {item.boxes.map((serialNumber, index) => (
                  <React.Fragment key={index}>
                    {serialNumber}
                    {index < item.boxes.length - 1 && ', '}
                  </React.Fragment>
                ))}
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    );
  };

  const companyMap = new Map();

   // Extract unique companies from the device data
   const getUniqueCompanies = () => {
    if (!deviceData?.data?.length) return [];

    const companies = new Set();

    deviceData.data.forEach(device => {
      if (device.companyId && device.companyName) {
        companyMap.set(device.companyId, device.companyName);
      }
    });

    return Array.from(companyMap.entries()).map(([id, name]) => ({
      id,
      name
    }));
  };

  const uniqueCompanies = getUniqueCompanies();

  const getCompanyCounts = () => {
    if (!deviceData?.data?.length) return new Map();

    // Count devices with duplicate MACs by company
    const counts = new Map();
    deviceData.data.forEach(device => {
      if (device.companyId) {
        counts.set(device.companyId, (counts.get(device.companyId) || 0) + 1);
      }
    });

    return counts;
  };

  const companyCounts = getCompanyCounts();
  const totalCount = deviceData?.data?.length || 0;

  // Filter devices by selected company
  const filteredDevices = selectedCompany === 'all'
    ? deviceData?.data || []
    : (deviceData?.data || []).filter(device => device.companyId === selectedCompany);

  // Render Device-oriented view with company filter
  const renderDeviceView = () => {
    if (!deviceData?.data?.length) {
      return <div>No devices with duplicate MAC addresses found.</div>;
    }

    return (
      <>
        <div className="flex ml-4 mb-2 px-1">
          <Dropdown
            label={selectedCompany === 'all'
              ? `All companies (${totalCount})`
              : `${companyMap.get(selectedCompany) || 'Select Company'} (${companyCounts.get(selectedCompany) || 0})`
            }
          >
            <Dropdown.Item onClick={() => setSelectedCompany('all')}>
              All companies ({totalCount})
            </Dropdown.Item>
            <Dropdown.Divider />
            {uniqueCompanies.map(company => (
              <Dropdown.Item
                key={company.id}
                onClick={() => setSelectedCompany(company.id)}
              >
                {company.name} ({companyCounts.get(company.id) || 0})
              </Dropdown.Item>
            ))}
          </Dropdown>
        </div>

        <Table className="m-1">
          <Table.Head>
            <Table.HeadCell className="bg-stone-100">Device serial number</Table.HeadCell>
            <Table.HeadCell className="bg-stone-100">Company</Table.HeadCell>
            <Table.HeadCell className="bg-stone-100">Number of duplicate MACs</Table.HeadCell>
            <Table.HeadCell className="bg-stone-100">Details</Table.HeadCell>
          </Table.Head>
          <Table.Body className="divide-y">
            {filteredDevices.map((device) => (
              <Table.Row key={device.serialNumber} className="bg-white">
                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 align-top py-2">
                  <FriendlySerialNumber serial={device.serialNumber} />
                </Table.Cell>
                <Table.Cell className="align-top py-2">{device.companyName}</Table.Cell>
                <Table.Cell className="align-top py-2">{device.duplicateMacs.length}</Table.Cell>
                <Table.Cell className="align-top py-2">
                  {device.duplicateMacs.map((mac) => (
                    <div key={mac.macAddress} className="mb-2">
                      <strong className="text-xs">MAC:</strong> {formatMacAddress(mac.macAddress)}<br />
                      <strong className="text-xs">Shared with:</strong>{' '}
                      <span className="inline-flex flex-wrap gap-1">
                        {mac.sharedWith.map((shared, index) => (
                          <span key={index} className="inline-flex items-center">
                            <Tooltip1 content={shared.companyName}>
                              <span className="cursor-help border-b border-dotted border-gray-400">
                                {shared.serialNumber}
                              </span>
                            </Tooltip1>
                            {index < mac.sharedWith.length - 1 && <span>,&nbsp;</span>}
                          </span>
                        ))}
                      </span>
                    </div>
                  ))}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </>
    );
  };

  // Update the Alert component to show filtered count
  return (
    <div className="w-full bg-stone-100 rounded-lg">
      <div className="flex justify-between items-center m-1">
        <div className="text-sm p-3">
          Duplicate EPI MAC addresses
        </div>
        <div className="flex space-x-2">
          <Button
            className={`p-0 text-sm relative rounded-t-md rounded-b-none ${
              viewMode === 'byEpi'
                ? 'bg-[#0068B7] text-white border border-[#0068B7]'
                : 'bg-gray-50 text-gray-700 border border-gray-300'
            }`}
            onClick={() => setViewMode('byEpi')}
          >
            By EPI
          </Button>
          <Button
            className={`p-0 text-sm relative rounded-t-md rounded-b-none ${
              viewMode === 'byDevice'
                ? 'bg-[#0068B7] text-white border border-[#0068B7]'
                : 'bg-gray-50 text-gray-700 border border-gray-300'
            }`}
            onClick={() => setViewMode('byDevice')}
          >
            By device
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        {viewMode === 'byEpi' ? renderEpiView() : renderDeviceView()}
      </div>
    </div>
  );
}
