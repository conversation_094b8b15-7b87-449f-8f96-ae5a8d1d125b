name: 'Setup Go Protobuf'
description: 'Sets up protoc compiler and Go-specific plugins'

runs:
  using: "composite"
  steps:
    - uses: ./.github/actions/setup-protobuf
    - name: Install Go protoc plugins
      shell: bash
      run: |
        go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
        go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
        echo "${HOME}/go/bin" >> $GITHUB_PATH
