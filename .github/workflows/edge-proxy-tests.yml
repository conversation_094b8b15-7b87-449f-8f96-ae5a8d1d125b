on:
  push:
    branches: [ main ]
    paths:
      - 'edge-proxy/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'edge-proxy/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup-go
        with:
          go-version: '1.22.x'

      - uses: ./.github/actions/setup-go-protobuf

      - name: Generate protobuf code
        working-directory: edge-proxy
        run: |
          # Adjust this path based on your actual proto file location
          find . -name "*.proto" -exec protoc --go_out=. --go_opt=paths=source_relative \
            --go-grpc_out=. --go-grpc_opt=paths=source_relative {} \;

      - name: Run Tests
        working-directory: edge-proxy
        run: go test ./...
