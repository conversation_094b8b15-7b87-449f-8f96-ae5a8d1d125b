name: deploy-noc-wizard

on:
  push:
    branches:
      - main
    paths:
      - 'noc-wizard/**'

jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    steps:
      - uses: actions/checkout@v2
      - run: sudo docker build -f noc-wizard/Dockerfile -t epikedge/noc-wizard .
      - run: sudo docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
        env:
          DOCKER_USERNAME: ${{secrets.DOCKER_USERNAME}}
          DOCKER_PASSWORD: ${{secrets.DOCKER_PASSWORD}}
      - run: sudo docker push epikedge/noc-wizard
      - name: kubectl deployment
        run: |
          export KUBECONFIG=/etc/kubernetes/admin.conf
          sudo kubectl version
          sudo echo --$KUBECONFIG--
          sudo kubectl get pods --namespace=default
          sudo kubectl rollout restart deployment noc-wizard-deployment  --namespace=default
