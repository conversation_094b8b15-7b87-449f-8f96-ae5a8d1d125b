name: tests-auth

on:
  push:
    paths:
      - "auth/**"

jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    steps:
      - name: Fix up git URLs
        run: echo -e '[url "https://github.com/"]\n  insteadOf = "**************:"' >> ~/.gitconfig
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.GH_ACTIONS_ENHANCED_PAT }}
      - uses: oven-sh/setup-bun@v1
        with:  
         bun-version: latest
      - run: cd auth && bun install && bun run test:ci
