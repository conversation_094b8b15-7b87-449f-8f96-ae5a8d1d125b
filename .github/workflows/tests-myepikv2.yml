name: tests-myepikv2

on:
  push:
    paths:
      - "myepikv2/**"
  pull_request:
    paths:
      - "myepikv2/**"

jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    defaults:
      run:
        working-directory: myepikv2

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Lint JS/JSX
        run: npm run lint

      - name: Format check with Prettier
        run: npx prettier --check "src/**/*.{js,jsx,json,css,scss,md}"

      - name: Build project
        run: npm run build
