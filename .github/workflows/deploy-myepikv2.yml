name: deploy-myepikv2

on:
  push:
    branches:
      - main
    paths:
      - 'myepikv2/**'

jobs:
  deploy:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Build Docker image
        run: sudo docker build -f myepikv2/Dockerfile -t epikedge/myepikv2 .

      - name: Login to Docker Hub
        run: sudo docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

      - name: Push Docker image
        run: sudo docker push epikedge/myepikv2

      - name: kubectl deployment
        run: |
          export KUBECONFIG=/etc/kubernetes/admin.conf
          sudo kubectl version
          sudo kubectl get pods --namespace=default
          sudo kubectl rollout restart deployment myepikv2-deployment --namespace=default
