name: deploy-edge-proxy
on:
  push:
    branches:
      - main
    paths:
      - 'edge-proxy/**'
jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    steps:
      - name: Fix up git URLs
        run: sudo echo -e '[url "https://github.com/"]\n  insteadOf = "**************:"' >> ~/.gitconfig
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.GH_ACTIONS_ENHANCED_PAT }}
      - uses: ./.github/actions/setup-go
        with:
          go-version: '1.22.x'
      - uses: ./.github/actions/setup-go-protobuf
      - name: Get version info
        id: version
        run: |
          echo "sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "timestamp=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT

      - run: |
          sudo docker build -f edge-proxy/Dockerfile \
            -t epikedge/edge-proxy:latest \
            -t epikedge/edge-proxy:${{ steps.version.outputs.sha }} \
            -t epikedge/edge-proxy:${{ steps.version.outputs.timestamp }} .

          sudo docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD

          sudo docker push epikedge/edge-proxy:latest
          sudo docker push epikedge/edge-proxy:${{ steps.version.outputs.sha }}
          sudo docker push epikedge/edge-proxy:${{ steps.version.outputs.timestamp }}
        env:
          DOCKER_USERNAME: ${{secrets.DOCKER_USERNAME}}
          DOCKER_PASSWORD: ${{secrets.DOCKER_PASSWORD}}

      - name: kubectl deployment
        run: |
          export KUBECONFIG=/etc/kubernetes/admin.conf

          # Update image tag in deployment
          sudo kubectl set image deployment/edge-proxy-deployment \
            edge-proxy=epikedge/edge-proxy:${{ steps.version.outputs.sha }} \
            --namespace=default

          # Apply other configs
          sudo kubectl apply -f k8s/common/edge-proxy-clusterIP.yaml

          # Wait for rollout to complete
          sudo kubectl rollout status deployment/edge-proxy-deployment \
            --namespace=default \
            --timeout=300s

          # Verify pods are running
          sudo kubectl get pods -l app=edge-proxy --namespace=default
