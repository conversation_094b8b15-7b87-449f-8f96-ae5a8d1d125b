name: deploy-noc-wizard-api

on:
  push:
    branches:
      - main
    paths:
      - 'noc-wizard-api/**'

jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    steps:
      - name: Fix up git URLs
        run: sudo echo -e '[url "https://github.com/"]\n  insteadOf = "**************:"' >> ~/.gitconfig
      - uses: actions/checkout@v2
        with:
            submodules: true
            token: ${{ secrets.GH_ACTIONS_ENHANCED_PAT }}
      - run: sudo docker build -f noc-wizard-api/Dockerfile -t epikedge/noc-wizard-api .
      - run: sudo docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
        env:
          DOCKER_USERNAME: ${{secrets.DOCKER_USERNAME}}
          DOCKER_PASSWORD: ${{secrets.DOCKER_PASSWORD}}
      - run: sudo docker push epikedge/noc-wizard-api
      - name: kubectl deployment
        run: |
          export KUBECONFIG=/etc/kubernetes/admin.conf
          sudo kubectl version
          sudo echo --$KUBECONFIG--
          sudo kubectl get pods --namespace=default
          sudo kubectl rollout restart deployment noc-wizard-api-deployment  --namespace=default
