name: deploy-client

on:
  push:
    branches:
      - main
    paths:
      - 'client/**'

jobs:
  build:
    runs-on: [self-hosted, Linux, X64, LA-192-KubeMaster]
    steps:
      - uses: actions/checkout@v2
      - run: sudo docker build -f client/Dockerfile -t epikedge/clientv2 .
      - run: sudo docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
        env:
          DOCKER_USERNAME: ${{secrets.DOCKER_USERNAME}}
          DOCKER_PASSWORD: ${{secrets.DOCKER_PASSWORD}}
      - run: sudo docker push epikedge/clientv2
      - name: kubectl deployment
        run: |
          export KUBECONFIG=/etc/kubernetes/admin.conf
          sudo kubectl version
          sudo echo --$KUBECONFIG--
          sudo kubectl get pods --namespace=default
          sudo kubectl rollout restart deployment client-deployment  --namespace=default
