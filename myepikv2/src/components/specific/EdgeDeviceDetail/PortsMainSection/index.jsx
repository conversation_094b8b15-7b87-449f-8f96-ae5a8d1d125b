import { useState, lazy, Suspense } from 'react';
import { Col, Row, Segmented } from 'antd';
import {
  DeviceLockIcon,
  OOBMIcon,
  PhoneActivity,
  Flex,
  Button,
} from '@/components';
import PropTypes from 'prop-types';

const portSectionTabsList = [
  { label: 'Ports', value: 'ports' },
  { label: 'EPIs', value: 'epis' },
  { label: 'PRI | CAS', value: 'pri_cas' },
  { label: 'Networking', value: 'networking', disabled: false },
  { label: 'Options', value: 'options' },
  { label: 'Admin Tools', value: 'admin_tools' },
  { label: 'Phones', value: 'phones' },
];

const PortSectionTabs = {
  ports: lazy(() => import('@EdgeDeviceDetail/Section1/Ports')),
  phones: lazy(() => import('@EdgeDeviceDetail/Section1/Phone')),
  epis: lazy(() => import('@EdgeDeviceDetail/Section1/Epis')),
  pri_cas: lazy(() => import('@EdgeDeviceDetail/Section1/PriCas')),
  networking: lazy(() => import('@EdgeDeviceDetail/Section1/Networking')),
  admin_tools: lazy(() => import('@EdgeDeviceDetail/Section1/AdminTools')),
  options: lazy(() => import('@EdgeDeviceDetail/Section1/Options')),
};

const PortsMainSection = ({
  openPortDetailsModal,
  openRealTimePortActivityModal,
  openOOBMModal,
}) => {
  const [selectedTab, setSelectedTab] = useState('ports');
  const SelectedComponent = PortSectionTabs[selectedTab];

  return (
    <Flex
      vertical
      style={{
        flex: 1,
        padding: '16px',
        borderRadius: '12px',
        background: '#fff',
        marginTop: '16px',
      }}
    >
      <Row gutter={[10, 10]} align="middle">
        <Col>
          <Button
            size="middle"
            variant="outlined"
            icon={<DeviceLockIcon heigth={18} width={18} />}
            style={{ heigth: 'fit-content' }}
          />
        </Col>
        <Col>
          <Button
            size="middle"
            variant="outlined"
            icon={<OOBMIcon heigth={18} width={18} />}
            style={{ heigth: 'fit-content' }}
            onClick={openOOBMModal}
          />
        </Col>
        <Col>
          <Segmented
            value={selectedTab}
            onChange={setSelectedTab}
            options={portSectionTabsList}
            size="large"
          />
        </Col>

        <Col>
          <Button
            size="middle"
            variant="outlined"
            icon={<PhoneActivity heigth={18} width={19} />}
            style={{ heigth: 'fit-content' }}
            onClick={openRealTimePortActivityModal}
          />
        </Col>
      </Row>
      <Flex vertical style={{ flex: 1, marginTop: '24px', minHeight: '200px' }}>
        <Suspense fallback={<h2>Loading...</h2>}>
          {SelectedComponent && (
            <SelectedComponent
              tabName={selectedTab}
              openPortDetailsModal={openPortDetailsModal}
            />
          )}
        </Suspense>
      </Flex>
    </Flex>
  );
};

export default PortsMainSection;

PortsMainSection.propTypes = {
  openRealTimePortActivityModal: PropTypes.func,
  openPortDetailsModal: PropTypes.func.isRequired,
  openOOBMModal: PropTypes.func.isRequired,
};
