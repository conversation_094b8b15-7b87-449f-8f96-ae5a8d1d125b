import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';
import PropTypes from 'prop-types';

const OOBMTable = ({ handleOOMBAction }) => {
  const dataSource = [
    {
      key: '1',
      btn: '+19807900757',
      category: 'Router',
      interface: 'USB-1.1.1.0 ( Router )',
      interfaceState: 'Active',
      tty: 'Yes',
      speed: '--',
      dataBits: '--',
      parity: '--',
      flowControl: '--',
    },
    {
      key: '2',
      btn: '+19807900758',
      category: 'Modem',
      interface: 'USB-1.1.2.0 ( Modem )',
      interfaceState: 'Deactive',
      tty: 'No',
      speed: '9600',
      dataBits: '8',
      parity: 'None',
      flowControl: 'None',
    },
    {
      key: '3',
      btn: '+19807900759',
      category: 'Switch',
      interface: 'USB-1.1.3.0 ( Switch )',
      interfaceState: 'Active',
      tty: 'Yes',
      speed: '100Mbps',
      dataBits: '8',
      parity: 'Even',
      flowControl: 'RTS/CTS',
    },
    {
      key: '4',
      btn: '+19807900760',
      category: 'Router',
      interface: 'USB-1.1.4.0 ( Router )',
      interfaceState: 'Deactive',
      tty: 'No',
      speed: '--',
      dataBits: '--',
      parity: '--',
      flowControl: '--',
    },
    {
      key: '5',
      btn: '+19807900761',
      category: 'Access Point',
      interface: 'USB-1.1.5.0 ( AP )',
      interfaceState: 'Active',
      tty: 'Yes',
      speed: '1Gbps',
      dataBits: '8',
      parity: 'Odd',
      flowControl: 'XON/XOFF',
    },
  ];

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns({
    handleOOMBAction,
  });

  return (
    <DataTable
      columns={columns}
      data={dataSource}
      loading={loading}
      rowSelection={true}
      size="small"
      scroll={{ y: '200px', x: 'fit-content' }}
      headerClass="device-table-header-row"
      minHeight={200}
    />
  );
};

export default OOBMTable;

OOBMTable.propTypes = {
  handleOOMBAction: PropTypes.func.isRequired,
};
