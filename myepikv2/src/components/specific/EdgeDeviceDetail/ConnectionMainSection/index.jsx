import { useState, lazy, Suspense } from 'react';
import { Col, Row, Segmented } from 'antd';
import Flex from '@/components/common/Flex';
import { DownloadIcon, Reload } from '@/components/common/SvgIcons';

const connectionTabsList = [
  { label: 'Connection Test', value: 'connection_test' },
  { label: 'Vswitch', value: 'vswitch' },
  { label: 'System Info', value: 'system_info' },
  { label: 'Diagnostics', value: 'diagnostics' },
];

const ConnectionSectionTabs = {
  connection_test: lazy(() => import('@EdgeDeviceDetail/Section2/Connection')),
  vswitch: lazy(() => import('@EdgeDeviceDetail/Section2/Vswitch')),
  system_info: lazy(() => import('@EdgeDeviceDetail/Section2/SystemInfo')),
  diagnostics: lazy(() => import('@EdgeDeviceDetail/Section2/Diagnositics')),
};

const ConnectionMainSection = () => {
  const [selectedTab, setSelectedTab] = useState('connection_test');
  const SelectedComponent = ConnectionSectionTabs[selectedTab];

  return (
    <Flex
      vertical
      style={{
        flex: 1,
        padding: '16px',
        borderRadius: '12px',
        background: '#fff',
        marginTop: '16px',
      }}
    >
      <Row gutter={[10, 10]} align="middle" justify="space-between">
        <Col>
          <Segmented
            value={selectedTab}
            onChange={setSelectedTab}
            options={connectionTabsList}
            size="large"
          />
        </Col>
        {selectedTab === 'system_info' && (
          <Flex gap={8}>
            <DownloadIcon width={24} height={24} />
            <Reload width={24} height={24} />
          </Flex>
        )}
      </Row>
      <Flex vertical style={{ flex: 1, marginTop: '24px', minHeight: '300px' }}>
        <Suspense fallback={<h2>Loading...</h2>}>
          {SelectedComponent && <SelectedComponent tabName={selectedTab} />}
        </Suspense>
      </Flex>
    </Flex>
  );
};

export default ConnectionMainSection;
