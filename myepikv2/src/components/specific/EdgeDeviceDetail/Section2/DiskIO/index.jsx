import { Typography, Row, Col, theme } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';
import { Column } from '@ant-design/plots';
const { useToken } = theme;

const inboundOutboundData = [
  { type: 'Inbound', date: 'Jan', value: 120 },
  { type: 'Outbound', date: 'Jan', value: 80 },
  { type: 'Inbound', date: 'Feb', value: 150 },
  { type: 'Outbound', date: 'Feb', value: 90 },
  { type: 'Inbound', date: 'Mar', value: 130 },
  { type: 'Outbound', date: 'Mar', value: 100 },
  { type: 'Inbound', date: 'Apr', value: 160 },
  { type: 'Outbound', date: 'Apr', value: 95 },
  { type: 'Inbound', date: 'May', value: 140 },
  { type: 'Outbound', date: 'May', value: 110 },
  { type: 'Inbound', date: 'Jun', value: 170 },
  { type: 'Outbound', date: 'Jun', value: 105 },
  { type: 'Inbound', date: 'Jul', value: 180 },
  { type: 'Outbound', date: 'Jul', value: 115 },
  { type: 'Inbound', date: 'Aug', value: 165 },
  { type: 'Outbound', date: 'Aug', value: 120 },
  { type: 'Inbound', date: 'Sep', value: 155 },
  { type: 'Outbound', date: 'Sep', value: 100 },
  { type: 'Inbound', date: 'Oct', value: 145 },
  { type: 'Outbound', date: 'Oct', value: 85 },
  { type: 'Inbound', date: 'Nov', value: 135 },
  { type: 'Outbound', date: 'Nov', value: 90 },
  { type: 'Inbound', date: 'Dec', value: 160 },
  { type: 'Outbound', date: 'Dec', value: 95 },
];

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const DiskIO = () => {
  const {
    token: { barChartColor1, switchSecondaryVariant },
  } = useToken();

  const colors = [barChartColor1, switchSecondaryVariant];

  const inboundOutboundDataConfig = {
    data: inboundOutboundData,
    xField: 'date',
    yField: 'value',
    colorField: 'type',
    group: true,
    legend: false,
    scale: {
      color: {
        range: colors,
      },
    },
    style: { inset: 0 },
  };

  return (
    <div style={{ marginTop: 16 }}>
      <Row gutter={[8, 16]}>
        <Col xs={24} sm={24} lg={24} align="center" justify="center">
          <Flex
            vertical
            style={{
              borderRadius: '8px',
              height: '100%',
            }}
          >
            <Flex
              style={{
                height: 300,
              }}
            >
              <Column {...inboundOutboundDataConfig} />
            </Flex>
            <Flex gap={16} justify="center">
              <Legend label={'Inbound'} color={barChartColor1} />
              <Legend label={'Outbound'} color={switchSecondaryVariant} />
            </Flex>
          </Flex>
        </Col>
      </Row>
    </div>
  );
};

export default DiskIO;
