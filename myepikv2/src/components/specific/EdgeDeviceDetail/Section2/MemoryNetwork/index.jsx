import { Typography, Col, theme } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';
import { Pie } from '@ant-design/plots';
const { useToken } = theme;

const data = [
  { type: 'Active', value: 0.3 },
  { type: 'Available', value: 0.3 },
  { type: 'Buffers', value: 0.2 },
  { type: 'Cached', value: 0.2 },
  { type: 'Free', value: 0.2 },
  { type: 'Inactive', value: 0.2 },
  { type: 'Used', value: 0.2 },
];

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const ChartCard = ({ config }) => (
  <Col xs={24} sm={24} lg={24}>
    <Flex
      style={{
        borderRadius: '8px',
        marginBottom: 16,
        height: '300px',
      }}
    >
      <Pie {...config} />
    </Flex>
  </Col>
);

ChartCard.propTypes = {
  config: PropTypes.object.isRequired,
};

const MemoryNetwork = () => {
  const {
    token: {
      colorPrimary,
      colorPrimary2,
      colorPrimary4,
      colorPrimary5,
      colorPrimary6,
      colorPrimary7,
    },
  } = useToken();

  const colors = [
    colorPrimary,
    colorPrimary2,
    colorPrimary4,
    colorPrimary5,
    colorPrimary6,
    colorPrimary7,
  ];

  const config = {
    data,
    angleField: 'value',
    colorField: 'type',
    scale: { color: { range: colors } },
    legend: false,
    radius: 0.8,
    tooltip: ({ type, value }) => {
      return { type, value };
    },
    interaction: {
      tooltip: {
        render: (e, { items }) => {
          return (
            <>
              {items.map((item) => {
                const { type, value, color } = item;
                return (
                  <div
                    key={type}
                    style={{
                      margin: 0,
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div>
                      <span
                        style={{
                          display: 'inline-block',
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          backgroundColor: color,
                          marginRight: 6,
                        }}
                      ></span>
                      <span>{type}</span>
                    </div>
                    <b>{value}</b>
                  </div>
                );
              })}
            </>
          );
        },
      },
    },
  };
  return (
    <div
      style={{
        marginTop: '20px',
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
      }}
    >
      <Flex gap={16}>
        {config?.data?.map((dt, index) => (
          <Legend key={index} label={dt?.type} color={colors[index]} />
        ))}
      </Flex>
      <ChartCard config={config} />
    </div>
  );
};

export default MemoryNetwork;
