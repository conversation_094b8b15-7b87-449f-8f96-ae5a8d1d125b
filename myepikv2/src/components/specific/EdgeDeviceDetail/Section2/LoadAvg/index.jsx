import { Typography, Row, Col, theme } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';
import { Column } from '@ant-design/plots';
const { useToken } = theme;

const loadAvgData = [
  { type: '1min', date: 'Jan', value: 120 },
  { type: '5min', date: 'Jan', value: 100 },
  { type: '15min', date: 'Jan', value: 80 },
  { type: '1min', date: 'Feb', value: 150 },
  { type: '5min', date: 'Feb', value: 120 },
  { type: '15min', date: 'Feb', value: 90 },
  { type: '1min', date: 'Mar', value: 130 },
  { type: '5min', date: 'Mar', value: 110 },
  { type: '15min', date: 'Mar', value: 100 },
  { type: '1min', date: 'Apr', value: 160 },
  { type: '5min', date: 'Apr', value: 130 },
  { type: '15min', date: 'Apr', value: 95 },
  { type: '1min', date: 'May', value: 140 },
  { type: '5min', date: 'May', value: 115 },
  { type: '15min', date: 'May', value: 110 },
  { type: '1min', date: 'Jun', value: 170 },
  { type: '5min', date: 'Jun', value: 140 },
  { type: '15min', date: 'Jun', value: 105 },
  { type: '1min', date: 'Jul', value: 180 },
  { type: '5min', date: 'Jul', value: 150 },
  { type: '15min', date: 'Jul', value: 115 },
  { type: '1min', date: 'Aug', value: 165 },
  { type: '5min', date: 'Aug', value: 135 },
  { type: '15min', date: 'Aug', value: 120 },
  { type: '1min', date: 'Sep', value: 155 },
  { type: '5min', date: 'Sep', value: 130 },
  { type: '15min', date: 'Sep', value: 100 },
  { type: '1min', date: 'Oct', value: 145 },
  { type: '5min', date: 'Oct', value: 125 },
  { type: '15min', date: 'Oct', value: 85 },
  { type: '1min', date: 'Nov', value: 135 },
  { type: '5min', date: 'Nov', value: 115 },
  { type: '15min', date: 'Nov', value: 90 },
  { type: '1min', date: 'Dec', value: 160 },
  { type: '5min', date: 'Dec', value: 140 },
  { type: '15min', date: 'Dec', value: 95 },
];

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const LoadAvg = () => {
  const {
    token: { barChartColor1, switchSecondaryVariant },
  } = useToken();

  const colors = [barChartColor1, switchSecondaryVariant];

  const loadAvgDataConfig = {
    data: loadAvgData,
    xField: 'date',
    yField: 'value',
    colorField: 'type',
    group: true,
    legend: false,
    scale: {
      color: {
        range: colors,
      },
    },
    style: { inset: 0 },
  };

  return (
    <div style={{ marginTop: 16 }}>
      <Row gutter={[8, 16]}>
        <Col xs={24} sm={24} lg={24} align="center" justify="center">
          <Flex
            vertical
            style={{
              borderRadius: '8px',
              height: '100%',
            }}
          >
            <Flex
              style={{
                height: 300,
              }}
            >
              <Column {...loadAvgDataConfig} />
            </Flex>
            <Flex gap={16} justify="center">
              <Legend label={'1min'} color={barChartColor1} />
              <Legend label={'15min'} color={switchSecondaryVariant} />
              <Legend label={'5min'} color={barChartColor1} />
            </Flex>
          </Flex>
        </Col>
      </Row>
    </div>
  );
};

export default LoadAvg;
