import PropTypes from 'prop-types';

const PhoneActivity = ({
  width = 16,
  height = 17,
  className = '',
  fill = 'black',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M17.4794 7.95394C16.5088 7.95394 15.6862 7.61696 15.0115 6.94301C14.3372 6.26946 14 5.44744 14 4.47697C14 3.50649 14.3372 2.68428 15.0115 2.01032C15.6858 1.33637 16.5084 0.999596 17.4794 1C18.4504 1.0004 19.2728 1.33738 19.9467 2.01093C20.6206 2.68448 20.9578 3.50649 20.9582 4.47697C20.9582 4.89849 20.8835 5.30647 20.7342 5.70089C20.5848 6.09531 20.3885 6.43269 20.1453 6.71303L23.7327 10.2974C23.7894 10.354 23.8198 10.4238 23.8238 10.5067C23.8275 10.5889 23.7971 10.6623 23.7327 10.727C23.668 10.7917 23.5963 10.8241 23.5178 10.8241C23.4393 10.8241 23.3677 10.7917 23.3029 10.727L19.716 7.14204C19.4125 7.40053 19.0634 7.60058 18.6688 7.74216C18.2741 7.88375 17.8775 7.95454 17.4788 7.95454M17.4788 7.34774C18.2842 7.34774 18.9644 7.07064 19.5193 6.51643C20.0738 5.96221 20.3511 5.2824 20.3511 4.47697C20.3511 3.67154 20.074 2.99192 19.5199 2.43812C18.9658 1.88431 18.2859 1.60721 17.48 1.6068C16.6742 1.6068 15.994 1.88391 15.4395 2.43812C14.885 2.99233 14.6075 3.67195 14.6071 4.47697C14.6067 5.28199 14.884 5.96161 15.4389 6.51582C15.9938 7.07003 16.6738 7.34714 17.4788 7.34714"
        fill={fill}
      />
      <path
        d="M17.4794 7.95394C16.5088 7.95394 15.6862 7.61696 15.0115 6.94301C14.3372 6.26946 14 5.44745 14 4.47697C14 3.50649 14.3372 2.68428 15.0115 2.01032C15.6858 1.33637 16.5084 0.999596 17.4794 1C18.4504 1.0004 19.2728 1.33738 19.9467 2.01093C20.6206 2.68448 20.9578 3.50649 20.9582 4.47697C20.9582 4.89849 20.8835 5.30647 20.7342 5.70089C20.5848 6.09531 20.3885 6.43269 20.1453 6.71303L23.7327 10.2974C23.7894 10.354 23.8198 10.4238 23.8238 10.5067C23.8275 10.5889 23.7971 10.6623 23.7327 10.727C23.668 10.7917 23.5963 10.8241 23.5178 10.8241C23.4393 10.8241 23.3677 10.7917 23.3029 10.727L19.716 7.14204C19.4125 7.40053 19.0634 7.60058 18.6688 7.74216C18.2741 7.88375 17.8775 7.95454 17.4788 7.95454M17.4788 7.34774C18.2842 7.34774 18.9644 7.07064 19.5193 6.51643C20.0738 5.96221 20.3511 5.2824 20.3511 4.47697C20.3511 3.67154 20.074 2.99192 19.5199 2.43812C18.9658 1.88431 18.2859 1.60721 17.48 1.6068C16.6742 1.6068 15.994 1.88391 15.4395 2.43812C14.885 2.99233 14.6075 3.67195 14.6071 4.47697C14.6067 5.28199 14.884 5.96161 15.4389 6.51582C15.9938 7.07003 16.6738 7.34714 17.4788 7.34714"
        stroke={fill}
        strokeWidth="0.75"
      />
      <path
        d="M16.4473 17.3792L17.006 16.7898L15.67 15.5212L15.1112 16.1119L16.4473 17.3792ZM18.8468 16.5417L21.1923 17.8938L22.1121 16.2961L19.7654 14.9453L18.8468 16.5417ZM21.6455 20.6924L19.9017 22.5283L21.2365 23.798L22.9803 21.9622L21.6455 20.6924ZM18.8517 23.1177C17.0895 23.2909 12.4906 23.1435 7.49998 17.8901L6.16513 19.1574C11.604 24.8836 16.7924 25.1734 19.0323 24.9512L18.8517 23.1177ZM7.49998 17.8901C2.7402 12.8786 1.94199 8.65051 1.84252 6.79621L0.00296047 6.89567C0.125762 9.1675 1.08852 13.8131 6.16513 19.1574L7.49998 17.8901ZM9.18973 9.73853L9.54094 9.36767L8.20609 8.09913L7.85365 8.46999L9.18973 9.73853ZM9.81847 4.75034L8.26995 2.56202L6.76563 3.62548L8.31293 5.81502L9.81847 4.75034ZM3.02756 2.088L1.10203 4.11914L2.43811 5.38767L4.36609 3.35777L3.02756 2.088ZM8.52169 9.10364C8.30068 8.89064 8.07758 8.67982 7.85242 8.47121L7.84996 8.47367L7.84628 8.47736L7.78488 8.54858C7.66463 8.708 7.56939 8.88482 7.50244 9.07294C7.38209 9.41065 7.31824 9.85764 7.39928 10.4152C7.55893 11.5105 8.27731 12.9829 10.1525 14.9588L11.4886 13.689C9.73496 11.8433 9.3027 10.7074 9.22165 10.1475C9.18236 9.87729 9.22166 9.73607 9.23762 9.69063L9.24744 9.6673L9.21306 9.71274L9.19096 9.7373C9.19096 9.7373 9.1885 9.7373 8.52169 9.10364ZM10.1525 14.9588C12.0228 16.9285 13.435 17.701 14.5119 17.8766C15.0658 17.9662 15.514 17.8938 15.8529 17.7599C16.0409 17.6866 16.2156 17.5829 16.3699 17.4529L16.4141 17.4112L16.4313 17.394L16.4399 17.3866L16.4436 17.3829L16.4448 17.3805C16.4448 17.3805 16.4473 17.3792 15.7792 16.7443C15.1112 16.1107 15.1124 16.1095 15.1124 16.1082L15.1149 16.107L15.1173 16.1033L15.1235 16.0972L15.1358 16.0849L15.1812 16.0456C15.1935 16.0391 15.1918 16.0395 15.1763 16.0468C15.1517 16.0567 15.0412 16.0959 14.8079 16.0579C14.3142 15.9768 13.2483 15.5421 11.4886 13.689L10.1525 14.9588ZM8.26995 2.56202C7.02474 0.801045 4.53433 0.502638 3.02878 2.08923L4.36241 3.35777C5.00466 2.68236 6.13689 2.73517 6.76563 3.62548L8.26995 2.56202ZM1.84252 6.79621C1.81551 6.305 2.03041 5.81502 2.43811 5.38767L1.10203 4.11914C0.443817 4.81174 -0.0572122 5.77327 0.00296047 6.89567L1.84252 6.79621ZM19.9005 22.5295C19.5578 22.8905 19.2005 23.0833 18.8517 23.1177L19.0323 24.9512C19.9496 24.8615 20.6925 24.3715 21.2365 23.798L19.9005 22.5295ZM9.54094 9.36889C10.7297 8.11755 10.8132 6.16009 9.81847 4.75156L8.31415 5.81502C8.83238 6.54815 8.75255 7.52442 8.20609 8.09913L9.54094 9.36889ZM21.1923 17.8938C22.2005 18.4746 22.3982 19.9016 21.6455 20.6936L22.9803 21.9622C24.5829 20.2749 24.1162 17.4504 22.1121 16.2961L21.1923 17.8938ZM17.006 16.791C17.4788 16.2924 18.2169 16.1807 18.8468 16.5429L19.7654 14.9465C18.4158 14.1704 16.7445 14.3939 15.67 15.5237L17.006 16.791Z"
        fill={fill}
      />
    </svg>
  );
};
export default PhoneActivity;

PhoneActivity.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  fill: PropTypes.string,
};
