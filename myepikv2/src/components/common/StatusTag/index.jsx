import PropTypes from 'prop-types';
import Tooltip from '../Tooltip';
import Tag from '../Tag';

const themedStatuses = {
  green: [
    'Incomplete',
    'enabled',
    'Active',
    'Delivered',
    'Assigned',
    'Approved',
  ],
  blue: ['Pending Cancel'],
  red: ['Canceled', 'deleted', 'Closed', 'Failed', 'Unassigned', 'Rejected'],
  yellow: ['Pending', 'disabled'],
  warning: ['Deactive'],
};

const themeTokens = {
  green: {
    color: 'var(--primary-green)',
    backgroundColor: 'var(--secondary-green)',
  },
  blue: {
    color: 'var(--primary-blue)',
    backgroundColor: 'var(--surface-light-blue)',
  },
  red: {
    color: 'var(--primary-red)',
    backgroundColor: 'var(--secondary-red)',
  },
  yellow: {
    color: 'var(--primary-yellow)',
    backgroundColor: 'var(--secondary-yellow)',
  },
  warning: {
    color: 'var(--primary-warning)',
    backgroundColor: 'var(--secondary-warning)',
  },
};

const statusColors = Object.entries(themedStatuses).reduce(
  (acc, [theme, statuses]) => {
    statuses.forEach((status) => {
      acc[status] = themeTokens[theme];
    });
    return acc;
  },
  {},
);

const StatusTag = ({ text, toolTip = 'Last Updated on:24-Aug-2024' }) => {
  const colorStyles = statusColors[text] || {};

  return (
    <span style={{ width: 'fit-content', display: 'inline-block' }}>
      <Tooltip title={toolTip}>
        <Tag
          bordered={false}
          text={text}
          className="small-text"
          style={{
            color: colorStyles.color,
            backgroundColor: colorStyles.backgroundColor,
            borderRadius: '16px',
          }}
        />
      </Tooltip>
    </span>
  );
};

StatusTag.propTypes = {
  text: PropTypes.string.isRequired,
  toolTip: PropTypes.string,
};

export default StatusTag;
