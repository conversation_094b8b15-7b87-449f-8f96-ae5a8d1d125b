package grpc

import (
	"context"
	"fmt"
	"net"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

// Wraps the gRPC server with our service implementation.
type Server struct {
	pb.UnimplementedEdgeDeviceProxyServer
	proxyServer *proxy.Server
	grpcServer  *grpc.Server
	listener    net.Listener
}

// Config contains server configuration.
type Config struct {
	Port          int
	BoxRepository boxes.BoxFinder // Changed to use BoxFinder interface
	ProxyServer   *proxy.Server
}

// Creates a new gRPC server instance.
func New(cfg Config) (*Server, error) {
	// Create listener.
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to listen: %w", err)
	}

	// Create gRPC server.
	grpcServer := grpc.NewServer()

	// Create server instance.
	s := &Server{
		proxyServer: cfg.ProxyServer,
		grpcServer:  grpcServer,
		listener:    lis,
	}

	// Register our service.
	pb.RegisterEdgeDeviceProxyServer(grpcServer, s)

	// Enable reflection (add just this one line)
	reflection.Register(grpcServer)

	return s, nil
}

// Starts the gRPC server.
func (s *Server) Run() error {
	return s.grpcServer.Serve(s.listener)
}

// Gracefully stops the gRPC server.
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.grpcServer.GracefulStop()
	}
}

// Implements the gRPC service method.
func (s *Server) HandleRequest(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceResponse, error) {
	// Use proxy server to handle the actual work
	statusCode, body, headers, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, req.Path)
	if err != nil {
		return nil, err // Error is already in the correct gRPC format
	}

	return &pb.DeviceResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    headers,
	}, nil
}
